/* 任务列表页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 20rpx 60rpx 20rpx 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f9fafb;
  box-sizing: border-box;
}

.search-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  font-size: 28rpx;
  color: #6b7280;
}

.filter-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  border-radius: 12rpx;
}

.filter-icon {
  font-size: 32rpx;
  color: white;
}

/* 筛选面板 */
.filter-panel {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 30rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #6b7280;
  transition: all 0.2s;
}

.filter-option.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* 任务统计 */
.task-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  background: white;
  border-radius: 12rpx;
  padding: 25rpx 15rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}

/* 任务列表 */
.task-list {
  margin-bottom: 120rpx;
}

.task-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.task-card:active {
  transform: scale(0.98);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
  margin-right: 20rpx;
}

.task-priority {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.priority-low {
  background-color: #f3f4f6;
  color: #6b7280;
}

.priority-medium {
  background-color: #fef3c7;
  color: #d97706;
}

.priority-high {
  background-color: #fed7d7;
  color: #e53e3e;
}

.priority-urgent {
  background-color: #fecaca;
  color: #dc2626;
}

.task-desc {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.task-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.task-project {
  font-size: 24rpx;
  color: #6b7280;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-assignees {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.assignee-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 2rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.assignee-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-assignee {
  font-size: 24rpx;
  color: #9ca3af;
}

.task-date {
  text-align: right;
}

.due-date {
  font-size: 24rpx;
  color: #6b7280;
}

.due-date.overdue {
  color: #ef4444;
  font-weight: 600;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: #3b82f6;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
  z-index: 100;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}

/* 加载和空状态 */
.loading, .loading-more {
  text-align: center;
  padding: 60rpx;
  color: #6b7280;
  font-size: 28rpx;
}

.empty {
  text-align: center;
  padding: 120rpx 60rpx;
  color: #9ca3af;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #d1d5db;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .task-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-item {
    padding: 20rpx 10rpx;
  }
  
  .stat-number {
    font-size: 32rpx;
  }
  
  .task-card {
    padding: 25rpx;
  }
  
  .task-title {
    font-size: 30rpx;
  }
}
