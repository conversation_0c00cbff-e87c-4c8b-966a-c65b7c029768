import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import PrivateChat from '@/components/PrivateChat';
import Avatar from '@/components/Avatar';

interface Chat {
  id: string;
  type: string;
  name?: string;
  participants: Array<{
    id: string;
    name: string;
    email: string;
    avatar?: string;
  }>;
  otherParticipants: Array<{
    id: string;
    name: string;
    email: string;
    avatar?: string;
  }>;
  lastMessage?: {
    id: string;
    content: string;
    createdAt: string;
    senderId?: string;
    isSystem?: boolean;
    type?: string;
    sender: {
      id: string;
      name: string;
    };
  };
  messageCount: number;
  unreadCount?: number;
  updatedAt: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

export default function Chats() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [chats, setChats] = useState<Chat[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [selectedRecipient, setSelectedRecipient] = useState<string | null>(null);
  const [showNewChatModal, setShowNewChatModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取聊天列表和用户列表
  useEffect(() => {
    if (status === 'authenticated') {
      loadChats();
      loadUsers();
    }
  }, [status]);

  const loadChats = async () => {
    try {
      const response = await fetch('/api/chats');
      if (response.ok) {
        const data = await response.json();
        setChats(data);
      }
    } catch (error) {
      console.error('加载聊天列表失败:', error);
    }
  };

  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        // 过滤掉当前用户
        const otherUsers = data.filter((user: User) => user.id !== session?.user?.id);
        setUsers(otherUsers);
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取聊天最后查看时间
  const getLastViewTime = (chatId: string): Date => {
    const key = `chat_last_view_${chatId}_${session?.user?.id}`;
    const stored = localStorage.getItem(key);
    return stored ? new Date(stored) : new Date(0);
  };

  // 检查聊天是否有未读消息
  const hasUnreadMessages = (chat: Chat): boolean => {
    // 优先使用API返回的unreadCount
    if (chat.unreadCount && chat.unreadCount > 0) {
      return true;
    }

    // 回退到基于时间的检查（兼容性）
    if (!chat.lastMessage) return false;

    const lastMessageTime = new Date(chat.lastMessage.createdAt);
    const lastViewTime = getLastViewTime(chat.id);

    // 如果最后一条消息的时间晚于最后查看时间，且不是自己发送的，则算作未读
    return lastMessageTime > lastViewTime && chat.lastMessage.senderId !== session?.user?.id;
  };



  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}小时前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  // 开始新聊天
  const startNewChat = (userId: string) => {
    setSelectedRecipient(userId);
    setSelectedChat(null);
    setShowNewChatModal(false);
  };

  // 关闭聊天
  const closeChat = () => {
    setSelectedChat(null);
    setSelectedRecipient(null);
    loadChats(); // 重新加载聊天列表
  };

  // 聊天更新回调
  const handleChatUpdate = () => {
    loadChats(); // 重新加载聊天列表
  };

  // 删除聊天
  const deleteChat = async (chatId: string) => {
    try {
      const response = await fetch('/api/chats', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ chatId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '删除聊天失败');
      }

      // 刷新聊天列表
      loadChats();

      // 如果删除的是当前选中的聊天，关闭聊天窗口
      if (selectedChat === chatId) {
        closeChat();
      }

      setShowDeleteConfirm(null);
    } catch (error) {
      console.error('删除聊天失败:', error);
      alert(error instanceof Error ? error.message : '删除聊天失败，请稍后再试');
    }
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* 聊天列表 */}
        <div className="lg:w-1/3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md">
            {/* 头部 */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  团队沟通
                </h2>
                <button
                  onClick={() => setShowNewChatModal(true)}
                  className="btn btn-primary btn-sm"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  新对话
                </button>
              </div>
            </div>

            {/* 聊天列表 */}
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {chats.length === 0 ? (
                <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                  还没有对话记录
                  <br />
                  点击"新对话"开始沟通
                </div>
              ) : (
                chats.map((chat) => {
                  const otherParticipant = chat.otherParticipants?.[0];
                  const isSystemChat = chat.type === 'SYSTEM';

                  return (
                    <div
                      key={chat.id}
                      className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 ${
                        selectedChat === chat.id ? 'bg-primary-50 dark:bg-primary-900/20' : ''
                      }`}
                    >
                      <div className="flex items-center">
                        {isSystemChat ? (
                          <div className="w-10 h-10 rounded-full mr-3 bg-blue-500 flex items-center justify-center">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                        ) : otherParticipant ? (
                          <div className="mr-3">
                            <Avatar
                              user={{
                                id: otherParticipant.id,
                                name: otherParticipant.name,
                                avatar: otherParticipant.avatar
                              }}
                              size="md"
                              showTooltip={true}
                            />
                          </div>
                        ) : (
                          <div className="w-10 h-10 rounded-full mr-3 bg-gray-400 flex items-center justify-center">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                        )}
                        <div
                          className="flex-1 min-w-0 cursor-pointer"
                          onClick={async () => {
                            // 调用API标记聊天为已读
                            try {
                              await fetch(`/api/chats/${chat.id}/read`, {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json',
                                },
                              });
                            } catch (error) {
                              console.error('标记消息已读失败:', error);
                            }

                            // 同时使用localStorage作为备用方案
                            const key = `chat_last_view_${chat.id}_${session?.user?.id}`;
                            localStorage.setItem(key, new Date().toISOString());

                            setSelectedChat(chat.id);
                            setSelectedRecipient(null);

                            // 触发全局事件，通知MessageCenter刷新未读数
                            window.dispatchEvent(new CustomEvent('chatMarkedAsRead'));
                          }}
                        >
                          <div className="flex items-center justify-between">
                            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                              {isSystemChat ? '系统通知' : (otherParticipant?.name || '未知用户')}
                            </h3>
                            <div className="flex items-center space-x-2">
                              {chat.lastMessage && (
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {formatTime(chat.lastMessage.createdAt)}
                                </span>
                              )}
                              {hasUnreadMessages(chat) && (
                                <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                                  {chat.unreadCount || 1}
                                </span>
                              )}
                            </div>
                          </div>
                          {chat.lastMessage ? (
                            <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                              {chat.lastMessage.isSystem || chat.lastMessage.type === 'SYSTEM' ? (
                                <span className="text-blue-600 dark:text-blue-400">系统通知</span>
                              ) : (
                                <>
                                  {chat.lastMessage.sender?.id === session?.user?.id ? '我: ' : ''}
                                  {chat.lastMessage.content}
                                </>
                              )}
                            </p>
                          ) : (
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {isSystemChat ? '暂无系统通知' : '还没有消息'}
                            </p>
                          )}
                        </div>
                        {!isSystemChat && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowDeleteConfirm(chat.id);
                            }}
                            className="ml-2 p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                            title="删除对话"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>
        </div>

        {/* 聊天窗口 */}
        <div className="lg:w-2/3">
          {selectedChat || selectedRecipient ? (
            <PrivateChat
              chatId={selectedChat || undefined}
              recipientId={selectedRecipient || undefined}
              onClose={closeChat}
              onChatUpdate={handleChatUpdate}
            />
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md h-96 flex items-center justify-center">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <svg className="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p className="text-lg font-medium">选择一个对话开始沟通</p>
                <p className="text-sm">或者点击"新对话"开始新的沟通</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 新聊天模态框 */}
      {showNewChatModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  选择沟通对象
                </h3>
                <button
                  onClick={() => setShowNewChatModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="max-h-96 overflow-y-auto">
              {users.map((user) => (
                <div
                  key={user.id}
                  onClick={() => startNewChat(user.id)}
                  className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                >
                  <div className="flex items-center">
                    <div className="mr-3">
                      <Avatar
                        user={{
                          id: user.id,
                          name: user.name,
                          avatar: user.avatar
                        }}
                        size="md"
                        showTooltip={false}
                      />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {user.name}
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {user.email}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-sm w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full">
                <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 text-center mb-2">
                确认删除对话
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-6">
                删除后将无法恢复此对话的所有消息记录。
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(null)}
                  className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  取消
                </button>
                <button
                  onClick={() => deleteChat(showDeleteConfirm)}
                  className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
