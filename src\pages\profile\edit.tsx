import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import AvatarUpload from '../../components/AvatarUpload';

type FormData = {
  name: string;
  email: string;
  avatar: string;
  age: number;
  bio: string;
  phone: string;
  department: string;
  position: string;
};

export default function EditProfile() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [currentUser, setCurrentUser] = useState<any>(null);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<FormData>();

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取用户当前信息
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.id) {
      fetchUserProfile();
    }
  }, [status, session]);

  const fetchUserProfile = async () => {
    try {
      const response = await fetch(`/api/users/${session?.user?.id}`);

      if (!response.ok) {
        throw new Error('获取用户信息失败');
      }

      const user = await response.json();
      setCurrentUser(user);

      // 填充表单
      setValue('name', user.name || '');
      setValue('email', user.email || '');
      setValue('avatar', user.avatar || '');
      setValue('age', user.age || '');
      setValue('bio', user.bio || '');
      setValue('phone', user.phone || '');
      setValue('department', user.department || '');
      setValue('position', user.position || '');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      setError('获取用户信息失败');
    }
  };

  const handleAvatarChange = (avatarUrl: string) => {
    setValue('avatar', avatarUrl);
    setCurrentUser((prev: any) => ({ ...prev, avatar: avatarUrl }));
  };

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch(`/api/users/${session?.user?.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '更新用户信息失败');
      }

      setSuccess('用户信息更新成功！');

      // 3秒后跳转到个人资料页
      setTimeout(() => {
        router.push('/profile');
      }, 2000);
    } catch (error) {
      console.error('更新用户信息失败:', error);
      setError(error instanceof Error ? error.message : '更新用户信息失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          编辑个人资料
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          更新您的个人信息和资料
        </p>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{success}</span>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit(onSubmit)}>
          {/* 头像上传区域 */}
          <div className="mb-8 flex justify-center">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                个人头像
              </h3>
              {currentUser && (
                <AvatarUpload
                  currentAvatar={currentUser.avatar}
                  userName={currentUser.name}
                  onAvatarChange={handleAvatarChange}
                  size="lg"
                  editable={true}
                />
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="form-label" htmlFor="name">
                姓名 <span className="text-red-500">*</span>
              </label>
              <input
                id="name"
                type="text"
                className="form-input"
                placeholder="输入您的姓名"
                {...register('name', {
                  required: '请输入姓名',
                  minLength: {
                    value: 2,
                    message: '姓名至少需要2个字符',
                  },
                })}
              />
              {errors.name && (
                <p className="form-error">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="form-label" htmlFor="email">
                邮箱 <span className="text-red-500">*</span>
              </label>
              <input
                id="email"
                type="email"
                className="form-input"
                placeholder="输入您的邮箱"
                {...register('email', {
                  required: '请输入邮箱',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: '请输入有效的邮箱地址',
                  },
                })}
              />
              {errors.email && (
                <p className="form-error">{errors.email.message}</p>
              )}
            </div>



            <div>
              <label className="form-label" htmlFor="age">
                年龄
              </label>
              <input
                id="age"
                type="number"
                min="1"
                max="120"
                className="form-input"
                placeholder="输入您的年龄"
                {...register('age', {
                  min: {
                    value: 1,
                    message: '年龄必须大于0',
                  },
                  max: {
                    value: 120,
                    message: '年龄不能超过120',
                  },
                })}
              />
              {errors.age && (
                <p className="form-error">{errors.age.message}</p>
              )}
            </div>

            <div>
              <label className="form-label" htmlFor="phone">
                电话号码
              </label>
              <input
                id="phone"
                type="tel"
                className="form-input"
                placeholder="输入您的电话号码"
                {...register('phone')}
              />
            </div>

            <div>
              <label className="form-label" htmlFor="department">
                部门
              </label>
              <input
                id="department"
                type="text"
                className="form-input"
                placeholder="输入您的部门"
                {...register('department')}
              />
            </div>

            <div>
              <label className="form-label" htmlFor="position">
                职位
              </label>
              <input
                id="position"
                type="text"
                className="form-input"
                placeholder="输入您的职位"
                {...register('position')}
              />
            </div>
          </div>

          <div className="mt-6">
            <label className="form-label" htmlFor="bio">
              个人简介
            </label>
            <textarea
              id="bio"
              className="form-input"
              rows={4}
              placeholder="介绍一下您自己"
              {...register('bio')}
            ></textarea>
          </div>

          <div className="flex justify-end space-x-4 mt-6">
            <Link
              href="/profile"
              className="btn btn-secondary"
            >
              取消
            </Link>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  保存中...
                </>
              ) : '保存更改'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
