import { useState, useRef, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import Avatar from './Avatar';

interface MessageItem {
  id: string;
  type: 'notification' | 'chat';
  title: string;
  content: string;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    avatar?: string;
  };
  chatId?: string;
  notificationId?: string;
  relatedId?: string;
  notificationType?: string;
}

export default function MessageCenter() {
  const { data: session } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<MessageItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalUnread, setTotalUnread] = useState(0);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 获取聊天最后查看时间
  const getLastViewTime = (chatId: string): Date => {
    const key = `chat_last_view_${chatId}_${session?.user?.id}`;
    const stored = localStorage.getItem(key);
    return stored ? new Date(stored) : new Date(0); // 如果没有记录，返回很早的时间
  };

  // 设置聊天最后查看时间
  const setLastViewTime = (chatId: string) => {
    const key = `chat_last_view_${chatId}_${session?.user?.id}`;
    localStorage.setItem(key, new Date().toISOString());
  };

  // 加载未读数 - 使用统一的计数API
  const loadUnreadCount = async () => {
    try {
      const response = await fetch('/api/notifications/counts');
      if (response.ok) {
        const data = await response.json();
        setTotalUnread(data.total || 0);
      } else {
        // 如果API失败，回退到基于聊天列表的计算
        const chatsResponse = await fetch('/api/chats');
        if (chatsResponse.ok) {
          const chats = await chatsResponse.json();

          // 使用unreadCount字段计算总未读数
          const totalUnread = chats.reduce((sum: number, chat: any) => {
            return sum + (chat.unreadCount || 0);
          }, 0);

          setTotalUnread(totalUnread);
        }
      }
    } catch (error) {
      console.error('加载未读数失败:', error);
      setTotalUnread(0);
    }
  };

  // 初始化和定期刷新
  useEffect(() => {
    if (!session?.user?.id) return;

    // 初始加载未读数和消息
    loadUnreadCount();
    loadMessages();

    // 每30秒刷新一次未读数
    const interval = setInterval(loadUnreadCount, 30000);

    // 监听聊天标记为已读的事件
    const handleChatMarkedAsRead = () => {
      loadUnreadCount();
    };

    window.addEventListener('chatMarkedAsRead', handleChatMarkedAsRead);

    return () => {
      clearInterval(interval);
      window.removeEventListener('chatMarkedAsRead', handleChatMarkedAsRead);
    };
  }, [session?.user?.id]);

  // 加载所有消息（通知+聊天）
  const loadMessages = async () => {
    if (!session?.user?.id) return;

    setLoading(true);
    try {
      // 加载聊天和系统通知
      const chatsRes = await fetch('/api/chats');
      const notificationsRes = await fetch('/api/notifications');
      const allMessages: MessageItem[] = [];

      // 处理聊天
      if (chatsRes.ok) {
        const chats = await chatsRes.json();

        chats.forEach((chat: any) => {
          if (chat.lastMessage) {
            // 处理系统聊天
            if (chat.type === 'SYSTEM') {
              allMessages.push({
                id: `chat-${chat.id}`,
                type: 'chat',
                title: '系统通知',
                content: chat.lastMessage.content,
                read: true,
                createdAt: chat.lastMessage.createdAt,
                sender: {
                  id: 'system',
                  name: '系统',
                  avatar: '/icons/system.png'
                },
                chatId: chat.id
              });
            } else {
              // 处理普通聊天
              const otherParticipant = chat.otherParticipants?.[0];
              if (otherParticipant) {
                allMessages.push({
                  id: `chat-${chat.id}`,
                  type: 'chat',
                  title: otherParticipant.name,
                  content: chat.lastMessage.content,
                  read: true, // 聊天消息的已读状态需要单独处理
                  createdAt: chat.lastMessage.createdAt,
                  sender: {
                    id: otherParticipant.id,
                    name: otherParticipant.name,
                    avatar: otherParticipant.avatar || '/avatars/default-user.png'
                  },
                  chatId: chat.id
                });
              }
            }
          }
        });
      }

      // 处理系统通知
      if (notificationsRes.ok) {
        const notificationsData = await notificationsRes.json();
        const notifications = notificationsData.notifications || [];

        notifications.forEach((notification: any) => {
          allMessages.push({
            id: `notification-${notification.id}`,
            type: 'notification',
            title: notification.title || '系统通知',
            content: notification.message,
            read: notification.read,
            createdAt: notification.createdAt,
            sender: {
              id: 'system',
              name: '系统',
              avatar: '/icons/system.png'
            },
            notificationId: notification.id
          });
        });
      }

      // 按时间排序（最新的在前）
      allMessages.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      setMessages(allMessages);

      // 同时加载未读数
      await loadUnreadCount();
    } catch (error) {
      console.error('加载消息失败:', error);
    } finally {
      setLoading(false);
    }
  };



  // 打开下拉菜单
  const handleToggle = () => {
    setIsOpen(!isOpen);
    // 如果是打开菜单且消息为空，则加载消息
    if (!isOpen && messages.length === 0) {
      loadMessages();
    }
  };

  // 处理消息点击
  const handleMessageClick = async (message: MessageItem) => {
    if (message.type === 'chat' && message.chatId) {
      // 调用API标记聊天为已读
      try {
        await fetch(`/api/chats/${message.chatId}/read`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
      } catch (error) {
        console.error('标记聊天已读失败:', error);
      }

      // 同时设置localStorage作为备用方案
      setLastViewTime(message.chatId);

      // 立即刷新未读数和消息列表
      await loadUnreadCount();
      await loadMessages();

      // 触发全局事件，通知其他组件刷新
      window.dispatchEvent(new CustomEvent('chatMarkedAsRead'));

      // 跳转到聊天页面
      window.location.href = `/chats?chat=${message.chatId}`;
    } else if (message.type === 'notification' && message.notificationId) {
      // 标记通知为已读
      if (!message.read) {
        try {
          await fetch('/api/notifications/mark-read', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ notificationId: message.notificationId }),
          });

          // 刷新消息列表和未读数
          await loadMessages();
          await loadUnreadCount();
        } catch (error) {
          console.error('标记通知已读失败:', error);
        }
      }
    }
    setIsOpen(false);
  };



  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return date.toLocaleDateString('zh-CN');
  };



  return (
    <div className="relative" ref={dropdownRef}>
      {/* 消息中心按钮 */}
      <button
        onClick={handleToggle}
        className="relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-md transition-colors"
        title="消息与通知中心"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>
        {totalUnread > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
            {totalUnread > 99 ? '99+' : totalUnread}
          </span>
        )}
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-xl shadow-xl ring-1 ring-black ring-opacity-5 z-50 border border-gray-200 dark:border-gray-700">
          {/* 头部 */}
          <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">消息与通知</h3>
          </div>

          {/* 消息列表 */}
          <div className="max-h-96 overflow-y-auto">
            {messages.length === 0 ? (
              <div className="p-8 text-center">
                {loading ? (
                  <div className="flex items-center justify-center">
                    <svg className="animate-spin h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="ml-2 text-gray-500 dark:text-gray-400">加载中...</span>
                  </div>
                ) : (
                  <div className="text-gray-500 dark:text-gray-400">
                    <svg className="mx-auto h-12 w-12 mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-2.697-.413l-3.178 1.589a1 1 0 01-1.414-1.414l1.589-3.178A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                    </svg>
                    <p>暂无消息</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="divide-y divide-gray-100 dark:divide-gray-700">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    onClick={() => handleMessageClick(message)}
                    className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors ${
                      !message.read ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      {/* 头像 */}
                      <div className="flex-shrink-0">
                        {message.sender?.id === 'system' ? (
                          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                        ) : message.sender ? (
                          <Avatar
                            user={{
                              id: message.sender.id,
                              name: message.sender.name,
                              avatar: message.sender.avatar
                            }}
                            size="md"
                            showTooltip={true}
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                            <span className="text-gray-600 text-sm">?</span>
                          </div>
                        )}
                      </div>

                      {/* 消息内容 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                            {message.title}
                          </h4>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {formatTime(message.createdAt)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                          {message.content}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 底部操作 */}
          <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750 rounded-b-xl">
            <Link
              href="/chats"
              className="block w-full text-center py-2 px-3 text-sm text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-colors"
              onClick={() => setIsOpen(false)}
            >
              查看所有对话
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
