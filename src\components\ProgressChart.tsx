import { Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// 注册 Chart.js 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface TaskStats {
  todo: number;
  inProgress: number;
  review: number;
  completed: number;
}

interface MemberStats {
  name: string;
  completedTasks: number;
  totalTasks: number;
}

interface ProgressChartProps {
  taskStats: TaskStats;
  memberStats: MemberStats[];
  chartType?: 'bar' | 'doughnut';
}

export default function ProgressChart({ 
  taskStats, 
  memberStats, 
  chartType = 'bar' 
}: ProgressChartProps) {
  // 任务状态数据
  const taskStatusData = {
    labels: ['待处理', '进行中', '审核中', '已完成'],
    datasets: [
      {
        label: '任务数量',
        data: [taskStats.todo, taskStats.inProgress, taskStats.review, taskStats.completed],
        backgroundColor: [
          'rgba(156, 163, 175, 0.7)', // 灰色 - 待处理
          'rgba(59, 130, 246, 0.7)',  // 蓝色 - 进行中
          'rgba(245, 158, 11, 0.7)',  // 黄色 - 审核中
          'rgba(16, 185, 129, 0.7)',  // 绿色 - 已完成
        ],
        borderColor: [
          'rgba(156, 163, 175, 1)',
          'rgba(59, 130, 246, 1)',
          'rgba(245, 158, 11, 1)',
          'rgba(16, 185, 129, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // 成员任务完成情况数据
  const memberTaskData = {
    labels: memberStats.map(member => member.name),
    datasets: [
      {
        label: '已完成任务',
        data: memberStats.map(member => member.completedTasks),
        backgroundColor: 'rgba(16, 185, 129, 0.7)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
      },
      {
        label: '未完成任务',
        data: memberStats.map(member => member.totalTasks - member.completedTasks),
        backgroundColor: 'rgba(156, 163, 175, 0.7)',
        borderColor: 'rgba(156, 163, 175, 1)',
        borderWidth: 1,
      },
    ],
  };

  // 图表选项
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
        beginAtZero: true,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
      <div className="mb-4">
        <h3 className="text-lg font-semibold">任务状态分布</h3>
        <div className="h-64">
          {chartType === 'bar' ? (
            <Bar data={taskStatusData} options={barOptions} />
          ) : (
            <Doughnut data={taskStatusData} options={doughnutOptions} />
          )}
        </div>
      </div>
      
      {memberStats.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4">成员任务完成情况</h3>
          <div className="h-64">
            <Bar data={memberTaskData} options={barOptions} />
          </div>
        </div>
      )}
    </div>
  );
}
