// 小程序主逻辑文件
const { getCurrentConfig } = require('./config/env.js');
const { setUser, clearUser } = require('./utils/store.js');
const { startNetworkMonitoring } = require('./utils/network.js');

App({
  globalData: {
    userInfo: null,
    token: null,
    version: '1.0.0',

    // 从环境配置中获取设置
    ...getCurrentConfig()
  },

  onLaunch() {
    console.log('LabSync小程序启动');
    console.log('当前环境配置:', getCurrentConfig());

    // 启动网络监控
    startNetworkMonitoring();

    // 检查登录状态
    this.checkLoginStatus();

    // 获取系统信息
    this.getSystemInfo();
  },

  onShow() {
    console.log('LabSync小程序显示');
  },

  onHide() {
    console.log('LabSync小程序隐藏');
  },

  onError(msg) {
    console.error('小程序错误:', msg);
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');

    if (token && userInfo) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
    } else if (this.globalData.isDevelopment && this.globalData.useMockData) {
      // 开发模式下使用模拟登录
      this.setMockLogin();
    }
  },

  // 设置模拟登录（仅开发模式）
  setMockLogin() {
    const mockUserInfo = {
      id: 'mock-user-1',
      name: '测试用户',
      email: '<EMAIL>',
      avatar: '',
      role: 'MEMBER',
      status: 'APPROVED'
    };
    const mockToken = 'mock-token-for-development';

    this.globalData.userInfo = mockUserInfo;
    this.globalData.token = mockToken;

    // 不持久化模拟数据，避免影响真实登录
    console.log('开发模式：使用模拟登录状态');
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息:', res);
      }
    });
  },

  // 设置用户信息
  setUserInfo(userInfo, token) {
    this.globalData.userInfo = userInfo;
    this.globalData.token = token;

    // 更新全局状态
    setUser(userInfo, token);

    // 持久化存储
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('token', token);
  },

  // 清除用户信息
  clearUserInfo() {
    this.globalData.userInfo = null;
    this.globalData.token = null;

    // 更新全局状态
    clearUser();

    // 清除存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('token');
  },

  // 检查是否已登录
  isLoggedIn() {
    return !!(this.globalData.token && this.globalData.userInfo);
  }
});
