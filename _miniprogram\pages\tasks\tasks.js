// 任务列表页面
const { taskApi } = require('../../utils/api.js');
const { formatTime, getStatusText } = require('../../utils/util.js');
const app = getApp();

Page({
  data: {
    tasks: [],
    loading: true,
    refreshing: false,
    searchKeyword: '',
    filterStatus: 'ALL',
    statusOptions: [
      { value: 'ALL', label: '全部' },
      { value: 'TODO', label: '待办' },
      { value: 'IN_PROGRESS', label: '进行中' },
      { value: 'COMPLETED', label: '已完成' },
      { value: 'CANCELLED', label: '已取消' }
    ],
    showFilter: false,
    page: 1,
    hasMore: true
  },

  onLoad() {
    this.checkLogin();
  },

  onShow() {
    if (app.isLoggedIn()) {
      this.loadTasks();
    }
  },

  // 检查登录状态
  checkLogin() {
    if (!app.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    this.loadTasks();
  },

  // 加载任务列表
  async loadTasks(refresh = false) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        tasks: []
      });
    }

    this.setData({ loading: refresh ? false : true, refreshing: refresh });

    try {
      const params = {
        page: this.data.page,
        limit: 10,
        search: this.data.searchKeyword,
        status: this.data.filterStatus === 'ALL' ? '' : this.data.filterStatus
      };

      const response = await taskApi.getTaskList(params);
      const newTasks = response.data || [];

      this.setData({
        tasks: refresh ? newTasks : [...this.data.tasks, ...newTasks],
        hasMore: newTasks.length >= 10,
        page: refresh ? 2 : this.data.page + 1,
        loading: false,
        refreshing: false
      });

    } catch (error) {
      // 检查是否为API不可用错误
      if (error.message === 'API_UNAVAILABLE') {
        console.log('API服务器未连接，使用模拟数据');
      } else {
        console.error('加载任务列表失败:', error);
      }

      // 使用模拟数据
      const mockTasks = this.getMockTasks();
      this.setData({
        tasks: refresh ? mockTasks : [...this.data.tasks, ...mockTasks],
        hasMore: false,
        loading: false,
        refreshing: false
      });
    }
  },

  // 获取模拟任务数据
  getMockTasks() {
    return [
      {
        id: 1,
        title: '完成用户界面设计',
        description: '设计登录页面、首页和项目列表页面的用户界面',
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        dueDate: '2024-01-20',
        assignee: { id: 1, name: '张三', avatar: '' },
        project: { id: 1, title: 'LabSync 实验室管理系统' },
        createdAt: '2024-01-15'
      },
      {
        id: 2,
        title: '数据库设计',
        description: '设计用户、项目、任务等核心数据表结构',
        status: 'COMPLETED',
        priority: 'HIGH',
        dueDate: '2024-01-18',
        assignee: { id: 2, name: '李四', avatar: '' },
        project: { id: 1, title: 'LabSync 实验室管理系统' },
        createdAt: '2024-01-10'
      },
      {
        id: 3,
        title: '编写API文档',
        description: '编写后端API接口的详细文档',
        status: 'TODO',
        priority: 'MEDIUM',
        dueDate: '2024-01-25',
        assignee: { id: 3, name: '王五', avatar: '' },
        project: { id: 2, title: '数据分析平台' },
        createdAt: '2024-01-16'
      },
      {
        id: 4,
        title: '单元测试编写',
        description: '为核心功能模块编写单元测试',
        status: 'TODO',
        priority: 'LOW',
        dueDate: '2024-01-30',
        assignee: { id: 1, name: '张三', avatar: '' },
        project: { id: 1, title: 'LabSync 实验室管理系统' },
        createdAt: '2024-01-17'
      }
    ];
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  handleSearch() {
    this.loadTasks(true);
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.loadTasks(true);
  },

  // 切换筛选器显示
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 选择状态筛选
  selectStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      filterStatus: status,
      showFilter: false
    });
    this.loadTasks(true);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadTasks(true);
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadTasks();
    }
  },

  // 跳转到任务详情
  goToTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/task-detail/task-detail?id=${taskId}`
    });
  },

  // 创建新任务
  createTask() {
    wx.showModal({
      title: '创建任务',
      content: '此功能需要在详情页面实现',
      showCancel: false
    });
  },

  // 格式化任务状态
  formatStatus(status) {
    return getStatusText(status, 'task');
  },

  // 格式化时间
  formatTime(time) {
    return formatTime(time, 'YYYY-MM-DD');
  },

  // 获取优先级样式类
  getPriorityClass(priority) {
    const classMap = {
      'HIGH': 'priority-high',
      'MEDIUM': 'priority-medium',
      'LOW': 'priority-low'
    };
    return classMap[priority] || 'priority-medium';
  },

  // 获取状态样式类
  getStatusClass(status) {
    const classMap = {
      'TODO': 'status-pending',
      'IN_PROGRESS': 'status-active',
      'COMPLETED': 'status-completed',
      'CANCELLED': 'status-cancelled'
    };
    return classMap[status] || 'status-pending';
  }
});