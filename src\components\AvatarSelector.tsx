import React, { useState } from 'react';
import { getAvailableAvatars, generateSolidColorAvatar, getInitials } from '@/lib/avatars';

// 安全的Base64编码函数，支持Unicode字符
function safeBase64Encode(str: string): string {
  try {
    // 使用encodeURIComponent和btoa的组合来处理Unicode字符
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
      return String.fromCharCode(parseInt(p1, 16));
    }));
  } catch (error) {
    // 如果还是失败，使用URL编码作为备用方案
    return encodeURIComponent(str);
  }
}

interface AvatarSelectorProps {
  currentAvatar?: string;
  userName: string;
  onSelect: (avatarUrl: string) => void;
  onClose: () => void;
}

export default function AvatarSelector({
  currentAvatar,
  userName,
  onSelect,
  onClose
}: AvatarSelectorProps) {
  const [selectedAvatar, setSelectedAvatar] = useState(currentAvatar || '');
  const availableAvatars = getAvailableAvatars();
  const userInitials = getInitials(userName);

  // 生成带用户首字母的头像
  const avatarsWithInitials = availableAvatars.map(avatar => {
    if (avatar.id.startsWith('solid-')) {
      // 对于纯色头像，重新生成带首字母的版本
      const color = avatar.id === 'solid-blue' ? '#3B82F6' :
                   avatar.id === 'solid-green' ? '#10B981' :
                   avatar.id === 'solid-purple' ? '#8B5CF6' :
                   avatar.id === 'solid-pink' ? '#EC4899' :
                   avatar.id === 'solid-orange' ? '#F59E0B' :
                   avatar.id === 'solid-red' ? '#EF4444' :
                   avatar.id === 'solid-indigo' ? '#6366F1' :
                   avatar.id === 'solid-teal' ? '#14B8A6' : '#6B7280';

      return {
        ...avatar,
        url: generateSolidColorAvatar(color, userInitials)
      };
    }

    // 对于渐变头像，添加首字母
    try {
      const svgBase64 = avatar.url.split(',')[1];
      const svg = atob(svgBase64);
      const svgWithInitials = svg.replace(
        '</svg>',
        `<text x="20" y="26" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">${userInitials}</text></svg>`
      );

      return {
        ...avatar,
        url: `data:image/svg+xml;base64,${safeBase64Encode(svgWithInitials)}`
      };
    } catch (error) {
      // 如果解码失败，返回原始头像
      return avatar;
    }
  });

  const handleSelect = (avatarUrl: string) => {
    setSelectedAvatar(avatarUrl);
  };

  const handleConfirm = () => {
    onSelect(selectedAvatar);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            选择头像
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            当前头像预览
          </h4>
          <div className="flex justify-center">
            <img
              src={selectedAvatar || avatarsWithInitials[0].url}
              alt="当前选择的头像"
              className="w-16 h-16 rounded-full border-2 border-gray-300 dark:border-gray-600"
            />
          </div>
        </div>

        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            选择新头像
          </h4>
          <div className="grid grid-cols-4 gap-3">
            {avatarsWithInitials.map((avatar) => (
              <button
                key={avatar.id}
                onClick={() => handleSelect(avatar.url)}
                className={`p-1 rounded-lg border-2 transition-all duration-200 hover:scale-105 ${
                  selectedAvatar === avatar.url
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
                title={avatar.name}
              >
                <img
                  src={avatar.url}
                  alt={avatar.name}
                  className="w-12 h-12 rounded-full"
                />
              </button>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            确认选择
          </button>
        </div>
      </div>
    </div>
  );
}
