// 测试头像上传功能的脚本
const fs = require('fs');
const path = require('path');

console.log('🧪 测试头像上传功能...\n');

// 检查头像目录
const avatarDir = './public/avatars';
console.log('📁 检查头像目录...');

if (!fs.existsSync(avatarDir)) {
  console.log('❌ 头像目录不存在，正在创建...');
  fs.mkdirSync(avatarDir, { recursive: true });
  console.log('✅ 头像目录创建成功');
} else {
  console.log('✅ 头像目录存在');
}

// 检查目录权限
try {
  const testFile = path.join(avatarDir, 'test.txt');
  fs.writeFileSync(testFile, 'test');
  fs.unlinkSync(testFile);
  console.log('✅ 头像目录可写');
} catch (error) {
  console.error('❌ 头像目录权限问题:', error.message);
}

// 检查现有头像文件
const avatarFiles = fs.readdirSync(avatarDir);
console.log(`📊 现有头像文件数量: ${avatarFiles.length}`);

if (avatarFiles.length > 0) {
  console.log('📋 现有头像文件:');
  avatarFiles.forEach(file => {
    const filePath = path.join(avatarDir, file);
    const stats = fs.statSync(filePath);
    console.log(`  - ${file} (${Math.round(stats.size / 1024)}KB, ${stats.mtime.toLocaleString()})`);
  });
}

console.log('\n💡 头像上传功能说明:');
console.log('  • 支持的格式: JPEG, PNG, GIF, WebP');
console.log('  • 最大文件大小: 5MB');
console.log('  • 文件存储位置: /public/avatars/');
console.log('  • 文件命名规则: {userId}_{timestamp}.{ext}');
console.log('  • 自动删除旧头像文件');

console.log('\n🔧 如果头像上传仍有问题，请检查:');
console.log('  1. 文件格式是否支持');
console.log('  2. 文件大小是否超过5MB');
console.log('  3. 网络连接是否正常');
console.log('  4. 浏览器控制台是否有错误信息');
console.log('  5. 服务器日志是否有详细错误信息');

console.log('\n✅ 头像上传功能检查完成！');
