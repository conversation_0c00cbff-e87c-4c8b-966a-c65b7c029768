import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, isAdmin, getCurrentUserId } from '@/lib/auth';
import { notifyUserApproved, notifyUserRejected } from '@/lib/systemNotifications';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  // 检查是否为管理员
  if (!(await isAdmin(req, res))) {
    return res.status(403).json({ message: '需要管理员权限' });
  }

  const { id: userId } = req.query;
  const currentUserId = await getCurrentUserId(req, res);

  if (typeof userId !== 'string') {
    return res.status(400).json({ message: '无效的用户ID' });
  }

  // 处理PUT请求 - 更新用户状态
  if (req.method === 'PUT') {
    const { action } = req.body;

    if (!action || !['approve', 'reject'].includes(action)) {
      return res.status(400).json({ message: '无效的操作类型' });
    }

    try {
      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return res.status(404).json({ message: '用户不存在' });
      }

      if (user.status !== 'PENDING') {
        return res.status(400).json({ message: '该用户已经审核过了' });
      }

      // 更新用户状态
      const newStatus = action === 'approve' ? 'APPROVED' : 'REJECTED';

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          status: newStatus,
          approvedAt: action === 'approve' ? new Date() : null,
          approvedBy: action === 'approve' ? currentUserId : null,
        },
        select: {
          id: true,
          name: true,
          email: true,
          status: true,
          approvedAt: true,
        },
      });

      // 发送审核通知
      try {
        // 获取审核人信息
        const approver = await prisma.user.findUnique({
          where: { id: currentUserId! },
          select: { name: true },
        });

        if (approver) {
          if (action === 'approve') {
            await notifyUserApproved(userId, approver.name);
          } else {
            // 可以从请求体中获取拒绝原因
            const { reason } = req.body;
            await notifyUserRejected(userId, approver.name, reason);
          }
        }
      } catch (notificationError) {
        console.error('发送审核通知失败:', notificationError);
        // 不影响审核操作，只记录错误
      }

      const actionText = action === 'approve' ? '审核通过' : '拒绝';

      return res.status(200).json({
        message: `用户${actionText}成功`,
        user: updatedUser,
      });
    } catch (error) {
      console.error('更新用户状态失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
