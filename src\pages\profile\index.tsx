import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { getSafeAvatarUrl } from '@/lib/avatars';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  age?: number;
  bio?: string;
  phone?: string;
  department?: string;
  position?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export default function Profile() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [avatarTimestamp, setAvatarTimestamp] = useState(Date.now());

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取用户信息
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.id) {
      fetchUserProfile();
    }
  }, [status, session]);

  // 监听session中的avatar变化，重新获取用户信息
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.id && user) {
      // 如果session中的avatar与当前用户数据中的avatar不同，重新获取
      if (session.user.avatar !== user.avatar) {
        fetchUserProfile();
      }
    }
  }, [session?.user?.avatar, status, user]);

  // 监听头像更新事件
  useEffect(() => {
    const handleAvatarUpdate = (event: CustomEvent) => {
      console.log('收到头像更新事件:', event.detail);
      // 更新头像时间戳，强制刷新头像显示
      setAvatarTimestamp(Date.now());
      // 重新获取用户信息
      fetchUserProfile();
    };

    window.addEventListener('avatarUpdated', handleAvatarUpdate as EventListener);

    return () => {
      window.removeEventListener('avatarUpdated', handleAvatarUpdate as EventListener);
    };
  }, []);

  const fetchUserProfile = async () => {
    try {
      const response = await fetch(`/api/users/${session?.user?.id}`);

      if (!response.ok) {
        throw new Error('获取用户信息失败');
      }

      const userData = await response.json();
      setUser(userData);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      setError('获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  const getAvatarUrl = (user: UserProfile) => {
    if (user.avatar) {
      // 如果是本地头像文件，添加时间戳防止缓存
      if (user.avatar.startsWith('/avatars/')) {
        return `${user.avatar}?t=${avatarTimestamp}`;
      }
      return user.avatar;
    }
    // 使用新的头像系统
    return getSafeAvatarUrl({ id: user.id, name: user.name, avatar: user.avatar });
  };

  const getRoleDisplayName = (role: string) => {
    const roleMap: { [key: string]: string } = {
      'ADMIN': '管理员',
      'LEADER': '组长',
      'MEMBER': '成员',
      'GUEST': '访客'
    };
    return roleMap[role] || role;
  };

  // 加载中或未认证状态
  if (status === 'loading' || loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-3xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="max-w-3xl mx-auto">
        <div className="text-center text-gray-500 dark:text-gray-400">
          未找到用户信息
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              个人资料
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              查看和管理您的个人信息
            </p>
          </div>
          <Link
            href="/profile/edit"
            className="btn btn-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            编辑资料
          </Link>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        {/* 头部信息 */}
        <div className="px-6 py-8 bg-gradient-to-r from-primary-500 to-primary-600">
          <div className="flex items-center">
            <img
              src={getAvatarUrl(user)}
              alt={user.name}
              className="w-24 h-24 rounded-full border-4 border-white shadow-lg"
            />
            <div className="ml-6 text-white">
              <h2 className="text-2xl font-bold">{user.name}</h2>
              <p className="text-primary-100">{user.email}</p>
              <div className="flex items-center mt-2">
                <span className="px-3 py-1 bg-white/20 rounded-full text-sm font-medium">
                  {getRoleDisplayName(user.role)}
                </span>
                {user.position && (
                  <span className="ml-2 px-3 py-1 bg-white/20 rounded-full text-sm font-medium">
                    {user.position}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 详细信息 */}
        <div className="px-6 py-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {user.age && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  年龄
                </h3>
                <p className="mt-1 text-lg text-gray-900 dark:text-gray-100">
                  {user.age} 岁
                </p>
              </div>
            )}

            {user.phone && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  电话号码
                </h3>
                <p className="mt-1 text-lg text-gray-900 dark:text-gray-100">
                  {user.phone}
                </p>
              </div>
            )}

            {user.department && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  部门
                </h3>
                <p className="mt-1 text-lg text-gray-900 dark:text-gray-100">
                  {user.department}
                </p>
              </div>
            )}

            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                注册时间
              </h3>
              <p className="mt-1 text-lg text-gray-900 dark:text-gray-100">
                {new Date(user.createdAt).toLocaleDateString('zh-CN')}
              </p>
            </div>
          </div>

          {user.bio && (
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                个人简介
              </h3>
              <p className="mt-2 text-gray-900 dark:text-gray-100 leading-relaxed">
                {user.bio}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 快速操作 */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link
          href="/projects"
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">我的项目</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">查看参与的项目</p>
            </div>
          </div>
        </Link>

        <Link
          href="/tasks"
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">我的任务</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">查看分配的任务</p>
            </div>
          </div>
        </Link>

        <Link
          href="/files"
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">我的文件</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">查看上传的文件</p>
            </div>
          </div>
        </Link>
      </div>
    </div>
  );
}
