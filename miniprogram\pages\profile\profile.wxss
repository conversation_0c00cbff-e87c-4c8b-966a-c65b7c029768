/* 个人资料页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息卡片 */
.profile-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar-wrapper {
  position: relative;
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  font-size: 24rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-email {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 4rpx;
}

.user-role {
  font-size: 24rpx;
  opacity: 0.8;
}

.profile-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 菜单部分 */
.menu-section {
  margin-bottom: 30rpx;
}

.menu-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.menu-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f3f4f6;
  transition: background-color 0.2s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f9fafb;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 40rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #1f2937;
}

.menu-arrow {
  font-size: 28rpx;
  color: #9ca3af;
}

/* 退出登录部分 */
.logout-section {
  margin: 60rpx 0 40rpx;
}

.logout-btn {
  width: 100%;
  padding: 28rpx;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: background-color 0.2s;
}

.logout-btn:active {
  background: #dc2626;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 40rpx;
  color: #9ca3af;
  font-size: 24rpx;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 15rpx;
  }
  
  .profile-card {
    padding: 30rpx;
  }
  
  .profile-header {
    margin-bottom: 30rpx;
  }
  
  .avatar {
    width: 100rpx;
    height: 100rpx;
  }
  
  .user-name {
    font-size: 32rpx;
  }
  
  .profile-stats {
    gap: 20rpx;
  }
  
  .stat-number {
    font-size: 40rpx;
  }
  
  .menu-item {
    padding: 25rpx;
  }
  
  .menu-text {
    font-size: 28rpx;
  }
}
