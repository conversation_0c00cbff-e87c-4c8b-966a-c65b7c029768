import { NextApiRequest, NextApiResponse } from 'next';
import { isAuthenticated, getCurrentUserId } from '../../../lib/auth';
import prisma from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: '方法不允许' });
  }

  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权访问' });
  }

  const userId = await getCurrentUserId(req, res);
  if (!userId) {
    return res.status(401).json({ message: '用户未认证' });
  }

  try {

    // 获取团队成员统计
    const teamMembers = await prisma.user.findMany({
      where: {
        status: 'APPROVED',
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        role: true,
        position: true,
        department: true,
        age: true,
        bio: true,
        createdAt: true,
        ownedProjects: {
          select: {
            id: true,
            title: true,
            status: true,
          },
        },
        memberProjects: {
          select: {
            id: true,
            title: true,
            status: true,
          },
        },
        assignedTasks: {
          select: {
            id: true,
            status: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        _count: {
          select: {
            ownedProjects: true,
            memberProjects: true,
            assignedTasks: true,
            uploadedFiles: true,
            sentChatMessages: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // 计算团队统计数据
    const teamStats = {
      totalMembers: teamMembers.length,
      activeMembers: teamMembers.filter(member => {
        const recentActivity = member.assignedTasks.some(task => {
          const daysSinceUpdate = Math.floor((Date.now() - new Date(task.updatedAt).getTime()) / (1000 * 60 * 60 * 24));
          return daysSinceUpdate <= 7;
        });
        return recentActivity;
      }).length,
      totalProjects: await prisma.project.count(),
      totalTasks: await prisma.task.count(),
      completedTasks: await prisma.task.count({
        where: { status: 'COMPLETED' },
      }),
    };

    // 计算成员详细统计
    const memberStats = teamMembers.map(member => {
      const totalProjects = member.ownedProjects.length + member.memberProjects.length;
      const activeProjects = [...member.ownedProjects, ...member.memberProjects].filter(
        project => project.status === 'ACTIVE' || project.status === 'PLANNING'
      ).length;

      const completedTasks = member.assignedTasks.filter(task => task.status === 'COMPLETED').length;
      const totalTasks = member.assignedTasks.length;
      const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      // 计算贡献度（综合多个维度，区分角色权重）
      const now = Date.now();

      // 判断用户角色
      const isProjectLeader = member.ownedProjects.length > 0;
      const isTeamLeader = member.role === 'LEADER';
      const isAdmin = member.role === 'ADMIN';

      // 1. 项目负责人贡献 (权重: 35% for leaders, 20% for members)
      const projectLeadershipScore = isProjectLeader ?
        Math.min(35, member.ownedProjects.length * 15) : 0;
      const projectLeadershipWeight = isProjectLeader ? 35 : 20;

      // 2. 任务完成贡献 (权重: 30% for all)
      const recentCompletedTasks = member.assignedTasks.filter(task => {
        if (task.status !== 'COMPLETED') return false;
        const daysSinceUpdate = Math.floor((now - new Date(task.updatedAt).getTime()) / (1000 * 60 * 60 * 24));
        return daysSinceUpdate <= 30;
      }).length;
      const taskCompletionScore = Math.min(30, recentCompletedTasks * 6);

      // 3. 任务活跃度贡献 (权重: 20%)
      const recentTaskUpdates = member.assignedTasks.filter(task => {
        const daysSinceUpdate = Math.floor((now - new Date(task.updatedAt).getTime()) / (1000 * 60 * 60 * 24));
        return daysSinceUpdate <= 7;
      }).length;
      const taskActivityScore = Math.min(20, recentTaskUpdates * 7);

      // 4. 协作贡献 (权重: 10%)
      const collaborationScore = Math.min(10, member._count.sentChatMessages * 0.3);

      // 5. 资源贡献 (权重: 5%)
      const resourceScore = Math.min(5, member._count.uploadedFiles * 1);

      // 根据角色调整权重
      let roleMultiplier = 1.0;
      if (isAdmin) roleMultiplier = 1.2;
      else if (isTeamLeader) roleMultiplier = 1.1;
      else if (isProjectLeader) roleMultiplier = 1.05;

      // 综合计算贡献度分数 (0-100)
      const contributionScore = Math.min(100,
        (projectLeadershipScore +
        taskCompletionScore +
        taskActivityScore +
        collaborationScore +
        resourceScore) * roleMultiplier
      );

      return {
        id: member.id,
        name: member.name,
        email: member.email,
        avatar: member.avatar,
        role: member.role,
        position: member.position,
        department: member.department,
        age: member.age,
        bio: member.bio,
        stats: {
          totalProjects,
          activeProjects,
          totalTasks,
          completedTasks,
          completionRate: Math.round(completionRate),
          contributionScore: Math.round(contributionScore),
          isProjectLeader,
          isTeamLeader,
          filesUploaded: member._count.uploadedFiles,
          messagesCount: member._count.sentChatMessages,
        },
      };
    });

    // 按贡献度排序
    memberStats.sort((a, b) => b.stats.contributionScore - a.stats.contributionScore);

    // 角色分布统计
    const roleDistribution = teamMembers.reduce((acc, member) => {
      acc[member.role] = (acc[member.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // 部门分布统计
    const departmentDistribution = teamMembers.reduce((acc, member) => {
      const dept = member.department || '未设置';
      acc[dept] = (acc[dept] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return res.status(200).json({
      teamStats,
      memberStats,
      roleDistribution,
      departmentDistribution,
    });

  } catch (error) {
    console.error('获取团队统计失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后再试' });
  }
}
