# 任务删除功能说明

## 功能概述
LabSync 系统现已支持任务删除功能，允许有权限的用户安全地删除任务。

## 访问权限
只有以下用户可以删除任务：
- **任务负责人** - 被分配到该任务的用户
- **项目所有者** - 创建该项目的用户
- **系统管理员** - 具有 ADMIN 角色的用户

## 使用方法

### 1. 进入任务详情页面
- 通过项目页面的任务列表点击任务
- 或直接访问 `/tasks/{任务ID}`

### 2. 查看删除按钮
- 只有有权限的用户才能看到红色的"删除任务"按钮
- 按钮位于任务详情页面的操作按钮区域

### 3. 确认删除
- 点击"删除任务"按钮
- 系统会弹出确认对话框
- 对话框显示任务名称和警告信息
- 点击"确认删除"执行删除操作

### 4. 删除完成
- 删除成功后自动跳转到项目页面
- 项目进度会自动重新计算
- 相关文件和通知会被自动清理

## 安全特性

### 权限验证
- 前端和后端双重权限检查
- 确保只有授权用户可以执行删除操作

### 确认机制
- 删除前必须通过确认对话框
- 显示任务名称避免误删
- 明确提示删除的不可逆性

### 数据完整性
- 自动删除相关文件记录
- 清理相关通知数据
- 更新项目进度统计

## 技术实现

### API 端点
```
DELETE /api/tasks/{taskId}
```

### 权限检查逻辑
```javascript
const canDelete = 
  userId === task.assignee?.id ||      // 任务负责人
  userId === task.project.ownerId ||  // 项目所有者
  isUserAdmin;                        // 管理员
```

### 级联删除
- 任务文件关联 (onDelete: SetNull)
- 通知记录 (onDelete: Cascade)
- 项目进度自动重算

## 测试场景

### 正常删除流程
1. 使用有权限的账户登录
2. 进入任务详情页面
3. 点击删除按钮
4. 确认删除操作
5. 验证跳转和数据清理

### 权限验证测试
1. 使用无权限账户访问任务
2. 验证删除按钮不显示
3. 直接调用 API 验证权限拒绝

### 错误处理测试
1. 删除不存在的任务
2. 网络错误情况
3. 服务器错误处理

## 注意事项

1. **不可逆操作** - 删除后无法恢复，请谨慎操作
2. **数据清理** - 相关文件和通知会被自动删除
3. **进度影响** - 删除任务会影响项目完成进度
4. **权限限制** - 只有特定用户可以执行删除操作

## 相关文件
- `src/pages/api/tasks/[id].ts` - 删除 API 实现
- `src/pages/tasks/[id].tsx` - 前端删除界面
- `docs/bug-fixes.md` - 详细技术文档
