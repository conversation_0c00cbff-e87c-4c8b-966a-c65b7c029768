// 检查用户数据的脚本
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log('🔍 检查用户数据...\n');
    
    // 获取所有用户
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        createdAt: true,
        usedInviteCodeId: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    console.log(`📊 总用户数: ${users.length}\n`);
    
    // 按状态分组
    const statusGroups = users.reduce((groups, user) => {
      const status = user.status || 'NULL';
      if (!groups[status]) {
        groups[status] = [];
      }
      groups[status].push(user);
      return groups;
    }, {});
    
    console.log('📈 用户状态分布:');
    Object.keys(statusGroups).forEach(status => {
      console.log(`   ${status}: ${statusGroups[status].length} 个用户`);
    });
    
    console.log('\n👥 用户详细信息:');
    users.forEach((user, index) => {
      console.log(`\n${index + 1}. ${user.name} (${user.email})`);
      console.log(`   ID: ${user.id}`);
      console.log(`   角色: ${user.role}`);
      console.log(`   状态: ${user.status || 'NULL'}`);
      console.log(`   创建时间: ${user.createdAt.toISOString()}`);
      console.log(`   邀请码ID: ${user.usedInviteCodeId || 'NULL'}`);
    });
    
    // 检查已审核用户
    const approvedUsers = users.filter(user => user.status === 'APPROVED');
    console.log(`\n✅ 已审核用户数: ${approvedUsers.length}`);
    
    if (approvedUsers.length > 0) {
      console.log('已审核用户列表:');
      approvedUsers.forEach(user => {
        console.log(`   - ${user.name} (${user.email}) - ${user.role}`);
      });
    }
    
    // 检查项目数据
    console.log('\n📁 检查项目数据...');
    const projects = await prisma.project.findMany({
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        members: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
      },
    });
    
    console.log(`📊 总项目数: ${projects.length}\n`);
    
    projects.forEach((project, index) => {
      console.log(`${index + 1}. 项目: ${project.title}`);
      console.log(`   负责人: ${project.owner.name} (${project.owner.email})`);
      console.log(`   项目成员数: ${project.members.length}`);
      if (project.members.length > 0) {
        console.log(`   项目成员:`);
        project.members.forEach(member => {
          console.log(`     - ${member.name} (${member.email})`);
        });
      }
      console.log('');
    });
    
    // 分析可添加成员的情况
    if (projects.length > 0 && approvedUsers.length > 0) {
      console.log('🔍 分析可添加成员情况:\n');
      
      projects.forEach(project => {
        const currentMemberIds = [
          project.owner.id,
          ...project.members.map(member => member.id)
        ];
        
        const availableUsers = approvedUsers.filter(user =>
          !currentMemberIds.includes(user.id)
        );
        
        console.log(`项目 "${project.title}":`);
        console.log(`   当前成员: ${currentMemberIds.length} 人`);
        console.log(`   可添加成员: ${availableUsers.length} 人`);
        
        if (availableUsers.length > 0) {
          console.log(`   可添加的用户:`);
          availableUsers.forEach(user => {
            console.log(`     - ${user.name} (${user.email}) - ${user.role}`);
          });
        } else {
          console.log(`   ⚠️  没有可添加的用户`);
        }
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ 检查过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行检查
checkUsers();
