import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.email) {
      return res.status(401).json({ message: '未授权' });
    }

    // 检查用户权限（只有管理员和项目负责人可以导出）
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!currentUser || (currentUser.role !== 'ADMIN' && currentUser.role !== 'LEADER')) {
      return res.status(403).json({ message: '权限不足' });
    }

    // 获取所有用户数据
    const users = await prisma.user.findMany({
      where: {
        status: 'APPROVED',
      },
      include: {
        ownedProjects: {
          select: {
            id: true,
            status: true,
          },
        },
        memberProjects: {
          select: {
            id: true,
            status: true,
          },
        },
        assignedTasks: {
          select: {
            id: true,
            status: true,
            createdAt: true,
          },
        },
        uploadedFiles: {
          select: {
            id: true,
          },
        },
        messages: {
          select: {
            id: true,
          },
        },
        sentChatMessages: {
          select: {
            id: true,
          },
        },
      },
    });

    // 计算统计数据
    const memberStats = users.map(user => {
      const ownedProjects = user.ownedProjects;
      const memberProjects = user.memberProjects;
      const allProjects = [...ownedProjects, ...memberProjects];

      const totalProjects = allProjects.length;
      const activeProjects = allProjects.filter(p => p.status === 'ACTIVE').length;

      const totalTasks = user.assignedTasks.length;
      const completedTasks = user.assignedTasks.filter(t => t.status === 'COMPLETED').length;
      const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      // 计算贡献度评分
      const isProjectLeader = user.ownedProjects.length > 0;
      const isTeamLeader = user.role === 'LEADER';
      const isAdmin = user.role === 'ADMIN';

      const recentTasks = user.assignedTasks.filter(t => {
        const taskDate = new Date(t.createdAt);
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        return taskDate > thirtyDaysAgo;
      }).length;

      // 基础贡献度计算
      let contributionScore = 0;
      contributionScore += isProjectLeader ? user.ownedProjects.length * 15 : 0; // 项目负责人贡献
      contributionScore += completedTasks * 6; // 任务完成贡献
      contributionScore += recentTasks * 7; // 近期任务活跃度

      // 角色权重调整
      let roleMultiplier = 1.0;
      if (isAdmin) roleMultiplier = 1.2;
      else if (isTeamLeader) roleMultiplier = 1.1;
      else if (isProjectLeader) roleMultiplier = 1.05;

      contributionScore = Math.min(100, contributionScore * roleMultiplier);

      return {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        position: user.position || '',
        department: user.department || '',
        age: user.age || '',
        bio: user.bio || '',
        totalProjects,
        activeProjects,
        totalTasks,
        completedTasks,
        completionRate: Math.round(completionRate),
        contributionScore: Math.round(contributionScore),
        isProjectLeader,
        isTeamLeader,
        filesUploaded: user.uploadedFiles.length,
        messagesCount: user.messages.length + user.sentChatMessages.length,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
      };
    });

    // 生成CSV内容
    const csvHeaders = [
      '姓名',
      '邮箱',
      '角色',
      '职位',
      '部门',
      '年龄',
      '个人简介',
      '总项目数',
      '活跃项目数',
      '总任务数',
      '已完成任务数',
      '完成率(%)',
      '贡献度评分',
      '是否项目负责人',
      '是否团队负责人',
      '上传文件数',
      '发送消息数',
      '注册时间',
      '最后登录时间',
    ];

    const csvRows = memberStats.map(member => [
      member.name,
      member.email,
      getRoleDisplayName(member.role),
      member.position,
      member.department,
      member.age,
      member.bio.replace(/"/g, '""'), // 转义双引号
      member.totalProjects,
      member.activeProjects,
      member.totalTasks,
      member.completedTasks,
      member.completionRate,
      member.contributionScore,
      member.isProjectLeader ? '是' : '否',
      member.isTeamLeader ? '是' : '否',
      member.filesUploaded,
      member.messagesCount,
      formatDate(member.createdAt),
      member.lastLoginAt ? formatDate(member.lastLoginAt) : '从未登录',
    ]);

    // 构建CSV内容
    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row =>
        row.map(field =>
          typeof field === 'string' && field.includes(',')
            ? `"${field}"`
            : field
        ).join(',')
      )
    ].join('\n');

    // 添加BOM以支持中文
    const bom = '\uFEFF';
    const csvWithBom = bom + csvContent;

    // 设置响应头
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="team-data-${new Date().toISOString().split('T')[0]}.csv"`);

    res.status(200).send(csvWithBom);
  } catch (error) {
    console.error('导出团队数据失败:', error);
    res.status(500).json({ message: '服务器错误' });
  } finally {
    await prisma.$disconnect();
  }
}

function getRoleDisplayName(role: string) {
  switch (role) {
    case 'ADMIN': return '管理员';
    case 'LEADER': return '项目负责人';
    case 'MEMBER': return '团队成员';
    default: return '访客';
  }
}

function formatDate(date: Date) {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
}
