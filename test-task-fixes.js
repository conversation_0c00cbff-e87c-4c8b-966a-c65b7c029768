// 测试任务相关修复的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testTaskFixes() {
  console.log('🧪 测试任务相关修复...\n');

  try {
    // 1. 测试任务数据结构
    console.log('📋 检查任务数据结构...');
    const tasks = await prisma.task.findMany({
      include: {
        project: {
          select: {
            id: true,
            title: true,
            ownerId: true,
          },
        },
        assignee: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        files: {
          include: {
            uploader: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    console.log(`✅ 找到 ${tasks.length} 个任务`);
    
    // 检查每个任务的files字段
    tasks.forEach((task, index) => {
      const filesCount = task.files ? task.files.length : 0;
      console.log(`  任务 ${index + 1}: "${task.title}" - ${filesCount} 个文件`);
      
      if (task.files === null || task.files === undefined) {
        console.log(`    ⚠️  警告: 任务 "${task.title}" 的files字段为 ${task.files}`);
      }
    });

    // 2. 测试任务状态分布
    console.log('\n📊 任务状态分布:');
    const statusCounts = {
      'TODO': tasks.filter(t => t.status === 'TODO').length,
      'IN_PROGRESS': tasks.filter(t => t.status === 'IN_PROGRESS').length,
      'REVIEW': tasks.filter(t => t.status === 'REVIEW').length,
      'COMPLETED': tasks.filter(t => t.status === 'COMPLETED').length,
    };

    Object.entries(statusCounts).forEach(([status, count]) => {
      const statusName = {
        'TODO': '待处理',
        'IN_PROGRESS': '进行中',
        'REVIEW': '待审核',
        'COMPLETED': '已完成'
      }[status];
      
      console.log(`  ${statusName}: ${count} 个任务`);
    });

    // 3. 测试任务优先级分布
    console.log('\n🎯 任务优先级分布:');
    const priorityCounts = {
      'LOW': tasks.filter(t => t.priority === 'LOW').length,
      'MEDIUM': tasks.filter(t => t.priority === 'MEDIUM').length,
      'HIGH': tasks.filter(t => t.priority === 'HIGH').length,
      'URGENT': tasks.filter(t => t.priority === 'URGENT').length,
    };

    Object.entries(priorityCounts).forEach(([priority, count]) => {
      const priorityName = {
        'LOW': '低',
        'MEDIUM': '中',
        'HIGH': '高',
        'URGENT': '紧急'
      }[priority];
      
      if (count > 0) {
        console.log(`  ${priorityName}: ${count} 个任务`);
      }
    });

    // 4. 测试任务分配情况
    console.log('\n👥 任务分配情况:');
    const assignedTasks = tasks.filter(t => t.assignee);
    const unassignedTasks = tasks.filter(t => !t.assignee);
    
    console.log(`  已分配任务: ${assignedTasks.length} 个`);
    console.log(`  未分配任务: ${unassignedTasks.length} 个`);

    if (assignedTasks.length > 0) {
      console.log('\n  分配详情:');
      const assigneeStats = {};
      assignedTasks.forEach(task => {
        const assigneeName = task.assignee.name;
        if (!assigneeStats[assigneeName]) {
          assigneeStats[assigneeName] = {
            total: 0,
            completed: 0,
          };
        }
        assigneeStats[assigneeName].total++;
        if (task.status === 'COMPLETED') {
          assigneeStats[assigneeName].completed++;
        }
      });

      Object.entries(assigneeStats).forEach(([name, stats]) => {
        const completionRate = stats.total > 0 ? (stats.completed / stats.total * 100).toFixed(1) : 0;
        console.log(`    ${name}: ${stats.completed}/${stats.total} (${completionRate}%)`);
      });
    }

    // 5. 测试任务文件关联
    console.log('\n📎 任务文件关联:');
    const tasksWithFiles = tasks.filter(t => t.files && t.files.length > 0);
    console.log(`  有文件的任务: ${tasksWithFiles.length} 个`);
    
    if (tasksWithFiles.length > 0) {
      console.log('  详情:');
      tasksWithFiles.forEach(task => {
        console.log(`    "${task.title}": ${task.files.length} 个文件`);
      });
    }

    // 6. 测试项目关联
    console.log('\n🏗️ 项目关联情况:');
    const projectStats = {};
    tasks.forEach(task => {
      const projectTitle = task.project.title;
      if (!projectStats[projectTitle]) {
        projectStats[projectTitle] = {
          total: 0,
          completed: 0,
        };
      }
      projectStats[projectTitle].total++;
      if (task.status === 'COMPLETED') {
        projectStats[projectTitle].completed++;
      }
    });

    Object.entries(projectStats).forEach(([projectTitle, stats]) => {
      const completionRate = stats.total > 0 ? (stats.completed / stats.total * 100).toFixed(1) : 0;
      console.log(`  ${projectTitle}: ${stats.completed}/${stats.total} (${completionRate}%)`);
    });

    console.log('\n✅ 任务相关修复测试完成！');
    console.log('\n🔧 修复内容:');
    console.log('  • 修复了 task.files.length 的 undefined 错误');
    console.log('  • 添加了安全的空值检查 (task.files?.length || 0)');
    console.log('  • 修复了文件上传后的状态更新');
    console.log('  • 创建了任务编辑页面 (/tasks/[id]/edit)');
    console.log('  • 确保任务API返回完整的files字段');

    console.log('\n💡 使用建议:');
    console.log('  • 点击任务详情页的"编辑"按钮可以编辑任务');
    console.log('  • 使用"更新状态"下拉菜单快速更改任务状态');
    console.log('  • 在"相关文件"标签页可以上传和查看任务文件');
    console.log('  • 所有操作都有适当的权限检查');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testTaskFixes();
