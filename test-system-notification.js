// 测试系统通知功能的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSystemNotification() {
  try {
    console.log('开始测试系统通知功能...');

    // 获取第一个用户
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('没有找到用户');
      return;
    }

    console.log(`为用户 ${user.name} 创建系统通知...`);

    // 查找或创建系统聊天
    let systemChat = await prisma.chat.findFirst({
      where: {
        type: 'SYSTEM',
        participants: {
          some: {
            id: user.id,
          },
        },
      },
    });

    if (!systemChat) {
      console.log('创建系统聊天...');
      systemChat = await prisma.chat.create({
        data: {
          type: 'SYSTEM',
          name: '系统通知',
          participants: {
            connect: { id: user.id },
          },
        },
      });
    }

    console.log(`系统聊天ID: ${systemChat.id}`);

    // 创建系统通知消息
    const systemMessage = await prisma.chatMessage.create({
      data: {
        content: '📋 您被分配了新任务：「测试任务」\n项目：测试项目\n分配人：系统管理员',
        type: 'SYSTEM',
        isSystem: true,
        chatId: systemChat.id,
        // 系统消息不需要发送者
      },
    });

    console.log(`系统消息创建成功: ${systemMessage.id}`);

    // 更新聊天的最后更新时间
    await prisma.chat.update({
      where: { id: systemChat.id },
      data: { updatedAt: new Date() },
    });

    console.log('系统通知测试完成！');

    // 查看结果
    const chatWithMessages = await prisma.chat.findUnique({
      where: { id: systemChat.id },
      include: {
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
        participants: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    console.log('聊天详情:', {
      id: chatWithMessages.id,
      type: chatWithMessages.type,
      name: chatWithMessages.name,
      participants: chatWithMessages.participants,
      messageCount: chatWithMessages.messages.length,
      latestMessage: chatWithMessages.messages[0],
    });

  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSystemNotification();
