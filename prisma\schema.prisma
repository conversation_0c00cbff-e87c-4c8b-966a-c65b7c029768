// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户模型
model User {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  password      String
  role          String    @default("MEMBER") // ADMIN, LEADER, MEMBER, GUEST
  status        String    @default("PENDING") // PENDING, APPROVED, REJECTED
  avatar        String?   // 头像URL
  age           Int?      // 年龄
  bio           String?   // 个人简介
  phone         String?   // 电话号码
  department    String?   // 部门
  position      String?   // 职位
  approvedAt    DateTime? // 审核通过时间
  approvedBy    String?   // 审核人ID
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // 关联
  ownedProjects     Project[]     @relation("ProjectOwner")
  memberProjects    Project[]     @relation("ProjectMembers")
  assignedTasks     Task[]        @relation("TaskAssignee")
  assignedToTasks   Task[]        @relation("TaskAssignees") // 多人分配的任务
  uploadedFiles     File[]        @relation("FileUploader")
  messages          Message[]     @relation("MessageSender")
  chats             Chat[]        @relation("ChatParticipants")
  sentChatMessages  ChatMessage[] @relation("ChatMessageSender")
  readMessages      MessageRead[] // 已读消息
  notifications     Notification[]

  // 邀请码关联
  createdInviteCodes InviteCode[] @relation("InviteCodeCreator")
  usedInviteCode     InviteCode?  @relation("InviteCodeUser", fields: [usedInviteCodeId], references: [id])
  usedInviteCodeId   String?
  lastLoginAt        DateTime?    // 最后登录时间

  // 邀请关联
  sentInvitations    Invitation[] @relation("InvitationSender")
  receivedInvitation Invitation?  @relation("InvitationReceiver")
  createdMeetings    Meeting[]    @relation("MeetingCreator")
}

// 邀请码模型
model InviteCode {
  id          String    @id @default(cuid())
  code        String    @unique // 邀请码
  description String?   // 描述（如：张教授邀请的博士生）
  maxUses     Int       @default(1) // 最大使用次数
  usedCount   Int       @default(0) // 已使用次数
  expiresAt   DateTime? // 过期时间
  isActive    Boolean   @default(true) // 是否激活
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联
  createdBy   User      @relation("InviteCodeCreator", fields: [createdById], references: [id])
  createdById String
  usedBy      User[]    @relation("InviteCodeUser") // 使用此邀请码的用户
}

// 邀请模型（邮件邀请）
model Invitation {
  id          String    @id @default(cuid())
  email       String    // 被邀请人邮箱
  role        String    // 邀请角色
  code        String    @unique // 邀请码
  status      String    @default("PENDING") // PENDING, ACCEPTED, EXPIRED, CANCELLED
  expiresAt   DateTime  // 过期时间
  acceptedAt  DateTime? // 接受时间
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联
  inviter     User      @relation("InvitationSender", fields: [inviterId], references: [id])
  inviterId   String
  acceptedBy  User?     @relation("InvitationReceiver", fields: [acceptedById], references: [id])
  acceptedById String?  @unique
}

// 腾讯会议配置模型
model MeetingConfig {
  id        String   @id @default(cuid())
  appId     String   // 腾讯会议App ID
  secretId  String   // Secret ID
  secretKey String   // Secret Key
  sdkId     String   // SDK ID
  isEnabled Boolean  @default(false) // 是否启用
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("meeting_configs")
}

// 会议记录模型
model Meeting {
  id          String   @id @default(cuid())
  meetingId   String   @unique // 腾讯会议ID
  meetingCode String   // 会议号
  subject     String   // 会议主题
  joinUrl     String   // 加入链接
  password    String?  // 会议密码
  startTime   DateTime // 开始时间
  endTime     DateTime // 结束时间
  status      String   @default("SCHEDULED") // SCHEDULED, STARTED, ENDED, CANCELLED
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联
  creator     User     @relation("MeetingCreator", fields: [creatorId], references: [id])
  creatorId   String

  @@map("meetings")
}

// 用户角色类型
model UserRole {
  @@map("user_roles")
  name String @id // ADMIN, LEADER, MEMBER, GUEST
}

// 项目模型
model Project {
  id          String    @id @default(cuid())
  title       String
  description String?
  startDate   DateTime
  endDate     DateTime?
  status      String    @default("ACTIVE") // PLANNING, ACTIVE, COMPLETED, ARCHIVED
  progress    Float     @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联
  owner       User      @relation("ProjectOwner", fields: [ownerId], references: [id])
  ownerId     String
  members     User[]    @relation("ProjectMembers")
  tasks       Task[]
  files       File[]
  messages    Message[]
}

// 项目状态类型
model ProjectStatus {
  @@map("project_statuses")
  name String @id // PLANNING, ACTIVE, COMPLETED, ARCHIVED
}

// 任务模型
model Task {
  id          String    @id @default(cuid())
  title       String
  description String?
  dueDate     DateTime?
  status      String    @default("TODO") // TODO, IN_PROGRESS, REVIEW, COMPLETED
  priority    String    @default("MEDIUM") // LOW, MEDIUM, HIGH, URGENT
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId   String
  assignee    User?     @relation("TaskAssignee", fields: [assigneeId], references: [id])
  assigneeId  String?   // 主要负责人
  assignees   User[]    @relation("TaskAssignees") // 多人分配
  files       File[]
}

// 任务状态类型
model TaskStatus {
  @@map("task_statuses")
  name String @id // TODO, IN_PROGRESS, REVIEW, COMPLETED
}

// 任务优先级类型
model TaskPriority {
  @@map("task_priorities")
  name String @id // LOW, MEDIUM, HIGH, URGENT
}

// 文件模型
model File {
  id          String    @id @default(cuid())
  name        String
  path        String
  type        String
  size        Int
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联
  uploader    User      @relation("FileUploader", fields: [uploaderId], references: [id])
  uploaderId  String
  project     Project?  @relation(fields: [projectId], references: [id], onDelete: SetNull)
  projectId   String?
  task        Task?     @relation(fields: [taskId], references: [id], onDelete: SetNull)
  taskId      String?
}

// 消息模型（项目消息）
model Message {
  id          String    @id @default(cuid())
  content     String
  type        String    @default("TEXT") // TEXT, FILE, IMAGE
  fileName    String?   // 文件名（当type为FILE时）
  fileUrl     String?   // 文件URL（当type为FILE时）
  fileSize    Int?      // 文件大小（当type为FILE时）
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联
  sender      User      @relation("MessageSender", fields: [senderId], references: [id])
  senderId    String
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId   String
}

// 聊天模型
model Chat {
  id           String    @id @default(cuid())
  type         String    @default("PRIVATE") // PRIVATE, GROUP
  name         String?   // 群聊名称（私聊为空）
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // 关联
  participants User[]        @relation("ChatParticipants")
  messages     ChatMessage[]
}

// 聊天消息模型
model ChatMessage {
  id        String   @id @default(cuid())
  content   String
  type      String   @default("TEXT") // TEXT, FILE, IMAGE, SYSTEM
  fileName  String?  // 文件名（当type为FILE时）
  fileUrl   String?  // 文件URL（当type为FILE时）
  fileSize  Int?     // 文件大小（当type为FILE时）
  isSystem  Boolean  @default(false) // 是否为系统消息
  isRead    Boolean  @default(false) // 是否已读（用于系统消息）
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联
  sender    User?    @relation("ChatMessageSender", fields: [senderId], references: [id])
  senderId  String?  // 系统消息时可以为空
  chat      Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  chatId    String

  // 消息已读状态
  readBy    MessageRead[]
}

// 消息已读状态模型
model MessageRead {
  id        String   @id @default(cuid())
  readAt    DateTime @default(now())

  // 关联
  user      User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  message   ChatMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  messageId String

  @@unique([userId, messageId])
}

// 通知模型
model Notification {
  id          String   @id @default(cuid())
  type        String   // TASK_ASSIGNED, PROJECT_UPDATED, MESSAGE_RECEIVED, FILE_UPLOADED
  title       String
  message     String
  read        Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String

  // 相关资源ID（可选）
  relatedId   String?  // 相关的项目、任务或消息ID
  relatedType String?  // 'project', 'task', 'message'
}
