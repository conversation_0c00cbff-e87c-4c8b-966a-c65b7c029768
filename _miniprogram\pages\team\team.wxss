/* 团队管理页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.team-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  background: white;
  border-radius: 12rpx;
  padding: 25rpx 15rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}

.member-list {
  margin-bottom: 120rpx;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.member-item:active {
  transform: scale(0.98);
}

.member-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  position: relative;
}

.member-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.online-indicator {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  background: #10b981;
  border: 3rpx solid white;
  border-radius: 50%;
}

.member-content {
  flex: 1;
  min-width: 0;
}

.member-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.member-role {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}

.member-department {
  font-size: 24rpx;
  color: #9ca3af;
}

.member-actions {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  transition: background-color 0.2s;
}

.chat-btn {
  background: #dbeafe;
  color: #3b82f6;
}

.more-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: #3b82f6;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
  z-index: 100;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}

.loading {
  text-align: center;
  padding: 60rpx;
  color: #6b7280;
  font-size: 28rpx;
}

.empty {
  text-align: center;
  padding: 120rpx 60rpx;
  color: #9ca3af;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #d1d5db;
}
