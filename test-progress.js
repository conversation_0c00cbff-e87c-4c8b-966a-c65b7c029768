// 测试项目进度条功能的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testProgressUpdate() {
  console.log('🧪 测试项目进度条功能...\n');

  try {
    // 获取第一个项目
    const project = await prisma.project.findFirst({
      include: {
        tasks: true
      }
    });

    if (!project) {
      console.log('❌ 没有找到项目');
      return;
    }

    console.log(`📋 项目: ${project.title}`);
    console.log(`📊 当前进度: ${Math.round(project.progress * 100)}%`);
    console.log(`📝 任务总数: ${project.tasks.length}`);
    
    const completedTasks = project.tasks.filter(task => task.status === 'COMPLETED');
    console.log(`✅ 已完成任务: ${completedTasks.length}`);
    
    const expectedProgress = project.tasks.length > 0 ? completedTasks.length / project.tasks.length : 0;
    console.log(`🎯 预期进度: ${Math.round(expectedProgress * 100)}%\n`);

    // 检查进度是否正确
    if (Math.abs(project.progress - expectedProgress) < 0.01) {
      console.log('✅ 项目进度计算正确！');
    } else {
      console.log('❌ 项目进度不正确，正在修复...');
      
      // 更新项目进度
      await prisma.project.update({
        where: { id: project.id },
        data: { progress: expectedProgress }
      });
      
      console.log('✅ 项目进度已修复！');
    }

    // 显示任务状态分布
    console.log('\n📈 任务状态分布:');
    const statusCounts = {
      'TODO': project.tasks.filter(t => t.status === 'TODO').length,
      'IN_PROGRESS': project.tasks.filter(t => t.status === 'IN_PROGRESS').length,
      'REVIEW': project.tasks.filter(t => t.status === 'REVIEW').length,
      'COMPLETED': project.tasks.filter(t => t.status === 'COMPLETED').length,
    };

    Object.entries(statusCounts).forEach(([status, count]) => {
      const statusName = {
        'TODO': '待处理',
        'IN_PROGRESS': '进行中',
        'REVIEW': '待审核',
        'COMPLETED': '已完成'
      }[status];
      
      if (count > 0) {
        console.log(`  ${statusName}: ${count} 个任务`);
      }
    });

    // 测试任务状态更新对进度的影响
    console.log('\n🔄 测试任务状态更新...');
    
    const todoTask = project.tasks.find(task => task.status === 'TODO');
    if (todoTask) {
      console.log(`📝 将任务 "${todoTask.title}" 状态改为 "进行中"`);
      
      // 更新任务状态
      await prisma.task.update({
        where: { id: todoTask.id },
        data: { status: 'IN_PROGRESS' }
      });

      // 更新项目进度
      const updatedTasks = await prisma.task.findMany({
        where: { projectId: project.id }
      });
      
      const newCompletedTasks = updatedTasks.filter(task => task.status === 'COMPLETED');
      const newProgress = newCompletedTasks.length / updatedTasks.length;
      
      await prisma.project.update({
        where: { id: project.id },
        data: { progress: newProgress }
      });

      console.log(`📊 更新后进度: ${Math.round(newProgress * 100)}%`);
      
      // 恢复原状态
      await prisma.task.update({
        where: { id: todoTask.id },
        data: { status: 'TODO' }
      });
      
      await prisma.project.update({
        where: { id: project.id },
        data: { progress: expectedProgress }
      });
      
      console.log('🔄 已恢复原状态');
    }

    console.log('\n✅ 项目进度条功能测试完成！');
    console.log('\n💡 进度条功能说明:');
    console.log('  • 进度 = 已完成任务数 / 总任务数');
    console.log('  • 任务状态变更时自动更新项目进度');
    console.log('  • 在项目详情页和项目卡片中显示进度条');
    console.log('  • 进度条颜色根据完成度变化（红色<30%, 黄色30-70%, 绿色>70%）');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testProgressUpdate();
