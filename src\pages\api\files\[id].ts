import type { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';
import { isProjectMember } from '@/lib/permissions';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const { id } = req.query;
  const fileId = Array.isArray(id) ? id[0] : id;

  // 检查文件是否存在
  const file = await prisma.file.findUnique({
    where: { id: fileId },
  });

  if (!file) {
    return res.status(404).json({ message: '文件不存在' });
  }

  // 检查用户是否有权限访问该文件
  const userId = await getCurrentUserId(req, res);

  // 验证用户ID
  if (!userId) {
    return res.status(401).json({ message: '用户未认证' });
  }

  let hasAccess = false;

  // 文件上传者可以访问
  if (file.uploaderId === userId) {
    hasAccess = true;
  }
  // 如果文件关联了项目，检查用户是否为项目成员
  else if (file.projectId) {
    hasAccess = await isProjectMember(req, res, file.projectId);
  }
  // 如果文件关联了任务，检查用户是否有权限访问该任务所属的项目
  else if (file.taskId) {
    const task = await prisma.task.findUnique({
      where: { id: file.taskId },
      select: { projectId: true },
    });

    if (task) {
      hasAccess = await isProjectMember(req, res, task.projectId);
    }
  }

  if (!hasAccess) {
    return res.status(403).json({ message: '没有权限访问该文件' });
  }

  // 处理GET请求 - 获取文件详情或下载文件
  if (req.method === 'GET') {
    try {
      const { download } = req.query;

      // 如果请求下载文件
      if (download === 'true') {
        const filePath = path.join(process.cwd(), 'uploads', file.path);

        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
          return res.status(404).json({ message: '文件不存在或已被删除' });
        }

        // 设置响应头
        res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(file.name)}`);
        res.setHeader('Content-Type', file.type);

        // 创建文件读取流并发送文件
        const fileStream = fs.createReadStream(filePath);
        fileStream.pipe(res);
        return;
      }

      // 否则返回文件详情
      const fileDetails = await prisma.file.findUnique({
        where: { id: fileId },
        include: {
          uploader: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          project: {
            select: {
              id: true,
              title: true,
            },
          },
          task: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      });

      return res.status(200).json(fileDetails);
    } catch (error) {
      console.error('获取文件详情失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理DELETE请求 - 删除文件
  if (req.method === 'DELETE') {
    // 只有文件上传者或项目所有者可以删除文件
    const canDelete = file.uploaderId === userId ||
                     (file.projectId && await prisma.project.findFirst({
                       where: {
                         id: file.projectId,
                         ownerId: userId
                       }
                     }));

    if (!canDelete) {
      return res.status(403).json({ message: '没有权限删除该文件' });
    }

    try {
      // 删除物理文件
      const filePath = path.join(process.cwd(), 'uploads', file.path);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      // 删除数据库记录
      await prisma.file.delete({
        where: { id: fileId },
      });

      return res.status(200).json({ message: '文件已成功删除' });
    } catch (error) {
      console.error('删除文件失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理PUT请求 - 更新文件信息
  if (req.method === 'PUT') {
    // 只有文件上传者或项目所有者可以更新文件信息
    const canUpdate = file.uploaderId === userId ||
                     (file.projectId && await prisma.project.findFirst({
                       where: {
                         id: file.projectId,
                         ownerId: userId
                       }
                     }));

    if (!canUpdate) {
      return res.status(403).json({ message: '没有权限更新该文件信息' });
    }

    const { name, description, projectId, taskId } = req.body;

    try {
      // 更新文件信息
      const updatedFile = await prisma.file.update({
        where: { id: fileId },
        data: {
          ...(name && { name }),
          ...(description !== undefined && { description }),
          ...(projectId && {
            project: {
              connect: { id: projectId },
            },
          }),
          ...(taskId && {
            task: {
              connect: { id: taskId },
            },
          }),
        },
        include: {
          uploader: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          project: {
            select: {
              id: true,
              title: true,
            },
          },
          task: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      });

      return res.status(200).json(updatedFile);
    } catch (error) {
      console.error('更新文件信息失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
