// 通用工具函数

/**
 * 格式化时间
 * @param {Date|string|number} date 日期
 * @param {string} format 格式 (YYYY-MM-DD HH:mm:ss)
 */
function formatTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hour = String(d.getHours()).padStart(2, '0');
  const minute = String(d.getMinutes()).padStart(2, '0');
  const second = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second);
}

/**
 * 格式化相对时间
 * @param {Date|string|number} date 日期
 */
function formatRelativeTime(date) {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const now = new Date();
  const diff = now.getTime() - d.getTime();
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (seconds < 60) {
    return '刚刚';
  } else if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return formatTime(date, 'MM-DD');
  }
}

/**
 * 验证邮箱格式
 * @param {string} email 邮箱地址
 */
function validateEmail(email) {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

/**
 * 验证手机号格式
 * @param {string} phone 手机号
 */
function validatePhone(phone) {
  const regex = /^1[3-9]\d{9}$/;
  return regex.test(phone);
}

/**
 * 验证密码强度
 * @param {string} password 密码
 */
function validatePassword(password) {
  if (!password || password.length < 6) {
    return { valid: false, message: '密码长度至少6位' };
  }
  
  if (password.length > 20) {
    return { valid: false, message: '密码长度不能超过20位' };
  }
  
  // 检查是否包含字母和数字
  const hasLetter = /[a-zA-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  
  if (!hasLetter || !hasNumber) {
    return { valid: false, message: '密码必须包含字母和数字' };
  }
  
  return { valid: true, message: '密码强度良好' };
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 */
function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取文件扩展名
 * @param {string} filename 文件名
 */
function getFileExtension(filename) {
  if (!filename) return '';
  const lastDot = filename.lastIndexOf('.');
  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
}

/**
 * 判断是否为图片文件
 * @param {string} filename 文件名
 */
function isImageFile(filename) {
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  const ext = getFileExtension(filename);
  return imageExts.includes(ext);
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间(ms)
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 限制时间(ms)
 */
function throttle(func, limit) {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

/**
 * 生成随机字符串
 * @param {number} length 长度
 */
function generateRandomString(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 获取状态显示文本
 * @param {string} status 状态值
 * @param {string} type 类型 (project|task|user)
 */
function getStatusText(status, type = 'project') {
  const statusMap = {
    project: {
      'PLANNING': '计划中',
      'ACTIVE': '进行中',
      'COMPLETED': '已完成',
      'ARCHIVED': '已归档'
    },
    task: {
      'TODO': '待办',
      'IN_PROGRESS': '进行中',
      'REVIEW': '待审核',
      'COMPLETED': '已完成'
    },
    user: {
      'PENDING': '待审核',
      'APPROVED': '已批准',
      'REJECTED': '已拒绝'
    }
  };
  
  return statusMap[type]?.[status] || status;
}

/**
 * 获取优先级显示文本
 * @param {string} priority 优先级值
 */
function getPriorityText(priority) {
  const priorityMap = {
    'LOW': '低',
    'MEDIUM': '中',
    'HIGH': '高',
    'URGENT': '紧急'
  };
  
  return priorityMap[priority] || priority;
}

/**
 * 计算项目进度
 * @param {Array} tasks 任务列表
 */
function calculateProgress(tasks) {
  if (!tasks || tasks.length === 0) return 0;
  
  const completedTasks = tasks.filter(task => task.status === 'COMPLETED');
  return Math.round((completedTasks.length / tasks.length) * 100);
}

/**
 * 检查任务是否逾期
 * @param {string|Date} dueDate 截止日期
 */
function isTaskOverdue(dueDate) {
  if (!dueDate) return false;
  const due = new Date(dueDate);
  const now = new Date();
  return due < now;
}

/**
 * 存储数据到本地
 * @param {string} key 键名
 * @param {any} data 数据
 */
function setStorage(key, data) {
  try {
    wx.setStorageSync(key, data);
    return true;
  } catch (e) {
    console.error('存储数据失败:', e);
    return false;
  }
}

/**
 * 从本地获取数据
 * @param {string} key 键名
 * @param {any} defaultValue 默认值
 */
function getStorage(key, defaultValue = null) {
  try {
    const data = wx.getStorageSync(key);
    return data !== '' ? data : defaultValue;
  } catch (e) {
    console.error('获取数据失败:', e);
    return defaultValue;
  }
}

/**
 * 删除本地数据
 * @param {string} key 键名
 */
function removeStorage(key) {
  try {
    wx.removeStorageSync(key);
    return true;
  } catch (e) {
    console.error('删除数据失败:', e);
    return false;
  }
}

module.exports = {
  formatTime,
  formatRelativeTime,
  validateEmail,
  validatePhone,
  validatePassword,
  formatFileSize,
  getFileExtension,
  isImageFile,
  debounce,
  throttle,
  deepClone,
  generateRandomString,
  getStatusText,
  getPriorityText,
  calculateProgress,
  isTaskOverdue,
  setStorage,
  getStorage,
  removeStorage
};
