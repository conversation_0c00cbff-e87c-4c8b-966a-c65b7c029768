import type { NextApiRequest, NextApiResponse } from 'next';
import { AnalyticsService, AnalyticsTimeRange } from '@/lib/analytics';
import { withPermissions } from '@/lib/apiMiddleware';
import { Permission } from '@/lib/permissions';
import { sendSuccess, sendError, handleApiError } from '@/lib/apiMiddleware';
import { getCurrentUserId, isAdmin } from '@/lib/auth';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
  }

  try {
    const userId = await getCurrentUserId(req, res);
    const isUserAdmin = await isAdmin(req, res);
    
    const {
      timeRange = AnalyticsTimeRange.LAST_30_DAYS,
      startDate,
      endDate,
      scope = 'personal'
    } = req.query;

    // 检查权限
    if (scope === 'global' && !isUserAdmin) {
      return sendError(res, '权限不足，无法查看全局任务分析', 'FORBIDDEN', 403);
    }

    // 解析日期范围
    let customStart: Date | undefined;
    let customEnd: Date | undefined;
    
    if (timeRange === AnalyticsTimeRange.CUSTOM) {
      if (startDate && endDate) {
        customStart = new Date(startDate as string);
        customEnd = new Date(endDate as string);
        
        if (isNaN(customStart.getTime()) || isNaN(customEnd.getTime())) {
          return sendError(res, '无效的日期格式', 'INVALID_DATE_FORMAT', 400);
        }
      } else {
        return sendError(res, '自定义时间范围需要提供开始和结束日期', 'MISSING_DATE_RANGE', 400);
      }
    }

    const { startDate: rangeStart, endDate: rangeEnd } = AnalyticsService.getDateRange(
      timeRange as AnalyticsTimeRange,
      customStart,
      customEnd
    );

    // 获取任务分析数据
    const taskAnalytics = await AnalyticsService.getTaskAnalytics(
      rangeStart,
      rangeEnd,
      scope === 'personal' ? userId! : undefined
    );

    const response = {
      timeRange,
      startDate: rangeStart,
      endDate: rangeEnd,
      taskAnalytics
    };

    return sendSuccess(res, response, '任务分析数据获取成功');
  } catch (error) {
    console.error('获取任务分析数据失败:', error);
    return handleApiError(error, req, res);
  }
}

export default withPermissions(Permission.TASK_READ, handler);
