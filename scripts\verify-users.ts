import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyUsers() {
  console.log('🔍 验证测试用户...\n');

  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        age: true,
        department: true,
        position: true,
        bio: true,
        phone: true,
        avatar: true,
        createdAt: true,
      },
      orderBy: {
        role: 'desc', // ADMIN 排在前面
      },
    });

    if (users.length === 0) {
      console.log('❌ 没有找到任何用户');
      return;
    }

    console.log(`✅ 找到 ${users.length} 个用户:\n`);

    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name}`);
      console.log(`   📧 邮箱: ${user.email}`);
      console.log(`   👤 角色: ${user.role}`);
      console.log(`   📊 状态: ${user.status}`);
      console.log(`   🎂 年龄: ${user.age}岁`);
      console.log(`   🏢 部门: ${user.department}`);
      console.log(`   💼 职位: ${user.position}`);
      console.log(`   📱 电话: ${user.phone}`);
      console.log(`   🖼️  头像: ${user.avatar}`);
      console.log(`   📝 简介: ${user.bio}`);
      console.log(`   📅 创建时间: ${user.createdAt.toLocaleString('zh-CN')}`);
      console.log('');
    });

    // 验证项目和任务
    const projects = await prisma.project.findMany({
      include: {
        owner: {
          select: { name: true, email: true },
        },
        members: {
          select: { name: true, email: true },
        },
        tasks: {
          include: {
            assignee: {
              select: { name: true, email: true },
            },
          },
        },
      },
    });

    console.log(`📁 找到 ${projects.length} 个项目:\n`);

    projects.forEach((project, index) => {
      console.log(`${index + 1}. ${project.title}`);
      console.log(`   👑 项目负责人: ${project.owner.name} (${project.owner.email})`);
      console.log(`   👥 项目成员: ${project.members.map(m => m.name).join(', ')}`);
      console.log(`   📊 状态: ${project.status}`);
      console.log(`   📈 进度: ${(project.progress * 100).toFixed(1)}%`);
      console.log(`   📝 描述: ${project.description}`);
      
      if (project.tasks.length > 0) {
        console.log(`   📋 任务 (${project.tasks.length}个):`);
        project.tasks.forEach((task, taskIndex) => {
          console.log(`      ${taskIndex + 1}. ${task.title}`);
          console.log(`         👤 负责人: ${task.assignee?.name || '未分配'}`);
          console.log(`         📊 状态: ${task.status}`);
          console.log(`         🔥 优先级: ${task.priority}`);
          if (task.dueDate) {
            console.log(`         ⏰ 截止时间: ${task.dueDate.toLocaleDateString('zh-CN')}`);
          }
        });
      }
      console.log('');
    });

  } catch (error) {
    console.error('❌ 验证失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyUsers();
