/* 注册页面样式 */

.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx;
}

/* 进度指示器 */
.progress-container {
  margin-bottom: 60rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background-color: white;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 步骤容器 */
.step-container {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #6b7280;
}

/* 表单样式 */
.form-container {
  width: 100%;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  flex: 1;
  padding: 24rpx 60rpx 24rpx 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #F9FAFB;
  box-sizing: border-box;
  transition: all 0.2s;
}

.form-input:focus {
  border-color: #3B82F6;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}

.form-textarea {
  min-height: 120rpx;
  resize: vertical;
  padding: 24rpx;
}

.input-icon {
  position: absolute;
  right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  cursor: pointer;
}

.icon {
  font-size: 32rpx;
  color: #9CA3AF;
}

/* 选择器样式 */
.picker-input {
  padding: 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #F9FAFB;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #374151;
}

.picker-arrow {
  color: #9CA3AF;
  font-size: 24rpx;
}

/* 邀请码提示 */
.invite-tips {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f0f9ff;
  border-radius: 12rpx;
  margin-bottom: 40rpx;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #0369a1;
  line-height: 1.5;
}

.invite-code-input {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 4rpx;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.button-group .btn {
  flex: 1;
}

/* 登录链接 */
.login-link {
  text-align: center;
  margin-top: 40rpx;
  font-size: 26rpx;
  color: #6B7280;
}

.link {
  color: #3B82F6;
  margin-left: 8rpx;
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-container {
    padding: 40rpx 30rpx;
  }
  
  .step-container {
    padding: 40rpx 30rpx;
  }
  
  .title {
    font-size: 32rpx;
  }
  
  .button-group {
    flex-direction: column;
    gap: 15rpx;
  }
}
