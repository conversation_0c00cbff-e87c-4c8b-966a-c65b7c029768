// 环境配置文件
const ENV_CONFIG = {
  // 开发环境
  development: {
    baseUrl: 'http://localhost:3000',
    isDevelopment: true,
    useMockData: true,
    timeout: 10000,
    retryCount: 3,
    enableLog: true
  },
  
  // 测试环境
  testing: {
    baseUrl: 'https://test-api.your-domain.com',
    isDevelopment: true,
    useMockData: false,
    timeout: 15000,
    retryCount: 3,
    enableLog: true
  },
  
  // 生产环境
  production: {
    baseUrl: 'https://api.your-domain.com',
    isDevelopment: false,
    useMockData: false,
    timeout: 10000,
    retryCount: 2,
    enableLog: false
  }
};

// 当前环境 - 修改这里来切换环境
const CURRENT_ENV = 'development'; // development | testing | production

// 获取当前环境配置
function getCurrentConfig() {
  return ENV_CONFIG[CURRENT_ENV] || ENV_CONFIG.development;
}

// 导出配置
module.exports = {
  ENV_CONFIG,
  CURRENT_ENV,
  getCurrentConfig
};
