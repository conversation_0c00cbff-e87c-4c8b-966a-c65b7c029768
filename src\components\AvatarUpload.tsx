import React, { useState, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

interface AvatarUploadProps {
  currentAvatar?: string;
  userName: string;
  onAvatarChange?: (avatarUrl: string) => void;
  size?: 'sm' | 'md' | 'lg';
  editable?: boolean;
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({
  currentAvatar,
  userName,
  onAvatarChange,
  size = 'md',
  editable = true,
}) => {
  const { data: session, update } = useSession();
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState('');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 根据尺寸设置样式
  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  // 获取头像URL
  const getAvatarUrl = () => {
    if (previewUrl) return previewUrl;
    if (currentAvatar) return currentAvatar;
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=6366f1&color=fff&size=128`;
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setError('只支持 JPEG、PNG、GIF、WebP 格式的图片');
      return;
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('头像文件大小不能超过5MB');
      return;
    }

    setError('');

    // 创建预览
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // 上传文件
    uploadAvatar(file);
  };

  // 上传头像
  const uploadAvatar = async (file: File) => {
    setIsUploading(true);
    setError('');

    const formData = new FormData();
    formData.append('avatar', file);

    try {
      const response = await fetch('/api/upload/avatar', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '头像上传失败');
      }

      const data = await response.json();
      console.log('头像上传成功:', data);

      // 通知父组件头像已更改
      if (onAvatarChange) {
        onAvatarChange(data.avatar);
      }

      // 更新NextAuth session
      try {
        await update();
        console.log('Session更新成功');
      } catch (sessionError) {
        console.error('Session更新失败:', sessionError);
        // 如果session更新失败，仍然显示成功，因为文件已经上传成功
        console.log('文件上传成功，但session更新失败，可能需要刷新页面');
      }

      setUploadSuccess(true);
      setPreviewUrl(null); // 清除预览，使用服务器返回的URL

      // 3秒后清除成功提示
      setTimeout(() => {
        setUploadSuccess(false);
      }, 3000);

      // 触发自定义事件通知其他组件头像已更新
      window.dispatchEvent(new CustomEvent('avatarUpdated', {
        detail: { avatar: data.avatar }
      }));
    } catch (error) {
      console.error('头像上传失败:', error);
      setError(error instanceof Error ? error.message : '头像上传失败，请稍后再试');
      setPreviewUrl(null); // 清除预览
    } finally {
      setIsUploading(false);
    }
  };

  // 点击头像选择文件
  const handleAvatarClick = () => {
    if (editable && !isUploading) {
      fileInputRef.current?.click();
    }
  };

  return (
    <div className="flex flex-col items-center">
      {/* 头像显示区域 */}
      <div className="relative">
        <div
          className={`${sizeClasses[size]} rounded-full overflow-hidden border-4 border-white shadow-lg ${
            editable ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''
          } ${isUploading ? 'opacity-50' : ''}`}
          onClick={handleAvatarClick}
        >
          <img
            src={getAvatarUrl()}
            alt={userName}
            className="w-full h-full object-cover"
          />
        </div>

        {/* 上传状态指示器 */}
        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full">
            <svg className={`${iconSizeClasses[size]} text-white animate-spin`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        )}

        {/* 编辑图标 */}
        {editable && !isUploading && (
          <div className="absolute bottom-0 right-0 bg-primary-600 rounded-full p-1 shadow-lg">
            <svg className={iconSizeClasses[size]} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
        )}
      </div>

      {/* 隐藏的文件输入 */}
      {editable && (
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/gif,image/webp"
          onChange={handleFileSelect}
          className="hidden"
        />
      )}

      {/* 提示文本 */}
      {editable && !isUploading && (
        <p className="mt-2 text-xs text-gray-500 dark:text-gray-400 text-center">
          点击头像更换图片
          <br />
          支持 JPEG、PNG、GIF、WebP 格式
          <br />
          文件大小不超过 5MB
        </p>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="mt-2 text-xs text-red-600 dark:text-red-400 text-center">
          {error}
        </div>
      )}

      {/* 上传成功提示 */}
      {uploadSuccess && (
        <div className="mt-2 text-xs text-green-600 dark:text-green-400 text-center">
          ✅ 头像更新成功！
        </div>
      )}
    </div>
  );
};

export default AvatarUpload;
