/* 通知页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.notification-list {
  margin-bottom: 30rpx;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  position: relative;
}

.notification-item:active {
  transform: scale(0.98);
}

.notification-item.unread {
  border-left: 6rpx solid #3b82f6;
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ef4444;
  border-radius: 50%;
}

.notification-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 40rpx;
  flex-shrink: 0;
}

.notification-icon.system {
  background: #dbeafe;
  color: #3b82f6;
}

.notification-icon.task {
  background: #d1fae5;
  color: #10b981;
}

.notification-icon.project {
  background: #fef3c7;
  color: #d97706;
}

.notification-icon.message {
  background: #f3e8ff;
  color: #8b5cf6;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-desc {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.notification-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.notification-actions {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-left: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  transition: background-color 0.2s;
}

.read-btn {
  background: #dbeafe;
  color: #3b82f6;
}

.delete-btn {
  background: #fee2e2;
  color: #ef4444;
}

.filter-tabs {
  display: flex;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.filter-tab {
  flex: 1;
  padding: 25rpx 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #6b7280;
  transition: all 0.2s;
}

.filter-tab.active {
  color: #3b82f6;
  background: #f8fafc;
}

.loading {
  text-align: center;
  padding: 60rpx;
  color: #6b7280;
  font-size: 28rpx;
}

.empty {
  text-align: center;
  padding: 120rpx 60rpx;
  color: #9ca3af;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #d1d5db;
}

.mark-all-read {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  padding: 20rpx 30rpx;
  background: #3b82f6;
  color: white;
  border-radius: 25rpx;
  font-size: 26rpx;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
  z-index: 100;
}
