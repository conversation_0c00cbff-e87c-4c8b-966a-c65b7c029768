/* 聊天详情页面样式 */

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.message-list {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.message-item {
  margin-bottom: 30rpx;
  display: flex;
  align-items: flex-end;
}

.message-item.own {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 20rpx;
}

.message-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-content {
  max-width: 60%;
  display: flex;
  flex-direction: column;
}

.message-item.own .message-content {
  align-items: flex-end;
}

.message-bubble {
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  line-height: 1.4;
  word-wrap: break-word;
}

.message-item:not(.own) .message-bubble {
  background: white;
  color: #1f2937;
  border-bottom-left-radius: 8rpx;
}

.message-item.own .message-bubble {
  background: #3b82f6;
  color: white;
  border-bottom-right-radius: 8rpx;
}

.message-time {
  font-size: 22rpx;
  color: #9ca3af;
  margin-top: 8rpx;
  padding: 0 8rpx;
}

.input-area {
  background: white;
  padding: 20rpx;
  border-top: 1rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.message-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 25rpx;
  font-size: 28rpx;
  background: #f9fafb;
  max-height: 120rpx;
}

.send-btn {
  width: 80rpx;
  height: 80rpx;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
}

.send-btn.disabled {
  background: #d1d5db;
}

.loading {
  text-align: center;
  padding: 60rpx;
  color: #6b7280;
  font-size: 28rpx;
}
