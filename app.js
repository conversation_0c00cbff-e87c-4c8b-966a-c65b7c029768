// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'https://your-api-domain.com',
    version: '1.0.0'
  },

  onLaunch() {
    console.log('LabSync 小程序启动')
    this.checkLoginStatus()
    this.getSystemInfo()
  },

  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    
    if (token && userInfo) {
      this.globalData.token = token
      this.globalData.userInfo = userInfo
    }
  },

  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        this.globalData.statusBarHeight = res.statusBarHeight
        this.globalData.navBarHeight = res.statusBarHeight + 44
      }
    })
  },

  login(userInfo) {
    this.globalData.userInfo = userInfo
    this.globalData.token = userInfo.token
    wx.setStorageSync('userInfo', userInfo)
    wx.setStorageSync('token', userInfo.token)
  },

  logout() {
    this.globalData.userInfo = null
    this.globalData.token = null
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  isLoggedIn() {
    return !!(this.globalData.token && this.globalData.userInfo)
  }
})