{"name": "labsync-wechat", "version": "1.0.0", "description": "LabSync 实验室管理系统 - 微信小程序版", "main": "miniprogram/app.js", "scripts": {"dev": "echo '请使用微信开发者工具打开项目进行开发'", "build": "echo '请使用微信开发者工具进行构建和上传'", "lint": "echo 'ESLint 检查功能待添加'", "test": "echo 'No tests specified'"}, "keywords": ["labsync", "实验室管理", "微信小程序", "项目管理", "任务管理", "团队协作"], "author": "LabSync Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/LabSync-WeChat.git"}, "bugs": {"url": "https://github.com/your-username/LabSync-WeChat/issues"}, "homepage": "https://github.com/your-username/LabSync-WeChat#readme", "devDependencies": {}, "dependencies": {}, "miniprogram": {"libVersion": "2.19.4", "compileType": "miniprogram"}}