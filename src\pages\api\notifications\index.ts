import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await isAuthenticated(req, res);
  } catch (error) {
    return;
  }

  const userId = await getCurrentUserId(req, res);

  if (req.method === 'GET') {
    try {
      // 从系统通知聊天中获取通知消息
      const systemChat = await prisma.chat.findFirst({
        where: {
          type: 'SYSTEM',
          participants: {
            some: {
              id: userId,
            },
          },
        },
        include: {
          messages: {
            where: {
              isSystem: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 50,
          },
        },
      });

      const notifications = systemChat?.messages.map(message => ({
        id: message.id,
        title: extractTitle(message.content),
        message: message.content,
        type: getNotificationType(message.content),
        read: message.isRead || false,
        createdAt: message.createdAt.toISOString(),
      })) || [];

      return res.status(200).json({
        notifications,
        unreadCount: notifications.filter(n => !n.read).length,
      });
    } catch (error) {
      console.error('获取通知失败:', error);
      return res.status(500).json({ message: '获取通知失败' });
    }
  }

  return res.status(405).json({ message: '方法不允许' });
}

function extractTitle(content: string): string {
  // 从通知内容中提取标题
  const lines = content.split('\n');
  const firstLine = lines[0];

  // 移除表情符号，获取主要内容
  const title = firstLine.replace(/^[^\w\s]+\s*/, '').trim();

  // 如果标题太长，截断它
  return title.length > 50 ? title.substring(0, 50) + '...' : title;
}

function getNotificationType(content: string): 'info' | 'success' | 'warning' | 'error' {
  if (content.includes('🎉') || content.includes('✅') || content.includes('🏆')) {
    return 'success';
  }
  if (content.includes('❌') || content.includes('拒绝')) {
    return 'error';
  }
  if (content.includes('⚠️') || content.includes('⏰') || content.includes('提醒')) {
    return 'warning';
  }
  return 'info';
}
