import { NextApiRequest, NextApiResponse } from 'next';
import { getCurrentUserId } from '@/lib/auth';
import prisma from '@/lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    // 验证用户身份
    const currentUserId = await getCurrentUserId(req, res);
    if (!currentUserId) {
      return res.status(401).json({ message: '用户未认证' });
    }

    const { id: chatId } = req.query;

    if (!chatId || typeof chatId !== 'string') {
      return res.status(400).json({ message: '聊天ID无效' });
    }

    // 验证用户是否是聊天参与者
    const chat = await prisma.chat.findFirst({
      where: {
        id: chatId,
        participants: {
          some: {
            id: currentUserId,
          },
        },
      },
    });

    if (!chat) {
      return res.status(404).json({ message: '聊天不存在或无权限访问' });
    }

    // 获取聊天中用户未读的消息
    const unreadMessages = await prisma.chatMessage.findMany({
      where: {
        chatId,
        senderId: {
          not: currentUserId, // 不包括自己发送的消息
        },
        readBy: {
          none: {
            userId: currentUserId,
          },
        },
      },
      select: {
        id: true,
      },
    });

    // 批量标记消息为已读
    const messageReads = unreadMessages.map(message => ({
      userId: currentUserId,
      messageId: message.id,
    }));

    if (messageReads.length > 0) {
      // 逐个插入，避免重复
      for (const messageRead of messageReads) {
        try {
          await prisma.messageRead.create({
            data: messageRead,
          });
        } catch (error) {
          // 忽略重复插入错误
          console.log('消息已读记录已存在，跳过:', messageRead);
        }
      }
    }

    return res.status(200).json({
      message: '消息已标记为已读',
      markedCount: messageReads.length
    });
  } catch (error) {
    console.error('标记消息已读失败:', error);
    return res.status(500).json({ message: '服务器内部错误' });
  }
}
