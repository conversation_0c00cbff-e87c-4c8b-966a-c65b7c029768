# 系统通知功能使用指南

## 🔔 系统概述

LabSync 现在拥有完整的消息通知系统，能够在关键操作发生时自动向相关用户发送通知消息。所有通知都通过系统聊天的形式发送，用户可以在聊天页面查看。

## 📋 通知类型

### 1. 用户审核通知

**触发时机**: 管理员审核用户注册申请时

**通知对象**: 被审核的用户

**通知内容**:
- ✅ **审核通过**: "🎉 您的账号已通过审核！审核人：[管理员姓名]，您现在可以正常使用系统的所有功能了。"
- ❌ **审核拒绝**: "❌ 您的账号审核未通过，审核人：[管理员姓名]，拒绝原因：[原因]，如有疑问，请联系管理员。"

### 2. 任务相关通知

**触发时机**: 任务分配、任务完成时

**通知对象**: 任务相关人员

**通知内容**:
- 📋 **任务分配**: "📋 您被分配了新任务：「[任务标题]」，项目：[项目名称]，分配人：[分配人姓名]"
- ✅ **任务完成**: "✅ 任务已完成：「[任务标题]」，项目：[项目名称]，完成人：[完成人姓名]"

### 3. 项目状态通知

**触发时机**: 项目状态发生变更时

**通知对象**: 项目所有成员

**通知内容**:
- 📊 **状态变更**: "📊 项目状态已更新：「[项目标题]」，新状态：[新状态]，更新人：[更新人姓名]"
- 🏆 **项目结题**: "🏆 项目已结题：「[项目标题]」，结题人：[结题人姓名]，感谢您的参与和贡献！"
- 📦 **项目归档**: "📦 项目已归档：「[项目标题]」，归档人：[归档人姓名]，项目相关资料已保存至归档区。"

### 4. 项目进度通知

**触发时机**: 项目进度达到重要节点时（25%、50%、75%、100%）

**通知对象**: 项目所有成员

**通知内容**:
- 🚀 **25%**: "🎯 项目进度更新：「[项目标题]」，🚀 项目进度达到 25%，开局良好！，当前进度：25%"
- ⚡ **50%**: "🎯 项目进度更新：「[项目标题]」，⚡ 项目进度过半，继续加油！，当前进度：50%"
- 🎯 **75%**: "🎯 项目进度更新：「[项目标题]」，🎯 项目进度达到 75%，即将完成！，当前进度：75%"
- 🎉 **100%**: "🎯 项目进度更新：「[项目标题]」，🎉 恭喜！项目所有任务已完成！，当前进度：100%"

### 5. 项目成员通知

**触发时机**: 项目成员发生变更时

**通知对象**: 相关成员

**通知内容**:
- 👥 **成员添加**: "👥 您被添加到项目：「[项目标题]」，添加人：[添加人姓名]"
- 👋 **成员移除**: "👋 您已被移出项目：「[项目标题]」，操作人：[操作人姓名]"

### 6. 文件上传通知

**触发时机**: 项目中有新文件上传时

**通知对象**: 项目所有成员（除上传者）

**通知内容**:
- 📎 **文件上传**: "📎 新文件上传：「[文件名]」，项目：[项目标题]，上传人：[上传人姓名]"

### 7. 截止日期提醒

**触发时机**: 任务或项目临近截止日期时

**通知对象**: 相关负责人

**通知内容**:
- ⏰ **任务提醒**: "⏰ 任务截止日期提醒：「[任务标题]」，项目：[项目标题]，剩余时间：[天数] 天"
- ⏰ **项目提醒**: "⏰ 项目截止日期提醒：「[项目标题]」，剩余时间：[天数] 天"

## 🔧 技术实现

### 通知发送机制

1. **系统聊天**: 每个用户都有一个专门的"系统通知"聊天，用于接收所有系统通知
2. **自动创建**: 首次发送通知时，系统会自动为用户创建系统通知聊天
3. **消息类型**: 所有通知消息都标记为系统消息（`isSystem: true`）
4. **错误处理**: 通知发送失败不会影响主要业务逻辑的执行

### 核心文件

- `src/lib/systemNotifications.ts`: 系统通知核心功能
- `src/pages/api/admin/users/[id]/status.ts`: 用户审核通知
- `src/pages/api/projects/[id].ts`: 项目相关通知
- `src/pages/api/tasks/[id].ts`: 任务相关通知
- `src/pages/api/files/index.ts`: 文件上传通知
- `src/lib/project-utils.ts`: 项目进度通知

## 📱 用户体验

### 查看通知

1. 用户登录后，在聊天页面可以看到"系统通知"聊天
2. 点击进入可查看所有历史通知消息
3. 通知消息按时间倒序排列，最新的在最下方

### 通知标识

- 🎉 审核通过
- ❌ 审核拒绝
- 📋 任务分配
- ✅ 任务完成
- 📊 项目状态变更
- 🏆 项目结题
- 📦 项目归档
- 🎯 项目进度
- 👥 成员添加
- 👋 成员移除
- 📎 文件上传
- ⏰ 截止日期提醒

## 🧪 测试功能

运行测试脚本验证通知功能：

```bash
node test-notifications.js
```

测试脚本会：
1. 创建各种类型的测试通知
2. 验证通知是否正确发送
3. 显示通知统计信息

## 🔒 安全特性

1. **权限控制**: 只有相关用户才能收到对应的通知
2. **错误隔离**: 通知发送失败不影响主要功能
3. **数据完整性**: 通知消息与业务数据保持一致
4. **隐私保护**: 用户只能看到与自己相关的通知

## 📞 技术支持

如果遇到通知相关问题：
1. 检查系统日志中的通知发送记录
2. 确认用户的系统通知聊天是否正常创建
3. 验证相关API接口的通知发送逻辑

---

**注意**: 系统通知功能已完全集成到现有业务流程中，无需额外配置即可使用。
