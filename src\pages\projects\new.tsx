import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import UserSelector from '../../components/UserSelector';

type FormData = {
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  members: string[];
};

export default function NewProject() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [selectedLeader, setSelectedLeader] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>();

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取用户列表（用于选择项目成员）
  useEffect(() => {
    if (status === 'authenticated') {
      fetchUsers();
    }
  }, [status]);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users');

      if (!response.ok) {
        throw new Error('获取用户列表失败');
      }

      const data = await response.json();
      setUsers(data);
    } catch (error) {
      console.error('获取用户列表失败:', error);
    }
  };

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    setError('');

    // 验证项目负责人是否已选择
    if (!selectedLeader) {
      setError('请选择项目负责人');
      setLoading(false);
      return;
    }

    try {
      const projectData = {
        ...data,
        members: selectedMembers,
        leaderId: selectedLeader,
      };

      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(projectData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '创建项目失败');
      }

      const project = await response.json();

      // 创建成功后跳转到项目详情页
      router.push(`/projects/${project.id}`);
    } catch (error) {
      console.error('创建项目失败:', error);
      setError(error instanceof Error ? error.message : '创建项目失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          创建新项目
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          填写以下信息创建一个新项目
        </p>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <label className="form-label" htmlFor="title">
              项目标题 <span className="text-red-500">*</span>
            </label>
            <input
              id="title"
              type="text"
              className="form-input"
              placeholder="输入项目标题"
              {...register('title', {
                required: '请输入项目标题',
                minLength: {
                  value: 2,
                  message: '项目标题至少需要2个字符',
                },
              })}
            />
            {errors.title && (
              <p className="form-error">{errors.title.message}</p>
            )}
          </div>

          <div className="mb-4">
            <label className="form-label" htmlFor="description">
              项目描述
            </label>
            <textarea
              id="description"
              className="form-input"
              rows={4}
              placeholder="输入项目描述"
              {...register('description')}
            ></textarea>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="form-label" htmlFor="startDate">
                开始日期 <span className="text-red-500">*</span>
              </label>
              <input
                id="startDate"
                type="date"
                className="form-input"
                {...register('startDate', {
                  required: '请选择开始日期',
                })}
              />
              {errors.startDate && (
                <p className="form-error">{errors.startDate.message}</p>
              )}
            </div>

            <div>
              <label className="form-label" htmlFor="endDate">
                截止日期
              </label>
              <input
                id="endDate"
                type="date"
                className="form-input"
                {...register('endDate')}
              />
            </div>
          </div>

          <div className="mb-6">
            <label className="form-label" htmlFor="leader">
              项目负责人 <span className="text-red-500">*</span>
            </label>
            <UserSelector
              selectedUsers={selectedLeader ? [selectedLeader] : []}
              onSelectionChange={(users) => setSelectedLeader(users[0] || '')}
              placeholder="选择项目负责人"
              multiple={false}
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              项目负责人将负责项目的整体管理和协调
            </p>
          </div>

          <div className="mb-6">
            <label className="form-label" htmlFor="members">
              项目成员
            </label>
            <UserSelector
              selectedUsers={selectedMembers}
              onSelectionChange={setSelectedMembers}
              placeholder="选择项目成员"
              multiple={true}
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              可以搜索用户姓名、邮箱或部门来快速找到需要的成员
            </p>
          </div>

          <div className="flex justify-end space-x-4">
            <Link
              href="/projects"
              className="btn btn-secondary"
            >
              取消
            </Link>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  创建中...
                </>
              ) : '创建项目'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
