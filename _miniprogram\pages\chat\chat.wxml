<!--聊天列表页面-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <input
        class="search-input"
        placeholder="搜索聊天"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
      />
      <view class="search-icon">🔍</view>
    </view>
    <view class="create-btn" bindtap="createChat">
      <text class="create-icon">+</text>
    </view>
  </view>

  <!-- 加载中 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 聊天列表 -->
  <view wx:else class="chat-list">
    <!-- 空状态 -->
    <view wx:if="{{chats.length === 0}}" class="empty">
      <view class="empty-icon">💬</view>
      <view class="empty-text">暂无聊天记录</view>
      <view class="empty-desc">点击右上角开始新的对话</view>
    </view>

    <!-- 聊天项 -->
    <view wx:else>
      <view
        wx:for="{{chats}}"
        wx:key="id"
        class="chat-item"
        bindtap="goToChatDetail"
        data-id="{{item.id}}"
      >
        <view class="chat-avatar">
          <image
            wx:if="{{item.avatar}}"
            src="{{item.avatar}}"
            class="avatar-image"
          />
          <view wx:else class="avatar-placeholder">
            <text class="avatar-text">{{getChatTypeIcon(item.type)}}</text>
          </view>
          <view wx:if="{{item.unreadCount > 0}}" class="unread-badge">
            <text class="unread-count">{{item.unreadCount > 99 ? '99+' : item.unreadCount}}</text>
          </view>
        </view>

        <view class="chat-content">
          <view class="chat-header">
            <view class="chat-name">{{item.name}}</view>
            <view class="chat-time">{{formatTime(item.lastMessage.sendTime)}}</view>
          </view>
          <view class="chat-message">
            <text class="message-sender" wx:if="{{item.type === 'GROUP'}}">
              {{item.lastMessage.sender.name}}:
            </text>
            <text class="message-content">{{item.lastMessage.content}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>