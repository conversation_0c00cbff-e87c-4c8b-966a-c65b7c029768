import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';

export default function FileUpload() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { projectId, taskId } = router.query;

  const [file, setFile] = useState<File | null>(null);
  const [description, setDescription] = useState('');
  const [projects, setProjects] = useState<any[]>([]);
  const [tasks, setTasks] = useState<any[]>([]);
  const [selectedProject, setSelectedProject] = useState(projectId || '');
  const [selectedTask, setSelectedTask] = useState(taskId || '');
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState('');
  const fileInputRef = useRef(null);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取项目列表
  useEffect(() => {
    if (status === 'authenticated') {
      fetchProjects();
    }
  }, [status]);

  // 当选择项目时，获取该项目的任务列表
  useEffect(() => {
    if (selectedProject && typeof selectedProject === 'string') {
      fetchTasks(selectedProject);
    } else {
      setTasks([]);
      setSelectedTask('');
    }
  }, [selectedProject]);

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects');

      if (!response.ok) {
        throw new Error('获取项目列表失败');
      }

      const data = await response.json();
      setProjects(data);

      // 如果URL中有projectId，则设置为选中项目
      if (projectId && data.some((p: any) => p.id === projectId)) {
        setSelectedProject(projectId);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      setError('获取项目列表失败，请稍后再试');
    }
  };

  const fetchTasks = async (projectId: string) => {
    try {
      const response = await fetch(`/api/tasks?projectId=${projectId}`);

      if (!response.ok) {
        throw new Error('获取任务列表失败');
      }

      const data = await response.json();
      setTasks(data);

      // 如果URL中有taskId，则设置为选中任务
      if (taskId && data.some((t: any) => t.id === taskId)) {
        setSelectedTask(taskId);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setError('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError('请选择要上传的文件');
      return;
    }

    setIsUploading(true);
    setProgress(0);
    setError('');

    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);
    formData.append('description', description);

    if (selectedProject && typeof selectedProject === 'string') {
      formData.append('projectId', selectedProject);
    }

    if (selectedTask && typeof selectedTask === 'string') {
      formData.append('taskId', selectedTask);
    }

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + 5;
          if (newProgress >= 95) {
            clearInterval(progressInterval);
            return 95;
          }
          return newProgress;
        });
      }, 100);

      // 发送上传请求
      const response = await fetch('/api/files', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '文件上传失败');
      }

      setProgress(100);

      // 上传成功后跳转到文件列表页
      setTimeout(() => {
        router.push('/files');
      }, 1000);
    } catch (error) {
      console.error('文件上传失败:', error);
      let errorMessage = '文件上传失败，请稍后再试';

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
      setIsUploading(false);
      setProgress(0);
    }
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          上传文件
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          上传文件并关联到项目或任务
        </p>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="form-label" htmlFor="file">
              选择文件 <span className="text-red-500">*</span>
            </label>
            <input
              id="file"
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="block w-full text-sm text-gray-500 dark:text-gray-400
                        file:mr-4 file:py-2 file:px-4
                        file:rounded-md file:border-0
                        file:text-sm file:font-semibold
                        file:bg-primary-50 file:text-primary-700
                        dark:file:bg-primary-900 dark:file:text-primary-200
                        hover:file:bg-primary-100 dark:hover:file:bg-primary-800"
              disabled={isUploading}
            />
            {file && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                已选择: {file.name} ({(file.size / 1024).toFixed(2)} KB)
              </p>
            )}
          </div>

          <div className="mb-4">
            <label className="form-label" htmlFor="description">
              文件描述 (可选)
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="form-input"
              rows={3}
              placeholder="请输入文件描述..."
              disabled={isUploading}
            ></textarea>
          </div>

          <div className="mb-4">
            <label className="form-label" htmlFor="project">
              关联项目 (可选)
            </label>
            <select
              id="project"
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="form-input"
              disabled={isUploading}
            >
              <option value="">不关联项目</option>
              {projects.map(project => (
                <option key={project.id} value={project.id}>
                  {project.title}
                </option>
              ))}
            </select>
          </div>

          {selectedProject && tasks.length > 0 && (
            <div className="mb-4">
              <label className="form-label" htmlFor="task">
                关联任务 (可选)
              </label>
              <select
                id="task"
                value={selectedTask}
                onChange={(e) => setSelectedTask(e.target.value)}
                className="form-input"
                disabled={isUploading}
              >
                <option value="">不关联任务</option>
                {tasks.map(task => (
                  <option key={task.id} value={task.id}>
                    {task.title}
                  </option>
                ))}
              </select>
            </div>
          )}

          {isUploading && (
            <div className="mb-4">
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                <span>上传进度</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-primary-500"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-4">
            <Link
              href="/files"
              className="btn btn-secondary"
            >
              取消
            </Link>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isUploading || !file}
            >
              {isUploading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  上传中...
                </>
              ) : '上传文件'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
