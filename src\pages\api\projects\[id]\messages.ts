import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';
import { isProjectMember } from '@/lib/permissions';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const { id: projectId } = req.query;
  const userId = await getCurrentUserId(req, res);

  if (typeof projectId !== 'string') {
    return res.status(400).json({ message: '无效的项目ID' });
  }

  // 检查用户是否为项目成员
  const canAccess = await isProjectMember(req, res, projectId);
  if (!canAccess) {
    return res.status(403).json({ message: '没有权限访问该项目聊天' });
  }

  // 处理GET请求 - 获取项目消息
  if (req.method === 'GET') {
    try {
      const { page = '1', limit = '50' } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      const messages = await prisma.message.findMany({
        where: {
          projectId,
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limitNum,
      });

      // 获取总消息数
      const totalMessages = await prisma.message.count({
        where: {
          projectId,
        },
      });

      return res.status(200).json({
        messages: messages.reverse(), // 反转顺序，最新消息在底部
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: totalMessages,
          totalPages: Math.ceil(totalMessages / limitNum),
        },
      });
    } catch (error) {
      console.error('获取项目消息失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理POST请求 - 发送消息
  if (req.method === 'POST') {
    try {
      const { content, type = 'TEXT', fileName, fileUrl, fileSize } = req.body;

      // 验证消息内容
      if (type === 'FILE') {
        // 文件消息验证
        if (!fileName || !fileUrl) {
          return res.status(400).json({ message: '文件消息缺少必要信息' });
        }
      } else {
        // 文本消息验证
        if (!content || content.trim().length === 0) {
          return res.status(400).json({ message: '消息内容不能为空' });
        }

        if (content.length > 1000) {
          return res.status(400).json({ message: '消息内容不能超过1000个字符' });
        }
      }

      const message = await prisma.message.create({
        data: {
          content: type === 'FILE' ? fileName : content.trim(),
          type,
          ...(type === 'FILE' && {
            fileName,
            fileUrl,
            fileSize,
          }),
          senderId: userId!,
          projectId,
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
      });

      return res.status(201).json(message);
    } catch (error) {
      console.error('发送消息失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
