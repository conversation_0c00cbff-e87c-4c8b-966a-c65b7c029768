import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { format } from 'date-fns';
import { getTaskStatusText, getTaskStatusBadgeClass, getProjectStatusText, getProjectStatusBadgeClass, getUserRoleText, getUserRoleBadgeClass } from '@/utils/statusUtils';

// 用户类型定义
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
  ownedProjects?: Array<{
    id: string;
    title: string;
    status: string;
  }>;
  memberProjects?: Array<{
    id: string;
    title: string;
    status: string;
  }>;
  assignedTasks?: Array<{
    id: string;
    title: string;
    status: string;
    project?: {
      id: string;
      title: string;
    };
  }>;
}

export default function UserDetail() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { id } = router.query;

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 如果未登录或不是管理员，重定向到首页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    } else if (status === 'authenticated' && session?.user?.role !== 'ADMIN') {
      router.push('/');
    }
  }, [status, session, router]);

  // 获取用户详情
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role === 'ADMIN' && id) {
      fetchUserDetails();
    }
  }, [status, session, id]);

  const fetchUserDetails = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/users/${id}`);

      if (!response.ok) {
        throw new Error('获取用户详情失败');
      }

      const data = await response.json();
      setUser(data);
    } catch (error) {
      console.error('获取用户详情失败:', error);
      setError('获取用户详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 格式化日期
  const formatDate = (date: string | Date | null | undefined) => {
    if (!date) return '';
    return format(new Date(date), 'yyyy-MM-dd HH:mm:ss');
  };

  // 获取角色显示文本
  const getRoleText = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return '管理员';
      case 'LEADER':
        return '项目负责人';
      case 'MEMBER':
        return '项目成员';
      case 'GUEST':
        return '访客';
      default:
        return role;
    }
  };

  // 获取角色标签样式
  const getRoleBadgeClass = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'LEADER':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'MEMBER':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'GUEST':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated' || (status === 'authenticated' && session?.user?.role !== 'ADMIN')) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 加载中
  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="mt-4">
          <Link href="/admin/users" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回用户列表
          </Link>
        </div>
      </div>
    );
  }

  // 用户不存在
  if (!user) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">用户不存在或您没有权限访问</span>
        </div>
        <div className="mt-4">
          <Link href="/admin/users" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回用户列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* 用户标题和操作按钮 */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mr-3">
              {user.name}
            </h1>
            <span className={`px-2 py-1 text-xs rounded-full ${getRoleBadgeClass(user.role)}`}>
              {getRoleText(user.role)}
            </span>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {user.email}
          </p>
        </div>

        <div className="mt-4 md:mt-0 flex space-x-2">
          <Link href={`/admin/users/${id}/edit`} className="btn btn-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            编辑
          </Link>
          {user.id !== session?.user?.id && (
            <Link href={`/admin/users?delete=${user.id}`} className="btn btn-error">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              删除
            </Link>
          )}
        </div>
      </div>

      {/* 用户详情 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-4">用户信息</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">注册时间</h3>
            <p className="mt-1">{formatDate(user.createdAt)}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">最后更新</h3>
            <p className="mt-1">{formatDate(user.updatedAt)}</p>
          </div>
        </div>

        {/* 用户项目 */}
        {user.ownedProjects && user.ownedProjects.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">负责的项目</h3>
            <ul className="list-disc list-inside space-y-1">
              {user.ownedProjects.map(project => (
                <li key={project.id}>
                  <Link href={`/projects/${project.id}`} className="text-primary-600 dark:text-primary-400 hover:underline">
                    {project.title}
                  </Link>
                  <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                    ({project.status})
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 参与的项目 */}
        {user.memberProjects && user.memberProjects.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">参与的项目</h3>
            <ul className="list-disc list-inside space-y-1">
              {user.memberProjects.map(project => (
                <li key={project.id}>
                  <Link href={`/projects/${project.id}`} className="text-primary-600 dark:text-primary-400 hover:underline">
                    {project.title}
                  </Link>
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getProjectStatusBadgeClass(project.status)}`}>
                    {getProjectStatusText(project.status)}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 分配的任务 */}
        {user.assignedTasks && user.assignedTasks.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">分配的任务</h3>
            <ul className="list-disc list-inside space-y-1">
              {user.assignedTasks.map(task => (
                <li key={task.id}>
                  <Link href={`/tasks/${task.id}`} className="text-primary-600 dark:text-primary-400 hover:underline">
                    {task.title}
                  </Link>
                  <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getTaskStatusBadgeClass(task.status)}`}>
                    {getTaskStatusText(task.status)}
                  </span>
                  {task.project && (
                    <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                      - 项目:
                      <Link href={`/projects/${task.project.id}`} className="text-primary-600 dark:text-primary-400 hover:underline ml-1">
                        {task.project.title}
                      </Link>
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
