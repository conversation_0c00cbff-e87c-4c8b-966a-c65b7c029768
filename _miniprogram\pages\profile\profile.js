// 个人中心页面
const { userApi } = require('../../utils/api.js');
const app = getApp();

Page({
  data: {
    userInfo: {},
    loading: true,
    stats: {
      projectCount: 0,
      taskCount: 0,
      completedTasks: 0,
      messageCount: 0
    },
    menuItems: [
      {
        id: 'notifications',
        title: '消息通知',
        icon: '🔔',
        badge: 0,
        url: '/pages/notifications/notifications'
      },
      {
        id: 'files',
        title: '我的文件',
        icon: '📁',
        badge: 0,
        url: '/pages/files/files'
      },
      {
        id: 'team',
        title: '团队管理',
        icon: '👥',
        badge: 0,
        url: '/pages/team/team'
      },
      {
        id: 'settings',
        title: '设置',
        icon: '⚙️',
        badge: 0,
        url: '/pages/settings/settings'
      }
    ]
  },

  onLoad() {
    this.checkLogin();
  },

  onShow() {
    if (app.isLoggedIn()) {
      this.loadUserInfo();
      this.loadUserStats();
    }
  },

  // 检查登录状态
  checkLogin() {
    if (!app.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    this.loadUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    this.setData({ loading: true });

    try {
      // 尝试从API获取最新用户信息
      if (!app.globalData.useMockData) {
        const response = await userApi.getUserInfo();
        if (response.success && response.data) {
          app.setUserInfo(response.data, app.globalData.token);
        }
      }

      // 使用当前用户信息
      this.setData({
        userInfo: app.globalData.userInfo || this.getMockUserInfo(),
        loading: false
      });

    } catch (error) {
      console.error('加载用户信息失败:', error);

      // 使用本地存储的用户信息或模拟数据
      this.setData({
        userInfo: app.globalData.userInfo || this.getMockUserInfo(),
        loading: false
      });
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      // 这里可以调用用户统计API
      // const response = await userApi.getUserStats();

      // 使用模拟数据
      this.setData({
        stats: {
          projectCount: 5,
          taskCount: 12,
          completedTasks: 8,
          messageCount: 23
        }
      });

    } catch (error) {
      console.error('加载用户统计失败:', error);
    }
  },

  // 获取模拟用户信息
  getMockUserInfo() {
    return {
      id: '1',
      name: '测试用户',
      email: '<EMAIL>',
      role: 'MEMBER',
      department: '研发部',
      avatar: '',
      joinDate: '2024-01-01'
    };
  },

  // 编辑个人信息
  editProfile() {
    wx.navigateTo({
      url: '/pages/profile-edit/profile-edit'
    });
  },

  // 上传头像
  uploadAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.handleAvatarUpload(tempFilePath);
      }
    });
  },

  // 处理头像上传
  async handleAvatarUpload(filePath) {
    wx.showLoading({ title: '上传中...' });

    try {
      if (!app.globalData.useMockData) {
        const response = await userApi.uploadAvatar(filePath);
        if (response.success && response.data) {
          // 更新用户信息
          const updatedUserInfo = {
            ...this.data.userInfo,
            avatar: response.data.url
          };
          app.setUserInfo(updatedUserInfo, app.globalData.token);
          this.setData({ userInfo: updatedUserInfo });

          wx.showToast({
            title: '头像更新成功',
            icon: 'success'
          });
          return;
        }
      }

      // 模拟上传成功
      const updatedUserInfo = {
        ...this.data.userInfo,
        avatar: filePath
      };
      this.setData({ userInfo: updatedUserInfo });

      wx.showToast({
        title: '头像更新成功（模拟）',
        icon: 'success'
      });

    } catch (error) {
      console.error('头像上传失败:', error);
      wx.showToast({
        title: '头像上传失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 菜单项点击
  onMenuItemTap(e) {
    const item = e.currentTarget.dataset.item;

    if (item.id === 'settings') {
      this.showSettings();
    } else if (item.url) {
      wx.navigateTo({
        url: item.url
      });
    }
  },

  // 显示设置菜单
  showSettings() {
    wx.showActionSheet({
      itemList: ['修改密码', '清除缓存', '关于我们', '退出登录'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.changePassword();
            break;
          case 1:
            this.clearCache();
            break;
          case 2:
            this.showAbout();
            break;
          case 3:
            this.logout();
            break;
        }
      }
    });
  },

  // 修改密码
  changePassword() {
    wx.showModal({
      title: '修改密码',
      content: '此功能需要在专门页面实现',
      showCancel: false
    });
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除本地缓存吗？',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorageSync();
          wx.showToast({
            title: '缓存已清除',
            icon: 'success'
          });
        }
      }
    });
  },

  // 关于我们
  showAbout() {
    wx.showModal({
      title: 'LabSync',
      content: '版本: 1.0.0\n现代化的实验室管理系统',
      showCancel: false
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearUserInfo();
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadUserInfo();
    this.loadUserStats();
    wx.stopPullDownRefresh();
  }
});