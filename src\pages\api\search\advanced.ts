import type { NextApiRequest, NextApiResponse } from 'next';
import { AdvancedSearchService, SearchType, SearchFilters } from '@/lib/search';
import { AdvancedPermissions } from '@/lib/permissions';
import { requireAuth, sendSuccess, sendError, handleApiError } from '@/lib/apiMiddleware';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查认证
  try {
    await requireAuth(req, res, async () => {});
  } catch (error) {
    return;
  }

  if (req.method !== 'GET') {
    return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
  }

  try {
    // 获取用户权限
    const permissions = await AdvancedPermissions.fromRequest(req, res);
    if (!permissions) {
      return sendError(res, '权限验证失败', 'PERMISSION_VERIFICATION_FAILED', 401);
    }

    // 解析查询参数
    const {
      q: query,
      type = SearchType.ALL,
      status,
      priority,
      assigneeIds,
      projectIds,
      startDate,
      endDate,
      tags,
      fileTypes,
      limit = 20,
      offset = 0
    } = req.query;

    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return sendError(res, '请提供搜索关键词', 'QUERY_REQUIRED', 400);
    }

    // 构建搜索过滤器
    const filters: SearchFilters = {
      type: type as SearchType,
    };

    // 添加状态过滤
    if (status) {
      filters.status = Array.isArray(status) ? status as string[] : [status as string];
    }

    // 添加优先级过滤
    if (priority) {
      filters.priority = Array.isArray(priority) ? priority as string[] : [priority as string];
    }

    // 添加负责人过滤
    if (assigneeIds) {
      filters.assigneeIds = Array.isArray(assigneeIds) ? assigneeIds as string[] : [assigneeIds as string];
    }

    // 添加项目过滤
    if (projectIds) {
      filters.projectIds = Array.isArray(projectIds) ? projectIds as string[] : [projectIds as string];
    }

    // 添加日期范围过滤
    if (startDate && endDate) {
      const start = new Date(startDate as string);
      const end = new Date(endDate as string);
      
      if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
        filters.dateRange = { start, end };
      }
    }

    // 添加标签过滤
    if (tags) {
      filters.tags = Array.isArray(tags) ? tags as string[] : [tags as string];
    }

    // 添加文件类型过滤
    if (fileTypes) {
      filters.fileTypes = Array.isArray(fileTypes) ? fileTypes as string[] : [fileTypes as string];
    }

    // 执行搜索
    const searchService = new AdvancedSearchService(permissions);
    const searchResults = await searchService.search(
      query.trim(),
      filters,
      parseInt(limit as string),
      parseInt(offset as string)
    );

    return sendSuccess(res, searchResults, '搜索完成');
  } catch (error) {
    console.error('高级搜索失败:', error);
    return handleApiError(error, req, res);
  }
}
