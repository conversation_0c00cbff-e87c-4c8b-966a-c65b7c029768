// 默认头像系统

// 默认头像列表 - 使用不同颜色的圆形头像
export const DEFAULT_AVATARS = [
  // 渐变色头像
  {
    id: 'gradient-1',
    name: '蓝紫渐变',
    svg: `<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
        </linearGradient>
      </defs>
      <circle cx="20" cy="20" r="20" fill="url(#grad1)" />
      <circle cx="20" cy="16" r="6" fill="white" opacity="0.8" />
      <circle cx="20" cy="30" r="8" fill="white" opacity="0.8" />
    </svg>`
  },
  {
    id: 'gradient-2',
    name: '橙红渐变',
    svg: `<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
        </linearGradient>
      </defs>
      <circle cx="20" cy="20" r="20" fill="url(#grad2)" />
      <circle cx="20" cy="16" r="6" fill="white" opacity="0.8" />
      <circle cx="20" cy="30" r="8" fill="white" opacity="0.8" />
    </svg>`
  },
  {
    id: 'gradient-3',
    name: '绿青渐变',
    svg: `<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
        </linearGradient>
      </defs>
      <circle cx="20" cy="20" r="20" fill="url(#grad3)" />
      <circle cx="20" cy="16" r="6" fill="white" opacity="0.8" />
      <circle cx="20" cy="30" r="8" fill="white" opacity="0.8" />
    </svg>`
  },
  {
    id: 'gradient-4',
    name: '紫粉渐变',
    svg: `<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad4" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#a8edea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#fed6e3;stop-opacity:1" />
        </linearGradient>
      </defs>
      <circle cx="20" cy="20" r="20" fill="url(#grad4)" />
      <circle cx="20" cy="16" r="6" fill="white" opacity="0.8" />
      <circle cx="20" cy="30" r="8" fill="white" opacity="0.8" />
    </svg>`
  },
  {
    id: 'gradient-5',
    name: '黄橙渐变',
    svg: `<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad5" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#ffecd2;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#fcb69f;stop-opacity:1" />
        </linearGradient>
      </defs>
      <circle cx="20" cy="20" r="20" fill="url(#grad5)" />
      <circle cx="20" cy="16" r="6" fill="white" opacity="0.8" />
      <circle cx="20" cy="30" r="8" fill="white" opacity="0.8" />
    </svg>`
  },
  {
    id: 'gradient-6',
    name: '深蓝渐变',
    svg: `<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad6" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#89f7fe;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#66a6ff;stop-opacity:1" />
        </linearGradient>
      </defs>
      <circle cx="20" cy="20" r="20" fill="url(#grad6)" />
      <circle cx="20" cy="16" r="6" fill="white" opacity="0.8" />
      <circle cx="20" cy="30" r="8" fill="white" opacity="0.8" />
    </svg>`
  }
];

// 纯色头像
export const SOLID_COLOR_AVATARS = [
  {
    id: 'solid-blue',
    name: '蓝色',
    color: '#3B82F6',
  },
  {
    id: 'solid-green',
    name: '绿色',
    color: '#10B981',
  },
  {
    id: 'solid-purple',
    name: '紫色',
    color: '#8B5CF6',
  },
  {
    id: 'solid-pink',
    name: '粉色',
    color: '#EC4899',
  },
  {
    id: 'solid-orange',
    name: '橙色',
    color: '#F59E0B',
  },
  {
    id: 'solid-red',
    name: '红色',
    color: '#EF4444',
  },
  {
    id: 'solid-indigo',
    name: '靛蓝',
    color: '#6366F1',
  },
  {
    id: 'solid-teal',
    name: '青色',
    color: '#14B8A6',
  }
];

/**
 * 安全的Base64编码函数，支持Unicode字符
 */
function safeBase64Encode(str: string): string {
  try {
    // 使用encodeURIComponent和btoa的组合来处理Unicode字符
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
      return String.fromCharCode(parseInt(p1, 16));
    }));
  } catch (error) {
    // 如果还是失败，使用URL编码作为备用方案
    return encodeURIComponent(str);
  }
}

/**
 * 根据用户ID生成默认头像
 */
export function getDefaultAvatar(userId: string, userName?: string): string {
  // 使用用户ID的哈希值来选择头像
  const hash = userId.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  const avatarIndex = Math.abs(hash) % DEFAULT_AVATARS.length;
  const avatar = DEFAULT_AVATARS[avatarIndex];

  // 如果有用户名，在头像中显示首字母
  if (userName) {
    const initials = getInitials(userName);
    return generateAvatarWithInitials(avatar, initials);
  }

  return `data:image/svg+xml;base64,${safeBase64Encode(avatar.svg)}`;
}

/**
 * 根据用户名生成首字母
 */
export function getInitials(name: string): string {
  if (!name) return '?';

  const words = name.trim().split(/\s+/);
  if (words.length === 1) {
    return words[0].charAt(0).toUpperCase();
  }

  return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
}

/**
 * 生成带首字母的头像
 */
export function generateAvatarWithInitials(avatar: typeof DEFAULT_AVATARS[0], initials: string): string {
  const svgWithInitials = avatar.svg.replace(
    '</svg>',
    `<text x="20" y="26" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">${initials}</text></svg>`
  );

  return `data:image/svg+xml;base64,${safeBase64Encode(svgWithInitials)}`;
}

/**
 * 生成纯色头像（带首字母）
 */
export function generateSolidColorAvatar(color: string, initials: string): string {
  const svg = `<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
    <circle cx="20" cy="20" r="20" fill="${color}" />
    <text x="20" y="26" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">${initials}</text>
  </svg>`;

  return `data:image/svg+xml;base64,${safeBase64Encode(svg)}`;
}

/**
 * 根据用户信息获取头像URL
 */
export function getUserAvatarUrl(user: { id: string; name: string; avatar?: string | null }): string {
  // 如果用户有自定义头像，使用自定义头像
  if (user.avatar) {
    // 支持HTTP/HTTPS URL和本地路径
    if (user.avatar.startsWith('http') || user.avatar.startsWith('/avatars/')) {
      return user.avatar;
    }
    // 如果是data URL（base64），直接返回
    if (user.avatar.startsWith('data:')) {
      return user.avatar;
    }
  }

  // 否则生成默认头像
  return getDefaultAvatar(user.id, user.name);
}

/**
 * 获取头像URL的安全版本，包含错误处理
 */
export function getSafeAvatarUrl(user: { id: string; name: string; avatar?: string | null }): string {
  try {
    return getUserAvatarUrl(user);
  } catch (error) {
    console.error('生成头像URL失败:', error);
    // 如果生成失败，返回一个简单的默认头像
    return generateSolidColorAvatar('#6B7280', getInitials(user.name));
  }
}

/**
 * 获取所有可用的默认头像选项
 */
export function getAvailableAvatars(): Array<{ id: string; name: string; url: string }> {
  const gradientAvatars = DEFAULT_AVATARS.map(avatar => ({
    id: avatar.id,
    name: avatar.name,
    url: `data:image/svg+xml;base64,${safeBase64Encode(avatar.svg)}`
  }));

  const solidAvatars = SOLID_COLOR_AVATARS.map(avatar => ({
    id: avatar.id,
    name: avatar.name,
    url: generateSolidColorAvatar(avatar.color, '?')
  }));

  return [...gradientAvatars, ...solidAvatars];
}
