import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

interface SearchResult {
  id: string;
  title: string;
  type: 'project' | 'task' | 'user' | 'file';
  description?: string;
  url: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    await isAuthenticated(req, res);
  } catch (error) {
    return;
  }

  const userId = await getCurrentUserId(req, res);
  const { q: query } = req.query;

  if (!query || typeof query !== 'string' || query.trim().length < 2) {
    return res.status(400).json({ message: '搜索关键词至少需要2个字符' });
  }

  try {
    const searchTerm = query.trim();
    const searchTermLower = searchTerm.toLowerCase();
    const results: SearchResult[] = [];

    // 搜索项目 - 使用更宽泛的搜索然后在应用层过滤
    const allProjects = await prisma.project.findMany({
      where: {
        OR: [
          { ownerId: userId },
          {
            members: {
              some: {
                id: userId,
              },
            },
          },
        ],
      },
      select: {
        id: true,
        title: true,
        description: true,
        status: true,
      },
    });

    // 在应用层进行大小写不敏感的搜索
    const projects = allProjects.filter(project => {
      const titleMatch = project.title.toLowerCase().includes(searchTermLower);
      const descMatch = project.description?.toLowerCase().includes(searchTermLower) || false;
      return titleMatch || descMatch;
    }).slice(0, 10);

    projects.forEach(project => {
      results.push({
        id: project.id,
        title: project.title,
        type: 'project',
        description: project.description || `状态: ${project.status}`,
        url: `/projects/${project.id}`,
      });
    });

    // 搜索任务
    const allTasks = await prisma.task.findMany({
      where: {
        OR: [
          { assigneeId: userId },
          {
            project: {
              OR: [
                { ownerId: userId },
                {
                  members: {
                    some: {
                      id: userId,
                    },
                  },
                },
              ],
            },
          },
        ],
      },
      select: {
        id: true,
        title: true,
        description: true,
        status: true,
        priority: true,
        project: {
          select: {
            title: true,
          },
        },
      },
    });

    // 在应用层进行大小写不敏感的搜索
    const tasks = allTasks.filter(task => {
      const titleMatch = task.title.toLowerCase().includes(searchTermLower);
      const descMatch = task.description?.toLowerCase().includes(searchTermLower) || false;
      return titleMatch || descMatch;
    }).slice(0, 10);

    tasks.forEach(task => {
      results.push({
        id: task.id,
        title: task.title,
        type: 'task',
        description: `${task.project.title} - ${task.status} - ${task.priority}`,
        url: `/tasks/${task.id}`,
      });
    });

    // 搜索用户（仅搜索已审核的用户）
    const allUsers = await prisma.user.findMany({
      where: {
        status: 'APPROVED',
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        department: true,
      },
    });

    // 在应用层进行大小写不敏感的搜索
    const users = allUsers.filter(user => {
      const nameMatch = user.name.toLowerCase().includes(searchTermLower);
      const emailMatch = user.email.toLowerCase().includes(searchTermLower);
      const deptMatch = user.department?.toLowerCase().includes(searchTermLower) || false;
      return nameMatch || emailMatch || deptMatch;
    }).slice(0, 5);

    users.forEach(user => {
      results.push({
        id: user.id,
        title: user.name,
        type: 'user',
        description: `${user.email} - ${user.role}${user.department ? ` - ${user.department}` : ''}`,
        url: `/profile/${user.id}`,
      });
    });

    // 搜索文件
    const allFiles = await prisma.file.findMany({
      where: {
        OR: [
          { uploaderId: userId },
          {
            project: {
              OR: [
                { ownerId: userId },
                {
                  members: {
                    some: {
                      id: userId,
                    },
                  },
                },
              ],
            },
          },
        ],
      },
      select: {
        id: true,
        name: true,
        description: true,
        type: true,
        size: true,
        project: {
          select: {
            title: true,
          },
        },
      },
    });

    // 在应用层进行大小写不敏感的搜索
    const files = allFiles.filter(file => {
      const nameMatch = file.name.toLowerCase().includes(searchTermLower);
      const descMatch = file.description?.toLowerCase().includes(searchTermLower) || false;
      return nameMatch || descMatch;
    }).slice(0, 10);

    files.forEach(file => {
      const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
      results.push({
        id: file.id,
        title: file.name,
        type: 'file',
        description: `${file.project?.title || '个人文件'} - ${sizeInMB}MB`,
        url: `/files/${file.id}`,
      });
    });

    // 按相关性排序（简单的字符串匹配度）
    results.sort((a, b) => {
      const aScore = getRelevanceScore(a.title, searchTerm);
      const bScore = getRelevanceScore(b.title, searchTerm);
      return bScore - aScore;
    });

    return res.status(200).json({
      results: results.slice(0, 20), // 限制返回20个结果
      total: results.length,
    });
  } catch (error) {
    console.error('搜索失败:', error);
    return res.status(500).json({ message: '搜索失败' });
  }
}

function getRelevanceScore(title: string, searchTerm: string): number {
  const titleLower = title.toLowerCase();
  const termLower = searchTerm.toLowerCase();

  // 完全匹配得分最高
  if (titleLower === termLower) return 100;

  // 开头匹配得分较高
  if (titleLower.startsWith(termLower)) return 80;

  // 包含匹配得分中等
  if (titleLower.includes(termLower)) return 60;

  // 模糊匹配得分较低
  const words = termLower.split(' ');
  let score = 0;
  words.forEach(word => {
    if (titleLower.includes(word)) {
      score += 20;
    }
  });

  return score;
}
