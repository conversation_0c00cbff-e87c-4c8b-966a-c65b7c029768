import React, { useState } from 'react';
import { XMarkIcon, ArrowDownTrayIcon, EyeIcon } from '@heroicons/react/24/outline';

interface FilePreviewProps {
  file: {
    id: string;
    name: string;
    type: string;
    size: number;
    path: string;
    description?: string;
  };
  isOpen: boolean;
  onClose: () => void;
}

export default function FilePreview({ file, isOpen, onClose }: FilePreviewProps) {
  const [loading, setLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(true);
  const [previewError, setPreviewError] = useState<string | null>(null);

  // 当模态框打开时重置状态
  React.useEffect(() => {
    if (isOpen) {
      setPreviewLoading(true);
      setPreviewError(null);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const isImage = file.type.startsWith('image/');
  const isPDF = file.type === 'application/pdf';
  const isText = file.type.startsWith('text/') ||
                 file.type === 'application/json' ||
                 file.name.endsWith('.md') ||
                 file.name.endsWith('.txt');
  const isVideo = file.type.startsWith('video/');
  const isAudio = file.type.startsWith('audio/');

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDownload = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/files/${file.id}/download`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = file.name;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('下载失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderPreview = () => {
    const fileUrl = `/api/files/${file.id}/view`;

    if (isImage) {
      return (
        <div className="flex items-center justify-center h-full relative">
          {previewLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700">
              <div className="loading-spinner h-8 w-8"></div>
            </div>
          )}
          {previewError ? (
            <div className="text-center">
              <div className="text-6xl mb-4">🖼️</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                图片加载失败
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {file.name}
              </p>
              <button
                onClick={handleDownload}
                className="btn btn-primary"
              >
                下载文件
              </button>
            </div>
          ) : (
            <img
              src={fileUrl}
              alt={file.name}
              className="max-w-full max-h-full object-contain rounded-lg"
              onLoad={() => setPreviewLoading(false)}
              onError={() => {
                setPreviewLoading(false);
                setPreviewError('图片加载失败');
              }}
            />
          )}
        </div>
      );
    }

    if (isPDF) {
      return (
        <div className="h-full relative">
          {previewLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-10">
              <div className="text-center">
                <div className="loading-spinner h-8 w-8 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">正在加载PDF...</p>
              </div>
            </div>
          )}
          <iframe
            src={`${fileUrl}#toolbar=1`}
            className="w-full h-full border-0 rounded-lg"
            title={file.name}
            onLoad={() => setPreviewLoading(false)}
            onError={() => {
              setPreviewLoading(false);
              setPreviewError('PDF加载失败');
            }}
          />
        </div>
      );
    }

    if (isVideo) {
      return (
        <div className="flex items-center justify-center h-full">
          <video
            controls
            className="max-w-full max-h-full rounded-lg"
            preload="metadata"
          >
            <source src={fileUrl} type={file.type} />
            您的浏览器不支持视频播放。
          </video>
        </div>
      );
    }

    if (isAudio) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="text-6xl mb-4">🎵</div>
            <audio controls className="mb-4">
              <source src={fileUrl} type={file.type} />
              您的浏览器不支持音频播放。
            </audio>
            <p className="text-gray-600 dark:text-gray-400">{file.name}</p>
          </div>
        </div>
      );
    }

    if (isText) {
      return (
        <div className="h-full relative">
          {previewLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-10">
              <div className="text-center">
                <div className="loading-spinner h-8 w-8 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">正在加载文本...</p>
              </div>
            </div>
          )}
          <iframe
            src={fileUrl}
            className="w-full h-full border-0 rounded-lg bg-white dark:bg-gray-800"
            title={file.name}
            onLoad={() => setPreviewLoading(false)}
            onError={() => {
              setPreviewLoading(false);
              setPreviewError('文本加载失败');
            }}
          />
        </div>
      );
    }

    // 不支持预览的文件类型
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-6xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            无法预览此文件
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {file.name}
          </p>
          <button
            onClick={handleDownload}
            disabled={loading}
            className="btn-gradient-primary"
          >
            {loading ? (
              <>
                <div className="loading-spinner h-4 w-4 mr-2" />
                下载中...
              </>
            ) : (
              <>
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                下载文件
              </>
            )}
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-75" onClick={onClose} />

      <div className="relative h-full flex flex-col">
        {/* 头部工具栏 */}
        <div className="glass-effect p-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <EyeIcon className="h-5 w-5 text-white" />
            <div>
              <h2 className="text-white font-medium truncate max-w-md">
                {file.name}
              </h2>
              <p className="text-gray-300 text-sm">
                {formatFileSize(file.size)} • {file.type}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={handleDownload}
              disabled={loading}
              className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
              title="下载文件"
            >
              {loading ? (
                <div className="loading-spinner h-5 w-5" />
              ) : (
                <ArrowDownTrayIcon className="h-5 w-5" />
              )}
            </button>

            <button
              onClick={onClose}
              className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
              title="关闭预览"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* 预览内容 */}
        <div className="flex-1 p-4">
          <div className="h-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
            {renderPreview()}
          </div>
        </div>

        {/* 底部信息 */}
        {file.description && (
          <div className="glass-effect p-4">
            <p className="text-white text-sm">
              <span className="font-medium">描述：</span>
              {file.description}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
