// 网络请求封装
const app = getApp();

// 请求配置
const config = {
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
};

// 显示加载提示
function showLoading(title = '加载中...') {
  wx.showLoading({
    title,
    mask: true
  });
}

// 隐藏加载提示
function hideLoading() {
  wx.hideLoading();
}

// 显示错误提示
function showError(message) {
  wx.showToast({
    title: message,
    icon: 'error',
    duration: 2000
  });
}

// 显示成功提示
function showSuccess(message) {
  wx.showToast({
    title: message,
    icon: 'success',
    duration: 2000
  });
}

// 获取请求头
function getHeaders(needAuth = true) {
  const headers = { ...config.header };

  if (needAuth && app.globalData.token) {
    headers['Authorization'] = `Bearer ${app.globalData.token}`;
  }

  return headers;
}

// 处理响应
function handleResponse(res, resolve, reject, showLoading = true) {
  if (showLoading) {
    hideLoading();
  }

  const { statusCode, data } = res;

  if (statusCode === 200) {
    if (data.success !== false) {
      resolve(data);
    } else {
      const message = data.message || '请求失败';
      showError(message);
      reject(new Error(message));
    }
  } else if (statusCode === 401) {
    // 未授权，清除登录信息并跳转到登录页
    app.clearUserInfo();
    wx.reLaunch({
      url: '/pages/login/login'
    });
    reject(new Error('登录已过期，请重新登录'));
  } else {
    const message = data?.message || `请求失败 (${statusCode})`;
    showError(message);
    reject(new Error(message));
  }
}

// 处理错误
function handleError(err, reject, showLoadingOption = true) {
  if (showLoadingOption) {
    hideLoading();
  }

  // 检查是否为连接拒绝错误（开发环境下的正常情况）
  const isConnectionRefused = err.errMsg && (
    err.errMsg.includes('ERR_CONNECTION_REFUSED') ||
    err.errMsg.includes('request:fail') ||
    err.errMsg.includes('timeout')
  );

  if (isConnectionRefused) {
    // 连接拒绝错误静默处理，不显示错误信息
    console.log('API服务器未连接，将使用模拟数据');
    reject(new Error('API_UNAVAILABLE'));
    return;
  }

  // 其他错误正常处理
  console.error('请求错误:', err);

  let message = '网络请求失败';
  if (err.errMsg) {
    if (err.errMsg.includes('timeout')) {
      message = '请求超时，请检查网络连接';
    } else if (err.errMsg.includes('fail')) {
      message = '网络连接失败，请检查网络设置';
    }
  }

  showError(message);
  reject(new Error(message));
}

// GET 请求
function get(url, options = {}) {
  const { showLoading: showLoadingOption = true, needAuth = true } = options;

  return new Promise((resolve, reject) => {
    if (showLoadingOption) {
      showLoading();
    }

    wx.request({
      url: app.globalData.baseUrl + url,
      method: 'GET',
      header: getHeaders(needAuth),
      timeout: config.timeout,
      success: (res) => handleResponse(res, resolve, reject, showLoadingOption),
      fail: (err) => handleError(err, reject, showLoadingOption)
    });
  });
}

// POST 请求
function post(url, data = {}, options = {}) {
  const { showLoading: showLoadingOption = true, needAuth = true } = options;

  return new Promise((resolve, reject) => {
    if (showLoadingOption) {
      showLoading();
    }

    wx.request({
      url: app.globalData.baseUrl + url,
      method: 'POST',
      data,
      header: getHeaders(needAuth),
      timeout: config.timeout,
      success: (res) => handleResponse(res, resolve, reject, showLoadingOption),
      fail: (err) => handleError(err, reject, showLoadingOption)
    });
  });
}

// PUT 请求
function put(url, data = {}, options = {}) {
  const { showLoading: showLoadingOption = true, needAuth = true } = options;

  return new Promise((resolve, reject) => {
    if (showLoadingOption) {
      showLoading();
    }

    wx.request({
      url: app.globalData.baseUrl + url,
      method: 'PUT',
      data,
      header: getHeaders(needAuth),
      timeout: config.timeout,
      success: (res) => handleResponse(res, resolve, reject, showLoadingOption),
      fail: (err) => handleError(err, reject, showLoadingOption)
    });
  });
}

// DELETE 请求
function del(url, options = {}) {
  const { showLoading: showLoadingOption = true, needAuth = true } = options;

  return new Promise((resolve, reject) => {
    if (showLoadingOption) {
      showLoading();
    }

    wx.request({
      url: app.globalData.baseUrl + url,
      method: 'DELETE',
      header: getHeaders(needAuth),
      timeout: config.timeout,
      success: (res) => handleResponse(res, resolve, reject, showLoadingOption),
      fail: (err) => handleError(err, reject, showLoadingOption)
    });
  });
}

// 文件上传
function uploadFile(options) {
  const { url, filePath, name, formData = {}, showLoading: showLoadingOption = true } = options;

  return new Promise((resolve, reject) => {
    if (showLoadingOption) {
      showLoading('上传中...');
    }

    wx.uploadFile({
      url: app.globalData.baseUrl + url,
      filePath,
      name,
      formData,
      header: {
        'Authorization': app.globalData.token ? `Bearer ${app.globalData.token}` : ''
      },
      success: (res) => {
        if (showLoadingOption) {
          hideLoading();
        }

        try {
          const data = JSON.parse(res.data);
          if (data.success !== false) {
            resolve(data);
          } else {
            const message = data.message || '上传失败';
            showError(message);
            reject(new Error(message));
          }
        } catch (e) {
          showError('上传响应解析失败');
          reject(new Error('上传响应解析失败'));
        }
      },
      fail: (err) => handleError(err, reject, showLoadingOption)
    });
  });
}

module.exports = {
  get,
  post,
  put,
  del,
  uploadFile,
  showLoading,
  hideLoading,
  showError,
  showSuccess
};
