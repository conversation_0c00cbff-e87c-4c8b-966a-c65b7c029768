import { format } from 'date-fns';
import Link from 'next/link';
import Avatar from './Avatar';
import { CalendarIcon, UserIcon, ClockIcon } from '@heroicons/react/24/outline';

interface TaskCardProps {
  task: {
    id: string;
    title: string;
    description: string | null;
    dueDate: Date | null;
    status: string;
    priority: string;
    project?: {
      id: string;
      title: string;
    };
    assignee?: {
      id: string;
      name: string;
      avatar?: string | null;
    } | null;
    assignees?: {
      id: string;
      name: string;
      avatar?: string | null;
    }[];
  };
  onStatusChange?: (taskId: string, newStatus: string) => void;
  showQuickActions?: boolean;
}

export default function TaskCard({ task, onStatusChange, showQuickActions = false }: TaskCardProps) {
  // 格式化日期
  const formatDate = (date: Date | null) => {
    if (!date) return '未设置';
    return format(new Date(date), 'yyyy-MM-dd');
  };

  // 根据任务状态获取状态标签样式
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'TODO':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'REVIEW':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'TODO':
        return '待处理';
      case 'IN_PROGRESS':
        return '进行中';
      case 'REVIEW':
        return '审核中';
      case 'COMPLETED':
        return '已完成';
      default:
        return status;
    }
  };

  // 根据任务优先级获取优先级标签样式
  const getPriorityBadgeClass = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'URGENT':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // 获取优先级显示文本
  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return '低';
      case 'MEDIUM':
        return '中';
      case 'HIGH':
        return '高';
      case 'URGENT':
        return '紧急';
      default:
        return priority;
    }
  };

  // 快速状态切换
  const handleQuickStatusChange = async (newStatus: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onStatusChange) {
      onStatusChange(task.id, newStatus);
    }
  };

  // 获取下一个状态
  const getNextStatus = (currentStatus: string) => {
    switch (currentStatus) {
      case 'TODO':
        return 'IN_PROGRESS';
      case 'IN_PROGRESS':
        return 'REVIEW';
      case 'REVIEW':
        return 'COMPLETED';
      case 'COMPLETED':
        return 'TODO';
      default:
        return 'IN_PROGRESS';
    }
  };

  return (
    <div className="group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:border-primary-300 dark:hover:border-primary-600">
      {/* 优先级指示条 */}
      <div className={`h-1 ${
        task.priority === 'URGENT' ? 'bg-gradient-to-r from-red-500 to-red-600' :
        task.priority === 'HIGH' ? 'bg-gradient-to-r from-orange-500 to-orange-600' :
        task.priority === 'MEDIUM' ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
        'bg-gradient-to-r from-blue-500 to-blue-600'
      }`}></div>

      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
            <Link href={`/tasks/${task.id}`} className="hover:underline">
              {task.title}
            </Link>
          </h3>
          <div className="flex flex-col space-y-2">
            <span className={`px-3 py-1 text-xs font-semibold rounded-full shadow-sm ${getPriorityBadgeClass(task.priority)}`}>
              {getPriorityText(task.priority)}
            </span>
            <div className="flex items-center space-x-2">
              <span className={`px-3 py-1 text-xs font-semibold rounded-full shadow-sm ${getStatusBadgeClass(task.status)}`}>
                {getStatusText(task.status)}
              </span>
              {showQuickActions && onStatusChange && (
                <button
                  onClick={(e) => handleQuickStatusChange(getNextStatus(task.status), e)}
                  className="p-1 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-primary-100 dark:hover:bg-primary-900 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                  title={`切换到: ${getStatusText(getNextStatus(task.status))}`}
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        </div>

        {task.description && (
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border-l-4 border-primary-500">
            <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed line-clamp-2">
              {task.description}
            </p>
          </div>
        )}

        <div className="space-y-4 mb-4">
          {/* 截止日期 */}
          <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
            <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
              <CalendarIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-xs font-medium text-blue-700 dark:text-blue-300 uppercase tracking-wide">截止日期</p>
              <p className="text-sm font-semibold text-blue-900 dark:text-blue-100">{formatDate(task.dueDate)}</p>
            </div>
          </div>

          {/* 任务成员 */}
          <div className="p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg">
            <div className="flex items-center space-x-2 mb-3">
              <div className="p-2 bg-green-100 dark:bg-green-800/50 rounded-lg">
                <UserIcon className="w-4 h-4 text-green-600 dark:text-green-400" />
              </div>
              <p className="text-xs font-medium text-green-700 dark:text-green-300 uppercase tracking-wide">任务成员</p>
            </div>

            {/* 主要负责人 */}
            {task.assignee ? (
              <div className="flex items-center space-x-3 mb-3 p-2 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                <Avatar
                  user={{
                    id: task.assignee.id,
                    name: task.assignee.name,
                    avatar: task.assignee.avatar
                  }}
                  size="sm"
                />
                <div className="flex-1">
                  <Link
                    href={`/profile/${task.assignee.id}`}
                    className="text-sm font-semibold text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {task.assignee.name}
                  </Link>
                  <span className="ml-2 text-xs text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-800/50 px-2 py-1 rounded-full font-medium">
                    负责人
                  </span>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-3 p-2 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                  <UserIcon className="w-4 h-4 text-gray-400" />
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400">未分配负责人</p>
              </div>
            )}

            {/* 协作成员 */}
            {task.assignees && task.assignees.length > 0 && (
              <div className="space-y-2">
                {task.assignees.slice(0, 2).map((assignee) => (
                  <div key={assignee.id} className="flex items-center space-x-3 p-2 bg-white/40 dark:bg-gray-800/40 rounded-lg">
                    <Avatar
                      user={{
                        id: assignee.id,
                        name: assignee.name,
                        avatar: assignee.avatar
                      }}
                      size="xs"
                    />
                    <Link
                      href={`/profile/${assignee.id}`}
                      className="text-sm text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {assignee.name}
                    </Link>
                  </div>
                ))}
                {task.assignees.length > 2 && (
                  <div className="flex items-center space-x-3 p-2 bg-white/40 dark:bg-gray-800/40 rounded-lg">
                    <div className="w-6 h-6 rounded-full bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-700 flex items-center justify-center">
                      <span className="text-xs font-bold text-gray-600 dark:text-gray-300">+{task.assignees.length - 2}</span>
                    </div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      还有 {task.assignees.length - 2} 位协作成员
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {task.project && (
          <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
            <Link
              href={`/projects/${task.project.id}`}
              className="inline-flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 text-primary-700 dark:text-primary-300 rounded-lg hover:from-primary-100 hover:to-primary-200 dark:hover:from-primary-800/40 dark:hover:to-primary-700/40 transition-all duration-200 text-sm font-medium group"
            >
              <div className="w-2 h-2 bg-primary-500 rounded-full group-hover:animate-pulse"></div>
              <span>项目: {task.project.title}</span>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
