const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('开始创建示例项目和任务数据...');

  // 获取用户
  const users = await prisma.user.findMany({
    select: { id: true, name: true, email: true }
  });

  if (users.length === 0) {
    console.log('没有找到用户，请先运行用户种子数据');
    return;
  }

  const adminUser = users.find(u => u.email === '<EMAIL>');
  const leaderUser = users.find(u => u.email === '<EMAIL>');
  const memberUsers = users.filter(u => !['<EMAIL>', '<EMAIL>'].includes(u.email));

  if (!adminUser || !leaderUser) {
    console.log('未找到管理员或组长用户');
    return;
  }

  // 创建示例项目
  const projects = [
    {
      title: 'LabSync 研发项目',
      description: '实验室协作管理系统的核心开发项目，包含项目管理、任务跟踪、文件共享等功能模块。',
      status: 'ACTIVE',
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-06-30'),
      ownerId: adminUser.id,
      members: [adminUser.id, leaderUser.id, ...memberUsers.slice(0, 3).map(u => u.id)],
    },
    {
      title: '用户界面优化项目',
      description: '改进系统用户界面设计，提升用户体验和操作便利性。',
      status: 'ACTIVE',
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-04-30'),
      ownerId: leaderUser.id,
      members: [leaderUser.id, ...memberUsers.slice(0, 2).map(u => u.id)],
    },
    {
      title: '系统性能测试',
      description: '对系统进行全面的性能测试和优化，确保系统稳定性。',
      status: 'PLANNING',
      startDate: new Date('2024-03-01'),
      endDate: new Date('2024-05-31'),
      ownerId: adminUser.id,
      members: [adminUser.id, ...memberUsers.slice(2, 4).map(u => u.id)],
    },
    {
      title: '移动端适配',
      description: '开发移动端响应式界面，支持手机和平板设备访问。',
      status: 'COMPLETED',
      startDate: new Date('2023-10-01'),
      endDate: new Date('2023-12-31'),
      ownerId: leaderUser.id,
      members: [leaderUser.id, ...memberUsers.slice(0, 2).map(u => u.id)],
    },
  ];

  // 创建项目
  for (const projectData of projects) {
    const { members, ...projectInfo } = projectData;

    // 检查项目是否已存在
    const existingProject = await prisma.project.findFirst({
      where: { title: projectData.title }
    });

    if (existingProject) {
      console.log(`项目已存在: ${projectData.title}`);
      continue;
    }

    const project = await prisma.project.create({
      data: {
        ...projectInfo,
        members: {
          connect: members.map(id => ({ id }))
        }
      }
    });

    console.log(`创建项目: ${project.title}`);

    // 为每个项目创建任务
    const taskTemplates = [
      { title: '需求分析', status: 'COMPLETED', priority: 'HIGH' },
      { title: '系统设计', status: 'COMPLETED', priority: 'HIGH' },
      { title: '前端开发', status: 'IN_PROGRESS', priority: 'MEDIUM' },
      { title: '后端开发', status: 'IN_PROGRESS', priority: 'MEDIUM' },
      { title: '数据库设计', status: 'COMPLETED', priority: 'HIGH' },
      { title: '接口开发', status: 'REVIEW', priority: 'MEDIUM' },
      { title: '单元测试', status: 'TODO', priority: 'MEDIUM' },
      { title: '集成测试', status: 'TODO', priority: 'LOW' },
      { title: '用户验收测试', status: 'TODO', priority: 'HIGH' },
      { title: '部署上线', status: 'TODO', priority: 'HIGH' },
    ];

    // 根据项目状态调整任务状态
    let tasksToCreate = taskTemplates;
    if (project.status === 'COMPLETED') {
      tasksToCreate = taskTemplates.map(task => ({ ...task, status: 'COMPLETED' }));
    } else if (project.status === 'PLANNING') {
      tasksToCreate = taskTemplates.map(task => ({ ...task, status: 'TODO' }));
    }

    // 随机选择任务数量（5-8个任务）
    const numTasks = Math.floor(Math.random() * 4) + 5;
    const selectedTasks = tasksToCreate.slice(0, numTasks);

    for (let i = 0; i < selectedTasks.length; i++) {
      const taskTemplate = selectedTasks[i];
      const assigneeId = members[i % members.length];

      await prisma.task.create({
        data: {
          title: taskTemplate.title,
          description: `${taskTemplate.title}相关的详细工作内容和要求。`,
          status: taskTemplate.status,
          priority: taskTemplate.priority,
          projectId: project.id,
          assigneeId: assigneeId,
          dueDate: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000), // 随机30天内
        }
      });
    }

    console.log(`为项目 ${project.title} 创建了 ${selectedTasks.length} 个任务`);
  }

  console.log('示例项目和任务数据创建完成！');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
