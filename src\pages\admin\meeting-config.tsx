import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';

interface MeetingConfig {
  id?: string;
  appId: string;
  secretId: string;
  secretKey: string;
  sdkId: string;
  isEnabled: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export default function MeetingConfig() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [config, setConfig] = useState<MeetingConfig>({
    appId: '',
    secretId: '',
    secretKey: '',
    sdkId: '',
    isEnabled: false,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // 权限检查
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    } else if (status === 'authenticated' && session?.user?.role !== 'ADMIN') {
      router.push('/');
    }
  }, [status, session, router]);

  // 暂时跳过配置获取，显示配置说明
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role === 'ADMIN') {
      setLoading(false);
    }
  }, [status, session]);

  const saveConfig = async () => {
    if (!config.appId || !config.secretId || !config.secretKey || !config.sdkId) {
      setError('请填写所有必填字段');
      return;
    }

    // 暂时只显示提示信息
    setSuccess('配置功能正在开发中，目前会议创建功能使用本地记录模式。');
  };

  const testConnection = async () => {
    if (!config.appId || !config.secretId || !config.secretKey || !config.sdkId) {
      setError('请先填写完整配置信息');
      return;
    }

    // 暂时只显示提示信息
    setSuccess('连接测试功能正在开发中，请参考配置说明手动验证API密钥。');
  };

  if (status === 'loading' || loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (session?.user?.role !== 'ADMIN') {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded">
          权限不足，只有管理员可以访问此页面。
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          腾讯会议配置
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          配置腾讯会议API，实现一键创建会议功能
        </p>

        {/* 当前状态提示 */}
        <div className="mt-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200 px-4 py-3 rounded">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="font-medium">当前状态：本地会议模式</p>
              <p className="text-sm">会议创建功能已可用，使用本地记录模式。配置腾讯会议API后将支持自动创建真实会议室。</p>
            </div>
          </div>
        </div>
      </div>

      {/* 错误和成功提示 */}
      {error && (
        <div className="mb-6 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200 px-4 py-3 rounded">
          {success}
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md">
        <div className="p-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            API配置信息
          </h2>

          <div className="space-y-6">
            {/* App ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                App ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={config.appId}
                onChange={(e) => setConfig({ ...config, appId: e.target.value })}
                placeholder="请输入腾讯会议App ID"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Secret ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Secret ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={config.secretId}
                onChange={(e) => setConfig({ ...config, secretId: e.target.value })}
                placeholder="请输入Secret ID"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Secret Key */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Secret Key <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                value={config.secretKey}
                onChange={(e) => setConfig({ ...config, secretKey: e.target.value })}
                placeholder="请输入Secret Key"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* SDK ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SDK ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={config.sdkId}
                onChange={(e) => setConfig({ ...config, sdkId: e.target.value })}
                placeholder="请输入SDK ID"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* 启用状态 */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isEnabled"
                checked={config.isEnabled}
                onChange={(e) => setConfig({ ...config, isEnabled: e.target.checked })}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="isEnabled" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                启用腾讯会议集成
              </label>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="mt-8 flex space-x-4">
            <button
              onClick={testConnection}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              测试连接
            </button>
            <button
              onClick={saveConfig}
              disabled={saving}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                  保存中...
                </>
              ) : (
                '保存配置'
              )}
            </button>
          </div>
        </div>

        {/* 配置说明 */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            配置说明
          </h3>
          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <div>
              <strong>1. 获取API密钥：</strong>
              <p>访问 <a href="https://meeting.tencent.com/open-api.html" target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:underline">腾讯会议开放平台</a> 注册开发者账号并创建应用</p>
            </div>
            <div>
              <strong>2. App ID：</strong>
              <p>在腾讯会议开放平台创建应用后获得的应用ID</p>
            </div>
            <div>
              <strong>3. Secret ID & Secret Key：</strong>
              <p>用于API签名验证的密钥对，请妥善保管</p>
            </div>
            <div>
              <strong>4. SDK ID：</strong>
              <p>腾讯会议SDK的标识ID</p>
            </div>
            <div>
              <strong>5. 权限要求：</strong>
              <p>确保您的腾讯会议账号具有创建会议的权限，企业版账号支持更多高级功能</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
