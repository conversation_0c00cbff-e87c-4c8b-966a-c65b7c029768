import type { NextApiRequest, NextApiResponse } from 'next';
import { IncomingForm } from 'formidable';
import fs from 'fs';
import path from 'path';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId, isAdmin } from '@/lib/auth';
import { isProjectMember } from '@/lib/permissions';
import { notifyFileUploaded } from '@/lib/systemNotifications';

// 禁用默认的bodyParser，以便我们可以使用formidable解析multipart/form-data
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const userId = await getCurrentUserId(req, res);
  const isUserAdmin = await isAdmin(req, res);

  // 处理GET请求 - 获取文件列表
  if (req.method === 'GET') {
    try {
      let files;
      const { projectId, taskId } = req.query;

      // 构建查询条件
      const where: any = {};

      if (projectId) {
        // 检查用户是否有权限访问该项目
        const canAccessProject = await isProjectMember(req, res, projectId as string);
        if (!canAccessProject) {
          return res.status(403).json({ message: '没有权限访问该项目的文件' });
        }
        where.projectId = projectId;
      }

      if (taskId) {
        // 获取任务信息以检查项目权限
        const task = await prisma.task.findUnique({
          where: { id: taskId as string },
          select: { projectId: true },
        });

        if (!task) {
          return res.status(404).json({ message: '任务不存在' });
        }

        // 检查用户是否有权限访问该任务所属的项目
        const canAccessProject = await isProjectMember(req, res, task.projectId);
        if (!canAccessProject) {
          return res.status(403).json({ message: '没有权限访问该任务的文件' });
        }

        where.taskId = taskId;
      }

      // 如果既没有指定项目也没有指定任务，则根据用户角色获取文件
      if (!projectId && !taskId) {
        if (isUserAdmin) {
          // 管理员可以查看所有文件
        } else {
          // 普通用户只能查看自己上传的文件或有权限访问的项目/任务的文件
          where.OR = [
            { uploaderId: userId },
            {
              project: {
                OR: [
                  { ownerId: userId },
                  {
                    members: {
                      some: {
                        id: userId,
                      },
                    },
                  },
                ],
              },
            },
          ];
        }
      }

      // 获取文件列表
      files = await prisma.file.findMany({
        where,
        include: {
          uploader: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          project: {
            select: {
              id: true,
              title: true,
            },
          },
          task: {
            select: {
              id: true,
              title: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });

      return res.status(200).json(files);
    } catch (error) {
      console.error('获取文件列表失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理POST请求 - 上传文件
  if (req.method === 'POST') {
    return new Promise((resolve) => {
      // 创建上传目录（如果不存在）
      const uploadDir = path.join(process.cwd(), 'uploads');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      const form = new IncomingForm({
        uploadDir,
        keepExtensions: true,
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 默认10MB
      });

      form.parse(req, async (err: any, fields: any, files: any) => {
        if (err) {
          console.error('文件上传失败:', err);
          let errorMessage = '文件上传失败';

          // 根据错误类型提供更具体的错误信息
          if (err.code === 'LIMIT_FILE_SIZE') {
            errorMessage = '文件大小超过限制（最大10MB）';
          } else if (err.code === 'ENOENT') {
            errorMessage = '上传目录不存在或无权限访问';
          } else if (err.message) {
            errorMessage = `文件上传失败: ${err.message}`;
          }

          res.status(500).json({ message: errorMessage });
          return resolve(undefined);
        }

        try {
          // 获取当前用户ID
          const userId = await getCurrentUserId(req, res);
          if (!userId) {
            res.status(401).json({ message: '未认证' });
            return resolve(undefined);
          }

          const file = files.file && files.file[0];
          const { projectId, taskId, description } = fields;

          // 验证请求数据
          if (!file) {
            res.status(400).json({ message: '请提供文件' });
            return resolve(undefined);
          }

          // 验证文件类型（可选）
          const allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain', 'text/csv'
          ];

          if (file.mimetype && !allowedTypes.includes(file.mimetype)) {
            console.warn(`不支持的文件类型: ${file.mimetype}`);
            // 注意：这里不阻止上传，只是记录警告
          }

          // 如果指定了项目，检查用户是否有权限访问该项目
          if (projectId && projectId[0]) {
            const canAccessProject = await isProjectMember(req, res, projectId[0]);
            if (!canAccessProject) {
              res.status(403).json({ message: '没有权限在该项目中上传文件' });
              return resolve(undefined);
            }
          }

          // 如果指定了任务，检查用户是否有权限访问该任务
          if (taskId && taskId[0]) {
            const task = await prisma.task.findUnique({
              where: { id: taskId[0] },
              select: { projectId: true },
            });

            if (!task) {
              res.status(404).json({ message: '任务不存在' });
              return resolve(undefined);
            }

            const canAccessProject = await isProjectMember(req, res, task.projectId);
            if (!canAccessProject) {
              res.status(403).json({ message: '没有权限在该任务中上传文件' });
              return resolve(undefined);
            }
          }

          // 获取文件信息
          const originalFilename = file.originalFilename || 'unnamed_file';
          const fileSize = file.size;
          const fileType = file.mimetype || 'application/octet-stream';
          const filePath = file.filepath;

          // 生成相对路径（用于存储在数据库中）
          const relativePath = path.relative(uploadDir, filePath).replace(/\\/g, '/');

          // 创建文件记录
          const fileRecord = await prisma.file.create({
            data: {
              name: originalFilename,
              path: relativePath,
              type: fileType,
              size: fileSize,
              description: description ? description[0] : null,
              uploader: {
                connect: { id: userId },
              },
              ...(projectId && projectId[0]
                ? {
                    project: {
                      connect: { id: projectId[0] },
                    },
                  }
                : {}),
              ...(taskId && taskId[0]
                ? {
                    task: {
                      connect: { id: taskId[0] },
                    },
                  }
                : {}),
            },
          });

          // 发送文件上传通知
          try {
            if (projectId && projectId[0]) {
              // 获取项目信息和成员
              const project = await prisma.project.findUnique({
                where: { id: projectId[0] },
                include: {
                  members: {
                    select: { id: true }
                  }
                }
              });

              // 获取上传者信息
              const uploader = await prisma.user.findUnique({
                where: { id: userId! },
                select: { name: true }
              });

              if (project && uploader) {
                // 通知项目成员（除了上传者本人）
                const memberIds = project.members
                  .map(m => m.id)
                  .filter(id => id !== userId);

                if (memberIds.length > 0) {
                  await notifyFileUploaded(
                    projectId[0],
                    memberIds,
                    originalFilename,
                    project.title,
                    uploader.name
                  );
                }
              }
            }
          } catch (notificationError) {
            console.error('发送文件上传通知失败:', notificationError);
            // 不影响文件上传，只记录错误
          }

          res.status(201).json(fileRecord);
          return resolve(undefined);
        } catch (error) {
          console.error('保存文件记录失败:', error);
          res.status(500).json({ message: '服务器错误，请稍后再试' });
          return resolve(undefined);
        }
      });
    });
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
