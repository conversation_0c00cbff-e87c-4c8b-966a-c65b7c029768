import Link from 'next/link';
import { useState } from 'react';
import Avatar from './Avatar';

interface TeamMemberCardProps {
  member: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    role: string;
    position?: string;
    department?: string;
    age?: number;
    bio?: string;
    stats: {
      totalProjects: number;
      activeProjects: number;
      totalTasks: number;
      completedTasks: number;
      completionRate: number;
      contributionScore: number;
      filesUploaded: number;
      messagesCount: number;
    };
  };
  variant?: 'default' | 'compact' | 'detailed';
}

export default function TeamMemberCard({ member, variant = 'default' }: TeamMemberCardProps) {

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'LEADER':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'MEMBER':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return '管理员';
      case 'LEADER':
        return '项目负责人';
      case 'MEMBER':
        return '团队成员';
      default:
        return '访客';
    }
  };

  const getContributionLevel = (score: number) => {
    if (score >= 80) return { label: '杰出贡献', color: 'text-green-600 dark:text-green-400' };
    if (score >= 60) return { label: '优秀贡献', color: 'text-blue-600 dark:text-blue-400' };
    if (score >= 40) return { label: '良好贡献', color: 'text-yellow-600 dark:text-yellow-400' };
    if (score >= 20) return { label: '一般贡献', color: 'text-orange-600 dark:text-orange-400' };
    return { label: '待提升', color: 'text-red-600 dark:text-red-400' };
  };

  if (variant === 'compact') {
    return (
      <Link href={`/profile/${member.id}`}>
        <div className="group bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-4 border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-600">
          <div className="flex items-center space-x-3">
            <Avatar
              user={{
                id: member.id,
                name: member.name,
                avatar: member.avatar
              }}
              size="lg"
            />
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate group-hover:text-primary-600 dark:group-hover:text-primary-400">
                {member.name}
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {member.position || member.email}
              </p>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRoleColor(member.role)}`}>
                  {getRoleDisplayName(member.role)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  if (variant === 'detailed') {
    const contributionLevel = getContributionLevel(member.stats.contributionScore);

    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-all duration-300">
        {/* 头部背景 */}
        <div className="h-20 bg-gradient-to-r from-primary-500 to-primary-600"></div>

        {/* 内容区域 */}
        <div className="relative px-6 pb-6">
          {/* 头像 */}
          <div className="flex justify-center -mt-12 mb-4">
            <Link href={`/profile/${member.id}`}>
              <div className="hover:scale-105 transition-transform cursor-pointer">
                <Avatar
                  user={{
                    id: member.id,
                    name: member.name,
                    avatar: member.avatar
                  }}
                  size="xl"
                  className="border-4 border-white dark:border-gray-800 shadow-lg"
                />
              </div>
            </Link>
          </div>

          {/* 基本信息 */}
          <div className="text-center mb-4">
            <Link href={`/profile/${member.id}`}>
              <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 cursor-pointer">
                {member.name}
              </h3>
            </Link>
            {member.position && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {member.position}
              </p>
            )}
            {member.department && (
              <p className="text-xs text-gray-500 dark:text-gray-500">
                {member.department}
              </p>
            )}

            {/* 角色标签 */}
            <div className="flex justify-center mt-2">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getRoleColor(member.role)}`}>
                {getRoleDisplayName(member.role)}
              </span>
            </div>
          </div>

          {/* 个人简介 */}
          {member.bio && (
            <div className="mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400 text-center line-clamp-2">
                {member.bio}
              </p>
            </div>
          )}

          {/* 统计信息 */}
          <div className="space-y-3">
            {/* 贡献度 */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">贡献度</span>
              <div className="flex items-center space-x-2">
                <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${member.stats.contributionScore}%` }}
                  ></div>
                </div>
                <span className={`text-xs font-medium ${contributionLevel.color}`}>
                  {contributionLevel.label}
                </span>
              </div>
            </div>

            {/* 任务完成率 */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">完成率</span>
              <div className="flex items-center space-x-2">
                <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${member.stats.completionRate}%` }}
                  ></div>
                </div>
                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  {member.stats.completionRate}%
                </span>
              </div>
            </div>

            {/* 统计数字 */}
            <div className="grid grid-cols-2 gap-3 pt-3 border-t border-gray-200 dark:border-gray-700">
              <div className="text-center">
                <div className="text-lg font-bold text-primary-600 dark:text-primary-400">
                  {member.stats.totalProjects}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">项目</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-600 dark:text-green-400">
                  {member.stats.completedTasks}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">已完成</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 默认样式
  return (
    <Link href={`/profile/${member.id}`}>
      <div className="group bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-600">
        <div className="flex items-start space-x-4">
          <div className="group-hover:scale-105 transition-transform">
            <Avatar
              user={{
                id: member.id,
                name: member.name,
                avatar: member.avatar
              }}
              size="lg"
            />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate group-hover:text-primary-600 dark:group-hover:text-primary-400">
              {member.name}
            </h3>
            {member.position && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {member.position}
              </p>
            )}
            <div className="flex items-center space-x-2 mt-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(member.role)}`}>
                {getRoleDisplayName(member.role)}
              </span>
              {member.stats.contributionScore > 0 && (
                <span className={`text-xs font-medium ${getContributionLevel(member.stats.contributionScore).color}`}>
                  {getContributionLevel(member.stats.contributionScore).label}
                </span>
              )}
            </div>

            {/* 简要统计 */}
            <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500 dark:text-gray-400">
              <span>{member.stats.totalProjects} 项目</span>
              <span>{member.stats.completedTasks}/{member.stats.totalTasks} 任务</span>
              {member.stats.completionRate > 0 && (
                <span className="text-green-600 dark:text-green-400">
                  {member.stats.completionRate}% 完成率
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
