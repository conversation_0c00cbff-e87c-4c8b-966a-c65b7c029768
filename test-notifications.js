// 测试系统通知功能
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testNotifications() {
  try {
    console.log('🔔 开始测试系统通知功能...\n');

    // 获取测试用户
    const users = await prisma.user.findMany({
      take: 3,
      select: {
        id: true,
        name: true,
        email: true,
        status: true,
      },
    });

    if (users.length < 2) {
      console.log('❌ 需要至少2个用户来测试通知功能');
      return;
    }

    const user1 = users[0];
    const user2 = users[1];
    console.log(`📋 测试用户: ${user1.name} 和 ${user2.name}\n`);

    // 测试1: 用户审核通知
    console.log('🧪 测试1: 用户审核通知');
    
    // 创建系统通知聊天（如果不存在）
    let systemChat = await prisma.chat.findFirst({
      where: {
        type: 'SYSTEM',
        participants: {
          some: {
            id: user1.id,
          },
        },
      },
    });

    if (!systemChat) {
      systemChat = await prisma.chat.create({
        data: {
          type: 'SYSTEM',
          name: '系统通知',
          participants: {
            connect: { id: user1.id },
          },
        },
      });
    }

    // 发送审核通过通知
    const approvalMessage = await prisma.chatMessage.create({
      data: {
        content: `🎉 您的账号已通过审核！\n审核人：${user2.name}\n您现在可以正常使用系统的所有功能了。`,
        type: 'SYSTEM',
        isSystem: true,
        chatId: systemChat.id,
      },
    });

    console.log(`✅ 审核通过通知已发送: ${approvalMessage.id}`);

    // 测试2: 项目状态变更通知
    console.log('\n🧪 测试2: 项目状态变更通知');
    
    const project = await prisma.project.findFirst({
      include: {
        members: {
          select: { id: true, name: true }
        }
      }
    });

    if (project) {
      const statusMessage = await prisma.chatMessage.create({
        data: {
          content: `📊 项目状态已更新：「${project.title}」\n新状态：进行中\n更新人：${user2.name}`,
          type: 'SYSTEM',
          isSystem: true,
          chatId: systemChat.id,
        },
      });

      console.log(`✅ 项目状态变更通知已发送: ${statusMessage.id}`);
    }

    // 测试3: 项目进度节点通知
    console.log('\n🧪 测试3: 项目进度节点通知');
    
    if (project) {
      const milestoneMessage = await prisma.chatMessage.create({
        data: {
          content: `🎯 项目进度更新：「${project.title}」\n⚡ 项目进度过半，继续加油！\n当前进度：50%`,
          type: 'SYSTEM',
          isSystem: true,
          chatId: systemChat.id,
        },
      });

      console.log(`✅ 项目进度节点通知已发送: ${milestoneMessage.id}`);
    }

    // 测试4: 文件上传通知
    console.log('\n🧪 测试4: 文件上传通知');
    
    if (project) {
      const fileMessage = await prisma.chatMessage.create({
        data: {
          content: `📎 新文件上传：「测试文档.pdf」\n项目：${project.title}\n上传人：${user2.name}`,
          type: 'SYSTEM',
          isSystem: true,
          chatId: systemChat.id,
        },
      });

      console.log(`✅ 文件上传通知已发送: ${fileMessage.id}`);
    }

    // 测试5: 项目结题通知
    console.log('\n🧪 测试5: 项目结题通知');
    
    if (project) {
      const completionMessage = await prisma.chatMessage.create({
        data: {
          content: `🏆 项目已结题：「${project.title}」\n结题人：${user2.name}\n感谢您的参与和贡献！`,
          type: 'SYSTEM',
          isSystem: true,
          chatId: systemChat.id,
        },
      });

      console.log(`✅ 项目结题通知已发送: ${completionMessage.id}`);
    }

    // 更新聊天的最后更新时间
    await prisma.chat.update({
      where: { id: systemChat.id },
      data: { updatedAt: new Date() },
    });

    console.log('\n📊 测试结果统计:');
    
    // 查看系统通知聊天中的消息
    const chatWithMessages = await prisma.chat.findUnique({
      where: { id: systemChat.id },
      include: {
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        participants: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (chatWithMessages) {
      console.log(`📨 系统通知聊天ID: ${chatWithMessages.id}`);
      console.log(`👤 参与者: ${chatWithMessages.participants.map(p => p.name).join(', ')}`);
      console.log(`💬 消息总数: ${chatWithMessages.messages.length}`);
      console.log('\n📝 最近的系统通知:');
      
      chatWithMessages.messages.slice(0, 5).forEach((msg, index) => {
        const time = new Date(msg.createdAt).toLocaleString('zh-CN');
        console.log(`${index + 1}. [${time}] ${msg.content.split('\n')[0]}`);
      });
    }

    console.log('\n✅ 系统通知功能测试完成！');
    console.log('\n💡 提示: 您可以在聊天页面查看这些系统通知消息');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testNotifications();
