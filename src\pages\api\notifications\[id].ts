import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'DELETE') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    await isAuthenticated(req, res);
  } catch (error) {
    return;
  }

  const userId = await getCurrentUserId(req, res);
  const { id: notificationId } = req.query;

  if (!notificationId || typeof notificationId !== 'string') {
    return res.status(400).json({ message: '无效的通知ID' });
  }

  try {
    // 验证通知是否属于当前用户
    const message = await prisma.chatMessage.findFirst({
      where: {
        id: notificationId,
        isSystem: true,
        chat: {
          type: 'SYSTEM',
          participants: {
            some: {
              id: userId,
            },
          },
        },
      },
    });

    if (!message) {
      return res.status(404).json({ message: '通知不存在或无权限' });
    }

    // 删除通知消息
    await prisma.chatMessage.delete({
      where: {
        id: notificationId,
      },
    });

    return res.status(200).json({ message: '删除通知成功' });
  } catch (error) {
    console.error('删除通知失败:', error);
    return res.status(500).json({ message: '删除通知失败' });
  }
}
