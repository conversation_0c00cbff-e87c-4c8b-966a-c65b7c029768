/* 项目列表页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 20rpx 80rpx 20rpx 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f9fafb;
}

.search-btn, .clear-btn {
  position: absolute;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.clear-btn {
  right: 60rpx;
}

.search-icon, .clear-icon {
  font-size: 28rpx;
  color: #6b7280;
}

.filter-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  border-radius: 12rpx;
  cursor: pointer;
}

.filter-icon {
  font-size: 32rpx;
  color: white;
}

/* 筛选面板 */
.filter-panel {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.filter-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-option.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* 项目列表 */
.project-list {
  margin-bottom: 120rpx;
}

.project-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.project-card:active {
  transform: scale(0.98);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.project-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
  margin-right: 20rpx;
}

.project-desc {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

/* 项目进度 */
.project-progress {
  margin-bottom: 20rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-label {
  font-size: 26rpx;
  color: #6b7280;
}

.progress-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 项目底部信息 */
.project-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-info {
  display: flex;
  gap: 30rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-icon {
  font-size: 24rpx;
}

.info-text {
  font-size: 24rpx;
  color: #6b7280;
}

.project-date {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: #3b82f6;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
  z-index: 100;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}

/* 加载和空状态 */
.loading, .loading-more {
  text-align: center;
  padding: 60rpx;
  color: #6b7280;
  font-size: 28rpx;
}

.empty {
  text-align: center;
  padding: 120rpx 60rpx;
  color: #9ca3af;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #d1d5db;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 15rpx;
  }
  
  .project-card {
    padding: 25rpx;
  }
  
  .project-title {
    font-size: 30rpx;
  }
  
  .fab {
    width: 100rpx;
    height: 100rpx;
    bottom: 100rpx;
    right: 30rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}
