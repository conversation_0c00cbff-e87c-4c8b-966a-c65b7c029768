/* 项目详情页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-item {
  flex: 1;
  padding: 30rpx 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #6b7280;
  transition: all 0.2s;
  border-bottom: 4rpx solid transparent;
}

.tab-item.active {
  color: #3b82f6;
  background: #f8fafc;
  border-bottom-color: #3b82f6;
}

/* 标签页内容 */
.tab-content {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 项目概览 */
.project-overview {
  margin-bottom: 30rpx;
}

.project-header {
  margin-bottom: 30rpx;
}

.project-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.project-desc {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.project-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #6b7280;
}

.meta-icon {
  font-size: 28rpx;
}

/* 项目进度 */
.progress-section {
  margin-bottom: 30rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
}

.progress-percent {
  font-size: 28rpx;
  font-weight: 600;
  color: #3b82f6;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 任务列表 */
.task-list {
  margin-bottom: 30rpx;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.task-item:last-child {
  border-bottom: none;
}

.task-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d1d5db;
  border-radius: 8rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-checkbox.completed {
  background: #10b981;
  border-color: #10b981;
}

.task-checkmark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.task-content {
  flex: 1;
}

.task-name {
  font-size: 28rpx;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.task-name.completed {
  text-decoration: line-through;
  color: #9ca3af;
}

.task-info {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #6b7280;
}

/* 成员列表 */
.member-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.member-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  transition: background-color 0.2s;
}

.member-item:active {
  background: #f3f4f6;
}

.member-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.member-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-name {
  font-size: 26rpx;
  color: #1f2937;
  margin-bottom: 8rpx;
  text-align: center;
}

.member-role {
  font-size: 22rpx;
  color: #6b7280;
  text-align: center;
}

/* 文件列表 */
.file-list {
  margin-bottom: 30rpx;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  width: 60rpx;
  height: 60rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 32rpx;
}

.file-content {
  flex: 1;
}

.file-name {
  font-size: 28rpx;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.file-info {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #6b7280;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-outline {
  background: transparent;
  color: #3b82f6;
  border: 2rpx solid #3b82f6;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 60rpx;
  color: #6b7280;
  font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 15rpx;
  }
  
  .tab-item {
    padding: 25rpx 15rpx;
    font-size: 26rpx;
  }
  
  .project-title {
    font-size: 32rpx;
  }
  
  .member-list {
    grid-template-columns: repeat(auto-fill, minmax(150rpx, 1fr));
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
