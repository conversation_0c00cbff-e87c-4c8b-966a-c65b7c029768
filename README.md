# LabSync - 实验室管理系统

一个现代化的实验室管理系统，专为科研团队和实验室设计，提供项目管理、任务分配、文件共享、实时聊天等功能。

## ✨ 主要功能

### 🔐 用户管理
- **邀请码注册系统** - 通过邀请码控制用户注册
- **用户审核机制** - 管理员审核新用户注册申请
- **角色权限管理** - 支持管理员、普通用户等不同角色
- **用户状态管理** - 待审核、已批准、已拒绝等状态

### 📊 项目管理
- **项目创建与管理** - 完整的项目生命周期管理
- **项目状态跟踪** - 计划中、进行中、已完成、已归档
- **项目成员管理** - 添加/移除项目成员
- **项目进度监控** - 基于任务完成情况自动计算进度

### ✅ 任务管理
- **任务分配** - 将任务分配给项目成员
- **任务状态跟踪** - 待办、进行中、已完成等状态
- **任务优先级** - 低、中、高、紧急四个级别
- **截止日期管理** - 任务和项目的截止日期提醒

### 💬 实时聊天
- **项目聊天** - 每个项目都有专属聊天室
- **私人聊天** - 用户之间的一对一聊天
- **系统通知** - 重要操作的自动通知
- **文件分享** - 在聊天中分享文件

### 📁 文件管理
- **文件上传** - 支持多种文件格式上传
- **项目文件** - 按项目组织文件
- **文件预览** - 支持常见文件格式预览
- **文件下载** - 安全的文件下载机制

### 🔔 消息通知系统
- **用户审核通知** - 审核通过/拒绝自动通知
- **任务分配通知** - 任务分配和完成通知
- **项目状态通知** - 项目状态变更通知
- **项目进度通知** - 重要进度节点通知（25%、50%、75%、100%）
- **成员变更通知** - 项目成员添加/移除通知
- **文件上传通知** - 新文件上传通知

## 🛠️ 技术栈

### 前端
- **Next.js 13** - React 全栈框架
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS** - 实用优先的 CSS 框架
- **React Hook Form** - 表单管理
- **Headless UI** - 无样式 UI 组件

### 后端
- **Next.js API Routes** - 服务端 API
- **Prisma** - 现代数据库 ORM
- **PostgreSQL** - 关系型数据库
- **NextAuth.js** - 身份验证
- **bcryptjs** - 密码加密

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **TypeScript** - 静态类型检查

## 🚀 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- PostgreSQL 数据库
- npm 或 yarn 包管理器

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/BWQ-L/LabSync.git
cd LabSync
```

2. **安装依赖**
```bash
npm install
```

3. **环境配置**
```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，配置数据库连接和其他环境变量：
```env
DATABASE_URL="postgresql://username:password@localhost:5432/labsync"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

4. **数据库设置**
```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma db push

# 填充初始数据
npx prisma db seed
```

5. **启动开发服务器**
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 默认账户

系统会自动创建以下测试账户：

**管理员账户**
- 邮箱: `<EMAIL>`
- 密码: `admin123`

**普通用户账户**
- 邮箱: `<EMAIL>`
- 密码: `user123`

**邀请码**
- `LABSYNC-2024-001` (使用次数: 10)
- `LABSYNC-2024-002` (使用次数: 5)

## 📖 使用指南

### 新用户注册
1. 访问注册页面 `/register`
2. 输入有效的邀请码（格式：LABSYNC-YYYY-XXX）
3. 填写个人信息
4. 等待管理员审核

### 项目管理
1. 创建新项目
2. 添加项目成员
3. 创建和分配任务
4. 跟踪项目进度

### 文件管理
1. 在项目页面上传文件
2. 在聊天中分享文件
3. 下载和预览文件

## 📚 文档

- [邀请码使用指南](./INVITE_CODE_GUIDE.md)
- [消息通知系统指南](./NOTIFICATION_SYSTEM_GUIDE.md)

## 🧪 测试

运行通知系统测试：
```bash
node test-notifications.js
```

## 部署

### 生产环境部署

1. 构建应用

```bash
npm run build
```

2. 启动生产服务器

```bash
npm start
```

### 使用Docker部署（可选）

1. 构建Docker镜像

```bash
docker build -t labsync .
```

2. 运行容器

```bash
docker run -p 3000:3000 -d labsync
```

## 项目结构

```
labsync/
├── prisma/                # Prisma数据库模型和迁移
├── public/                # 静态资源
├── src/
│   ├── components/        # React组件
│   ├── lib/               # 工具函数和库
│   ├── pages/             # 页面和API路由
│   │   ├── api/           # API路由
│   │   ├── admin/         # 管理员页面
│   │   ├── projects/      # 项目相关页面
│   │   ├── tasks/         # 任务相关页面
│   │   └── files/         # 文件相关页面
│   └── styles/            # 样式文件
├── .env                   # 环境变量
├── next.config.js         # Next.js配置
└── package.json           # 项目依赖和脚本
```

## 权限体系

系统基于以下角色进行权限控制：

- **管理员(ADMIN)**：拥有系统最高权限，可管理所有用户、项目和数据
- **项目负责人(LEADER)**：可创建项目、管理项目成员和任务
- **项目成员(MEMBER)**：可查看参与的项目、管理自己的任务
- **访客(GUEST)**：默认无权限，需被邀请加入项目

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交 Issue 或联系开发团队。

---

**LabSync** - 让实验室管理更简单、更高效！
