# 项目创建逻辑和贡献度系统更新

## 📋 **更新概述**

本次更新解决了两个主要问题：
1. **项目创建逻辑问题**：老师创建项目时可以指定项目负责人，而不是自动成为项目负责人
2. **活跃度系统重构**：将"活跃度"概念改为"贡献度"，并优化计算逻辑以区分不同角色的贡献权重

## 🔧 **主要修改**

### 1. 项目创建逻辑修改

#### 前端修改 (`src/pages/projects/new.tsx`)
- ✅ 添加了项目负责人选择字段
- ✅ 使用 `UserSelector` 组件支持单选模式
- ✅ 添加前端验证确保选择了项目负责人
- ✅ 更新表单提交逻辑，包含 `leaderId` 参数

#### 后端API修改 (`src/pages/api/projects/index.ts`)
- ✅ 接收 `leaderId` 参数
- ✅ 验证项目负责人必须指定
- ✅ 将项目的 `owner` 设置为指定的负责人而不是创建者

### 2. 贡献度系统重构

#### 后端计算逻辑 (`src/pages/api/team/stats.ts`)
- ✅ 将 `activityScore` 重命名为 `contributionScore`
- ✅ 重新设计贡献度计算公式：
  - **项目负责人贡献** (35%): 每个项目15分，最高35分
  - **任务完成贡献** (30%): 每完成任务6分，最高30分
  - **任务活跃度贡献** (20%): 每次更新7分，最高20分
  - **协作贡献** (10%): 每条消息0.3分，最高10分
  - **资源贡献** (5%): 每个文件1分，最高5分
- ✅ 添加角色权重调整：
  - 管理员 (ADMIN): ×1.2
  - 团队负责人 (LEADER): ×1.1
  - 项目负责人: ×1.05
  - 普通成员: ×1.0

#### 导出功能更新 (`src/pages/api/team/export.ts`)
- ✅ 更新CSV导出字段名称
- ✅ 应用新的贡献度计算逻辑
- ✅ 添加角色标识字段

#### 前端界面更新
- ✅ 主页面 (`src/pages/index.tsx`): "团队活跃度" → "团队贡献度"
- ✅ 团队页面 (`src/pages/team.tsx`): 更新排序和显示逻辑
- ✅ 组件重命名: `ActivityScoreInfo` → `ContributionScoreInfo`
- ✅ 成员卡片 (`src/components/TeamMemberCard.tsx`): 更新显示文本和评级标准

### 3. 贡献度评级标准更新

#### 新的评级标准
- **80-100分**: 杰出贡献 (绿色)
- **60-79分**: 优秀贡献 (蓝色)
- **40-59分**: 良好贡献 (黄色)
- **20-39分**: 一般贡献 (橙色)
- **0-19分**: 待提升 (红色)

## 🎯 **功能特点**

### 项目创建流程
1. 老师/管理员创建项目时必须指定项目负责人
2. 项目负责人可以是任何团队成员
3. 创建者不会自动成为项目负责人
4. 支持同时添加项目成员

### 贡献度计算
1. **角色导向**: 不同角色有不同的贡献权重
2. **多维度评估**: 综合考虑项目管理、任务完成、协作等多个方面
3. **时间敏感**: 重点关注近期活动和贡献
4. **公平性**: 根据角色职责调整评分标准

## 📊 **数据库影响**

- ✅ 无需修改数据库结构
- ✅ 现有数据完全兼容
- ✅ 项目的 `ownerId` 字段现在指向指定的负责人

## 🔍 **测试建议**

### 项目创建测试
1. 验证必须选择项目负责人才能创建项目
2. 确认项目负责人正确设置
3. 测试项目成员添加功能
4. 验证权限分配正确

### 贡献度计算测试
1. 创建不同角色的测试用户
2. 分配不同类型的任务和项目
3. 验证贡献度计算结果
4. 测试排序和筛选功能

## 🚀 **部署注意事项**

1. **向后兼容**: 所有修改都保持向后兼容
2. **渐进式更新**: 可以逐步部署，不影响现有功能
3. **数据迁移**: 无需特殊的数据迁移步骤
4. **用户培训**: 建议向用户说明新的项目创建流程

## 📝 **后续优化建议**

1. **权重配置**: 考虑将贡献度权重设置为可配置参数
2. **历史数据**: 添加贡献度历史趋势分析
3. **通知系统**: 项目负责人变更时发送通知
4. **报表功能**: 增加贡献度分析报表
