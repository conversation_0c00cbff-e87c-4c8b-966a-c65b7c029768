// 登录页面
const { userApi } = require('../../utils/api.js');
const { validateEmail, validatePassword } = require('../../utils/util.js');
const app = getApp();

Page({
  data: {
    email: '',
    password: '',
    loading: false,
    showPassword: false,
    rememberMe: false
  },

  onLoad(options) {
    // 检查是否已登录
    if (app.isLoggedIn()) {
      wx.switchTab({
        url: '/pages/index/index'
      });
      return;
    }

    // 从存储中恢复登录信息
    const savedEmail = wx.getStorageSync('savedEmail');
    const rememberMe = wx.getStorageSync('rememberMe');
    
    if (rememberMe && savedEmail) {
      this.setData({
        email: savedEmail,
        rememberMe: true
      });
    }
  },

  // 输入邮箱
  onEmailInput(e) {
    this.setData({
      email: e.detail.value
    });
  },

  // 输入密码
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 切换密码显示
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 切换记住我
  toggleRememberMe() {
    this.setData({
      rememberMe: !this.data.rememberMe
    });
  },

  // 表单验证
  validateForm() {
    const { email, password } = this.data;

    if (!email.trim()) {
      wx.showToast({
        title: '请输入邮箱',
        icon: 'error'
      });
      return false;
    }

    if (!validateEmail(email)) {
      wx.showToast({
        title: '邮箱格式不正确',
        icon: 'error'
      });
      return false;
    }

    if (!password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'error'
      });
      return false;
    }

    if (password.length < 6) {
      wx.showToast({
        title: '密码长度至少6位',
        icon: 'error'
      });
      return false;
    }

    return true;
  },

  // 登录
  async handleLogin() {
    if (!this.validateForm()) {
      return;
    }

    const { email, password, rememberMe } = this.data;

    this.setData({ loading: true });

    try {
      // 模拟登录成功（实际项目中应该调用真实API）
      const mockResponse = {
        user: {
          id: '1',
          name: '测试用户',
          email: email.trim(),
          role: 'MEMBER',
          department: '研发部',
          avatar: ''
        },
        token: 'mock-token-' + Date.now()
      };

      // 保存用户信息和token
      app.setUserInfo(mockResponse.user, mockResponse.token);

      // 保存登录信息
      if (rememberMe) {
        wx.setStorageSync('savedEmail', email);
        wx.setStorageSync('rememberMe', true);
      } else {
        wx.removeStorageSync('savedEmail');
        wx.removeStorageSync('rememberMe');
      }

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);

    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 跳转到注册页面
  goToRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  // 忘记密码
  forgotPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请联系管理员重置密码',
      showCancel: false
    });
  },

  // 微信登录
  wechatLogin() {
    wx.showToast({
      title: '微信登录功能开发中',
      icon: 'none'
    });
  }
});
