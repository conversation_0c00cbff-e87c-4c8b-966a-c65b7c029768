import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { format } from 'date-fns';
import FileCard from '@/components/FileCard';
import SimpleFileUpload from '@/components/SimpleFileUpload';

export default function TaskDetail() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { id } = router.query;

  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('details'); // 'details', 'files'
  const [canEdit, setCanEdit] = useState(false);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取任务详情
  useEffect(() => {
    if (status === 'authenticated' && id) {
      fetchTaskDetails();
    }
  }, [status, id]);

  const fetchTaskDetails = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/tasks/${id}`);

      if (!response.ok) {
        throw new Error('获取任务详情失败');
      }

      const data = await response.json();
      setTask(data);

      // 检查当前用户是否可以编辑任务
      // 任务负责人、项目所有者或管理员可以编辑
      if (
        session?.user?.id === data.assignee?.id ||
        session?.user?.id === data.project?.ownerId ||
        session?.user?.role === 'ADMIN'
      ) {
        setCanEdit(true);
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      setError('获取任务详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 更新任务状态
  const updateTaskStatus = async (newStatus) => {
    if (!canEdit) return;

    try {
      const response = await fetch(`/api/tasks/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('更新任务状态失败');
      }

      const updatedTask = await response.json();
      setTask(updatedTask);
    } catch (error) {
      console.error('更新任务状态失败:', error);
      setError('更新任务状态失败，请稍后再试');
    }
  };

  // 处理文件上传完成
  const handleUploadComplete = (file) => {
    // 更新任务文件列表
    setTask(prevTask => ({
      ...prevTask,
      files: [...(prevTask.files || []), file],
    }));

    // 隐藏上传表单
    setShowUploadForm(false);
  };

  // 删除任务
  const handleDeleteTask = async () => {
    if (!task) return;

    setDeleting(true);
    try {
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '删除任务失败');
      }

      // 删除成功，重定向到项目页面
      router.push(`/projects/${task.project.id}`);
    } catch (error) {
      console.error('删除任务失败:', error);
      setError(error instanceof Error ? error.message : '删除任务失败，请稍后再试');
    } finally {
      setDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  // 格式化日期
  const formatDate = (date) => {
    if (!date) return '未设置';
    return format(new Date(date), 'yyyy-MM-dd');
  };

  // 获取任务状态文本
  const getStatusText = (status) => {
    switch (status) {
      case 'TODO':
        return '待处理';
      case 'IN_PROGRESS':
        return '进行中';
      case 'REVIEW':
        return '审核中';
      case 'COMPLETED':
        return '已完成';
      default:
        return status;
    }
  };

  // 获取状态标签样式
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'TODO':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'REVIEW':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // 获取优先级文本
  const getPriorityText = (priority) => {
    switch (priority) {
      case 'LOW':
        return '低';
      case 'MEDIUM':
        return '中';
      case 'HIGH':
        return '高';
      case 'URGENT':
        return '紧急';
      default:
        return priority;
    }
  };

  // 获取优先级标签样式
  const getPriorityBadgeClass = (priority) => {
    switch (priority) {
      case 'LOW':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'URGENT':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 加载中
  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="mt-4">
          <Link href="/tasks" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回任务列表
          </Link>
        </div>
      </div>
    );
  }

  // 任务不存在
  if (!task) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">任务不存在或您没有权限访问</span>
        </div>
        <div className="mt-4">
          <Link href="/tasks" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回任务列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* 任务标题和操作按钮 */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <div className="flex flex-wrap items-center gap-2">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {task.title}
            </h1>
            <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(task.status)}`}>
              {getStatusText(task.status)}
            </span>
            <span className={`px-2 py-1 text-xs rounded-full ${getPriorityBadgeClass(task.priority)}`}>
              优先级: {getPriorityText(task.priority)}
            </span>
          </div>
          <div className="mt-1">
            {task.project && (
              <Link href={`/projects/${task.project.id}`} className="text-primary-600 dark:text-primary-400 hover:underline">
                {task.project.title}
              </Link>
            )}
          </div>
        </div>

        {canEdit && (
          <div className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-3">
            {/* 编辑按钮 */}
            <Link
              href={`/tasks/${id}/edit`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              编辑任务
            </Link>

            {/* 删除按钮 */}
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-lg shadow-sm text-sm font-medium text-red-700 dark:text-red-200 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              删除任务
            </button>

            {/* 状态更新按钮组 */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => updateTaskStatus('TODO')}
                className={`inline-flex items-center px-3 py-2 rounded-lg text-xs font-medium transition-colors ${
                  task.status === 'TODO'
                    ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 ring-2 ring-gray-300 dark:ring-gray-600'
                    : 'bg-gray-50 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'
                }`}
              >
                <div className="w-2 h-2 rounded-full bg-gray-400 mr-2"></div>
                待处理
              </button>

              <button
                onClick={() => updateTaskStatus('IN_PROGRESS')}
                className={`inline-flex items-center px-3 py-2 rounded-lg text-xs font-medium transition-colors ${
                  task.status === 'IN_PROGRESS'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 ring-2 ring-blue-300 dark:ring-blue-600'
                    : 'bg-blue-50 text-blue-600 dark:bg-gray-800 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900 border border-blue-200 dark:border-gray-600'
                }`}
              >
                <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                进行中
              </button>

              <button
                onClick={() => updateTaskStatus('REVIEW')}
                className={`inline-flex items-center px-3 py-2 rounded-lg text-xs font-medium transition-colors ${
                  task.status === 'REVIEW'
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 ring-2 ring-yellow-300 dark:ring-yellow-600'
                    : 'bg-yellow-50 text-yellow-600 dark:bg-gray-800 dark:text-yellow-400 hover:bg-yellow-100 dark:hover:bg-yellow-900 border border-yellow-200 dark:border-gray-600'
                }`}
              >
                <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                待审核
              </button>

              <button
                onClick={() => updateTaskStatus('COMPLETED')}
                className={`inline-flex items-center px-3 py-2 rounded-lg text-xs font-medium transition-colors ${
                  task.status === 'COMPLETED'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 ring-2 ring-green-300 dark:ring-green-600'
                    : 'bg-green-50 text-green-600 dark:bg-gray-800 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900 border border-green-200 dark:border-gray-600'
                }`}
              >
                <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                已完成
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 标签页导航 */}
      <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('details')}
            className={`py-4 px-1 text-center border-b-2 font-medium text-sm ${
              activeTab === 'details'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            任务详情
          </button>
          <button
            onClick={() => setActiveTab('files')}
            className={`py-4 px-1 text-center border-b-2 font-medium text-sm ${
              activeTab === 'files'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            相关文件 ({task.files?.length || 0})
          </button>
        </nav>
      </div>

      {/* 任务详情标签页 */}
      {activeTab === 'details' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">描述</h3>
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                {task.description || '无描述'}
              </p>
            </div>

            {/* 任务成员信息 */}
            <div className="space-y-4">
              {/* 主要负责人 */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">主要负责人</h3>
                {task.assignee ? (
                  <div className="flex items-center space-x-3">
                    <img
                      src={task.assignee.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(task.assignee.name)}&background=6366f1&color=fff&size=40`}
                      alt={task.assignee.name}
                      className="w-8 h-8 rounded-full"
                    />
                    <div>
                      <Link
                        href={`/profile/${task.assignee.id}`}
                        className="text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 font-medium"
                      >
                        {task.assignee.name}
                      </Link>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{task.assignee.email}</p>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400">未分配</p>
                )}
              </div>

              {/* 协作成员 */}
              {task.assignees && task.assignees.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                    协作成员 ({task.assignees.length})
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    {task.assignees.map((assignee) => (
                      <div key={assignee.id} className="flex items-center space-x-2 bg-gray-50 dark:bg-gray-700 rounded-lg px-3 py-2">
                        <img
                          src={assignee.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(assignee.name)}&background=6366f1&color=fff&size=32`}
                          alt={assignee.name}
                          className="w-6 h-6 rounded-full"
                        />
                        <Link
                          href={`/profile/${assignee.id}`}
                          className="text-sm text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 font-medium"
                        >
                          {assignee.name}
                        </Link>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">截止日期</h3>
                <p className="mt-1 text-gray-900 dark:text-gray-100">
                  {formatDate(task.dueDate)}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">创建时间</h3>
                <p className="mt-1 text-gray-900 dark:text-gray-100">
                  {formatDate(task.createdAt)}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">最后更新</h3>
                <p className="mt-1 text-gray-900 dark:text-gray-100">
                  {formatDate(task.updatedAt)}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 文件标签页 */}
      {activeTab === 'files' && (
        <div>
          <div className="mb-6 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">相关文件</h3>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowUploadForm(!showUploadForm)}
                className="btn btn-sm btn-secondary"
              >
                {showUploadForm ? '隐藏上传区' : '显示上传区'}
              </button>
            </div>
          </div>

          {/* 拖拽上传区域 - 始终显示 */}
          <div className={`mb-6 transition-all duration-300 ${showUploadForm ? 'opacity-100' : 'opacity-60 hover:opacity-100'}`}>
            <SimpleFileUpload
              projectId={task.project?.id}
              taskId={task.id}
              onUploadComplete={handleUploadComplete}
              placeholder="拖拽文件到此处或点击选择文件上传到任务"
              className={showUploadForm ? 'bg-white dark:bg-gray-800 rounded-lg shadow-md' : 'bg-gray-50 dark:bg-gray-700 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600'}
            />
          </div>

          {task.files && task.files.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {task.files.map(file => (
                <FileCard key={file.id} file={file} />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700/50">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">暂无相关文件</h3>
              <p className="mt-2 text-gray-500 dark:text-gray-400">
                拖拽文件到上方区域或使用上传按钮添加任务相关文件
              </p>
            </div>
          )}
        </div>
      )}

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
                <svg className="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mt-4">
                确认删除任务
              </h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  您确定要删除任务 "<span className="font-medium">{task?.title}</span>" 吗？
                </p>
                <p className="text-sm text-red-600 dark:text-red-400 mt-2">
                  此操作无法撤销，相关文件和数据也将被删除。
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    disabled={deleting}
                    className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-base font-medium rounded-md shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleDeleteTask}
                    disabled={deleting}
                    className="flex-1 px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {deleting ? (
                      <div className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        删除中...
                      </div>
                    ) : (
                      '确认删除'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
