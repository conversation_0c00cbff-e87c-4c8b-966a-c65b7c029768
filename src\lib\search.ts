import prisma from './prisma';
import { AdvancedPermissions, Permission } from './permissions';

// 搜索类型
export enum SearchType {
  ALL = 'all',
  PROJECTS = 'projects',
  TASKS = 'tasks',
  FILES = 'files',
  USERS = 'users',
  MESSAGES = 'messages',
}

// 搜索过滤器
export interface SearchFilters {
  type?: SearchType;
  status?: string[];
  priority?: string[];
  assigneeIds?: string[];
  projectIds?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  tags?: string[];
  fileTypes?: string[];
}

// 搜索结果接口
export interface SearchResult {
  id: string;
  type: SearchType;
  title: string;
  description?: string;
  url: string;
  relevanceScore: number;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface SearchResponse {
  query: string;
  filters: SearchFilters;
  results: SearchResult[];
  totalCount: number;
  facets: {
    types: Array<{ type: SearchType; count: number }>;
    statuses: Array<{ status: string; count: number }>;
    priorities: Array<{ priority: string; count: number }>;
    projects: Array<{ id: string; title: string; count: number }>;
  };
  suggestions: string[];
}

// 高级搜索服务
export class AdvancedSearchService {
  private permissions: AdvancedPermissions;

  constructor(permissions: AdvancedPermissions) {
    this.permissions = permissions;
  }

  // 执行搜索
  async search(
    query: string,
    filters: SearchFilters = {},
    limit: number = 20,
    offset: number = 0
  ): Promise<SearchResponse> {
    const results: SearchResult[] = [];
    const facets = {
      types: [] as Array<{ type: SearchType; count: number }>,
      statuses: [] as Array<{ status: string; count: number }>,
      priorities: [] as Array<{ priority: string; count: number }>,
      projects: [] as Array<{ id: string; title: string; count: number }>,
    };

    // 根据搜索类型执行不同的搜索
    if (!filters.type || filters.type === SearchType.ALL || filters.type === SearchType.PROJECTS) {
      const projectResults = await this.searchProjects(query, filters);
      results.push(...projectResults);
      if (projectResults.length > 0) {
        facets.types.push({ type: SearchType.PROJECTS, count: projectResults.length });
      }
    }

    if (!filters.type || filters.type === SearchType.ALL || filters.type === SearchType.TASKS) {
      const taskResults = await this.searchTasks(query, filters);
      results.push(...taskResults);
      if (taskResults.length > 0) {
        facets.types.push({ type: SearchType.TASKS, count: taskResults.length });
      }
    }

    if (!filters.type || filters.type === SearchType.ALL || filters.type === SearchType.FILES) {
      const fileResults = await this.searchFiles(query, filters);
      results.push(...fileResults);
      if (fileResults.length > 0) {
        facets.types.push({ type: SearchType.FILES, count: fileResults.length });
      }
    }

    if (!filters.type || filters.type === SearchType.ALL || filters.type === SearchType.USERS) {
      const userResults = await this.searchUsers(query, filters);
      results.push(...userResults);
      if (userResults.length > 0) {
        facets.types.push({ type: SearchType.USERS, count: userResults.length });
      }
    }

    // 按相关性排序
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    // 分页
    const paginatedResults = results.slice(offset, offset + limit);

    // 生成搜索建议
    const suggestions = await this.generateSuggestions(query);

    return {
      query,
      filters,
      results: paginatedResults,
      totalCount: results.length,
      facets,
      suggestions,
    };
  }

  // 搜索项目
  private async searchProjects(query: string, filters: SearchFilters): Promise<SearchResult[]> {
    const whereClause: any = {};

    // 添加状态过滤
    if (filters.status && filters.status.length > 0) {
      whereClause.status = { in: filters.status };
    }

    // 添加项目ID过滤
    if (filters.projectIds && filters.projectIds.length > 0) {
      whereClause.id = { in: filters.projectIds };
    }

    // 添加日期范围过滤
    if (filters.dateRange) {
      whereClause.createdAt = {
        gte: filters.dateRange.start,
        lte: filters.dateRange.end,
      };
    }

    const allProjects = await prisma.project.findMany({
      where: whereClause,
      include: {
        owner: { select: { id: true, name: true } },
        members: { select: { id: true, name: true } },
        _count: { select: { tasks: true, files: true } },
      },
    });

    // 在应用层进行大小写不敏感的搜索
    const queryLower = query.toLowerCase();
    const filteredProjects = allProjects.filter(project => {
      const titleMatch = project.title.toLowerCase().includes(queryLower);
      const descMatch = project.description?.toLowerCase().includes(queryLower) || false;
      return titleMatch || descMatch;
    });

    const results: SearchResult[] = [];

    for (const project of filteredProjects) {
      // 检查权限
      if (await this.permissions.hasProjectPermission(Permission.PROJECT_READ, project.id)) {
        const relevanceScore = this.calculateRelevanceScore(query, project.title, project.description);

        results.push({
          id: project.id,
          type: SearchType.PROJECTS,
          title: project.title,
          description: project.description || undefined,
          url: `/projects/${project.id}`,
          relevanceScore,
          metadata: {
            status: project.status,
            progress: project.progress,
            owner: project.owner,
            memberCount: project.members.length,
            taskCount: project._count.tasks,
            fileCount: project._count.files,
          },
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
        });
      }
    }

    return results;
  }

  // 搜索任务
  private async searchTasks(query: string, filters: SearchFilters): Promise<SearchResult[]> {
    const whereClause: any = {};

    // 添加状态过滤
    if (filters.status && filters.status.length > 0) {
      whereClause.status = { in: filters.status };
    }

    // 添加优先级过滤
    if (filters.priority && filters.priority.length > 0) {
      whereClause.priority = { in: filters.priority };
    }

    // 添加负责人过滤
    if (filters.assigneeIds && filters.assigneeIds.length > 0) {
      whereClause.OR = [
        { assigneeId: { in: filters.assigneeIds } },
        { assignees: { some: { id: { in: filters.assigneeIds } } } },
      ];
    }

    // 添加项目过滤
    if (filters.projectIds && filters.projectIds.length > 0) {
      whereClause.projectId = { in: filters.projectIds };
    }

    const allTasks = await prisma.task.findMany({
      where: whereClause,
      include: {
        project: { select: { id: true, title: true } },
        assignee: { select: { id: true, name: true } },
        assignees: { select: { id: true, name: true } },
      },
    });

    // 在应用层进行大小写不敏感的搜索
    const queryLower = query.toLowerCase();
    const filteredTasks = allTasks.filter(task => {
      const titleMatch = task.title.toLowerCase().includes(queryLower);
      const descMatch = task.description?.toLowerCase().includes(queryLower) || false;
      return titleMatch || descMatch;
    });

    const results: SearchResult[] = [];

    for (const task of filteredTasks) {
      // 检查权限
      if (await this.permissions.hasTaskPermission(Permission.TASK_READ, task.id)) {
        const relevanceScore = this.calculateRelevanceScore(query, task.title, task.description);

        results.push({
          id: task.id,
          type: SearchType.TASKS,
          title: task.title,
          description: task.description || undefined,
          url: `/tasks/${task.id}`,
          relevanceScore,
          metadata: {
            status: task.status,
            priority: task.priority,
            project: task.project,
            assignee: task.assignee,
            assignees: task.assignees,
            dueDate: task.dueDate,
          },
          createdAt: task.createdAt,
          updatedAt: task.updatedAt,
        });
      }
    }

    return results;
  }

  // 搜索文件
  private async searchFiles(query: string, filters: SearchFilters): Promise<SearchResult[]> {
    const whereClause: any = {};

    // 添加文件类型过滤
    if (filters.fileTypes && filters.fileTypes.length > 0) {
      whereClause.type = { in: filters.fileTypes };
    }

    // 添加项目过滤
    if (filters.projectIds && filters.projectIds.length > 0) {
      whereClause.projectId = { in: filters.projectIds };
    }

    const allFiles = await prisma.file.findMany({
      where: whereClause,
      include: {
        uploader: { select: { id: true, name: true } },
        project: { select: { id: true, title: true } },
        task: { select: { id: true, title: true } },
      },
    });

    // 在应用层进行大小写不敏感的搜索
    const queryLower = query.toLowerCase();
    const filteredFiles = allFiles.filter(file => {
      const nameMatch = file.name.toLowerCase().includes(queryLower);
      const descMatch = file.description?.toLowerCase().includes(queryLower) || false;
      return nameMatch || descMatch;
    });

    const results: SearchResult[] = [];

    for (const file of filteredFiles) {
      // 检查权限
      if (await this.permissions.hasFilePermission(Permission.FILE_READ, file.id)) {
        const relevanceScore = this.calculateRelevanceScore(query, file.name, file.description);

        results.push({
          id: file.id,
          type: SearchType.FILES,
          title: file.name,
          description: file.description || undefined,
          url: `/files/${file.id}`,
          relevanceScore,
          metadata: {
            type: file.type,
            size: file.size,
            uploader: file.uploader,
            project: file.project,
            task: file.task,
          },
          createdAt: file.createdAt,
          updatedAt: file.updatedAt,
        });
      }
    }

    return results;
  }

  // 搜索用户
  private async searchUsers(query: string, filters: SearchFilters): Promise<SearchResult[]> {
    // 只有管理员可以搜索用户
    if (!this.permissions.hasPermission(Permission.USER_MANAGE)) {
      return [];
    }

    const allUsers = await prisma.user.findMany({
      where: {
        status: 'APPROVED', // 只搜索已批准的用户
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        department: true,
        position: true,
        avatar: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // 在应用层进行大小写不敏感的搜索
    const queryLower = query.toLowerCase();
    const users = allUsers.filter(user => {
      const nameMatch = user.name.toLowerCase().includes(queryLower);
      const emailMatch = user.email.toLowerCase().includes(queryLower);
      const deptMatch = user.department?.toLowerCase().includes(queryLower) || false;
      const posMatch = user.position?.toLowerCase().includes(queryLower) || false;
      return nameMatch || emailMatch || deptMatch || posMatch;
    });

    return users.map(user => {
      const relevanceScore = this.calculateRelevanceScore(
        query,
        user.name,
        `${user.email} ${user.department} ${user.position}`
      );

      return {
        id: user.id,
        type: SearchType.USERS,
        title: user.name,
        description: `${user.department} - ${user.position}`,
        url: `/team/${user.id}`,
        relevanceScore,
        metadata: {
          email: user.email,
          role: user.role,
          department: user.department,
          position: user.position,
          avatar: user.avatar,
        },
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    });
  }

  // 计算相关性分数
  private calculateRelevanceScore(query: string, title: string, description?: string | null): number {
    const queryLower = query.toLowerCase();
    const titleLower = title.toLowerCase();
    const descriptionLower = description?.toLowerCase() || '';

    let score = 0;

    // 标题完全匹配
    if (titleLower === queryLower) {
      score += 100;
    }
    // 标题包含查询
    else if (titleLower.includes(queryLower)) {
      score += 80;
    }
    // 标题单词匹配
    else {
      const queryWords = queryLower.split(' ');
      const titleWords = titleLower.split(' ');
      const matchingWords = queryWords.filter(word => titleWords.some(titleWord => titleWord.includes(word)));
      score += (matchingWords.length / queryWords.length) * 60;
    }

    // 描述匹配
    if (description) {
      if (descriptionLower.includes(queryLower)) {
        score += 20;
      } else {
        const queryWords = queryLower.split(' ');
        const descriptionWords = descriptionLower.split(' ');
        const matchingWords = queryWords.filter(word => descriptionWords.some(descWord => descWord.includes(word)));
        score += (matchingWords.length / queryWords.length) * 10;
      }
    }

    return Math.min(score, 100);
  }

  // 生成搜索建议
  private async generateSuggestions(query: string): Promise<string[]> {
    // 简单的建议生成逻辑，可以根据需要扩展
    const suggestions: string[] = [];
    const queryLower = query.toLowerCase();

    // 基于项目标题的建议
    const projects = await prisma.project.findMany({
      select: { title: true },
      take: 20,
    });

    const matchingProjects = projects.filter(p =>
      p.title.toLowerCase().includes(queryLower)
    ).slice(0, 3);

    suggestions.push(...matchingProjects.map(p => p.title));

    // 基于任务标题的建议
    const tasks = await prisma.task.findMany({
      select: { title: true },
      take: 20,
    });

    const matchingTasks = tasks.filter(t =>
      t.title.toLowerCase().includes(queryLower)
    ).slice(0, 3);

    suggestions.push(...matchingTasks.map(t => t.title));

    return Array.from(new Set(suggestions)).slice(0, 5);
  }
}
