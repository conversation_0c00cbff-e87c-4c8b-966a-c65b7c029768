import { useState } from 'react';

export default function ContributionScoreInfo() {
  const [showInfo, setShowInfo] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setShowInfo(!showInfo)}
        className="inline-flex items-center text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
      >
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        贡献度说明
      </button>

      {showInfo && (
        <div className="absolute top-full left-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-10">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                贡献度计算说明
              </h4>
              <button
                onClick={() => setShowInfo(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-2">
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">
                贡献度分数基于以下5个维度综合计算（总分100分）：
              </p>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                    项目负责人贡献
                  </span>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">35%</span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-500 ml-4">
                  项目负责人每个项目得15分，最高35分
                </p>

                <div className="flex items-center justify-between">
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    任务完成贡献
                  </span>
                  <span className="text-green-600 dark:text-green-400 font-medium">30%</span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-500 ml-4">
                  每完成一个任务得6分，最高30分
                </p>

                <div className="flex items-center justify-between">
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                    任务活跃度贡献
                  </span>
                  <span className="text-purple-600 dark:text-purple-400 font-medium">20%</span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-500 ml-4">
                  每次任务更新得7分，最高20分
                </p>

                <div className="flex items-center justify-between">
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                    协作贡献
                  </span>
                  <span className="text-yellow-600 dark:text-yellow-400 font-medium">10%</span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-500 ml-4">
                  每条消息得0.3分，最高10分
                </p>

                <div className="flex items-center justify-between">
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                    资源贡献
                  </span>
                  <span className="text-red-600 dark:text-red-400 font-medium">5%</span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-500 ml-4">
                  每上传一个文件得1分，最高5分
                </p>

              </div>

              <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-3">
                <p className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">
                  角色权重调整：
                </p>
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span>管理员 (ADMIN)</span>
                    <span className="text-blue-600 dark:text-blue-400">×1.2</span>
                  </div>
                  <div className="flex justify-between">
                    <span>团队负责人 (LEADER)</span>
                    <span className="text-green-600 dark:text-green-400">×1.1</span>
                  </div>
                  <div className="flex justify-between">
                    <span>项目负责人</span>
                    <span className="text-purple-600 dark:text-purple-400">×1.05</span>
                  </div>
                  <div className="flex justify-between">
                    <span>普通成员</span>
                    <span className="text-gray-600 dark:text-gray-400">×1.0</span>
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-3">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="font-medium text-green-600 dark:text-green-400">80-100分</span>
                    <span className="text-gray-500 dark:text-gray-500 ml-1">杰出贡献</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-600 dark:text-blue-400">60-79分</span>
                    <span className="text-gray-500 dark:text-gray-500 ml-1">优秀贡献</span>
                  </div>
                  <div>
                    <span className="font-medium text-yellow-600 dark:text-yellow-400">40-59分</span>
                    <span className="text-gray-500 dark:text-gray-500 ml-1">良好贡献</span>
                  </div>
                  <div>
                    <span className="font-medium text-orange-600 dark:text-orange-400">20-39分</span>
                    <span className="text-gray-500 dark:text-gray-500 ml-1">一般贡献</span>
                  </div>
                </div>
                <div className="mt-1">
                  <span className="font-medium text-red-600 dark:text-red-400">0-19分</span>
                  <span className="text-gray-500 dark:text-gray-500 ml-1">待提升</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
