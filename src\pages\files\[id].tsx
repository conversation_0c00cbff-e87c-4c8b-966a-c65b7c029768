import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { format } from 'date-fns';
import { File } from '@/types';

export default function FileDetail() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { id } = router.query;

  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [canEdit, setCanEdit] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取文件详情
  useEffect(() => {
    if (status === 'authenticated' && id) {
      fetchFileDetails();
    }
  }, [status, id]);

  const fetchFileDetails = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/files/${id}`);

      if (!response.ok) {
        throw new Error('获取文件详情失败');
      }

      const data = await response.json();
      setFile(data);

      // 检查当前用户是否可以编辑文件
      // 文件上传者、项目所有者或管理员可以编辑
      if (
        session?.user?.id === data.uploader.id ||
        (data.project && session?.user?.id === data.project.ownerId) ||
        session?.user?.role === 'ADMIN'
      ) {
        setCanEdit(true);
      }
    } catch (error) {
      console.error('获取文件详情失败:', error);
      setError('获取文件详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 删除文件
  const deleteFile = async () => {
    if (!canEdit) return;

    setIsDeleting(true);
    setError('');

    try {
      const response = await fetch(`/api/files/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('删除文件失败');
      }

      // 删除成功后跳转到文件列表页
      router.push('/files');
    } catch (error) {
      console.error('删除文件失败:', error);
      setError('删除文件失败，请稍后再试');
      setIsDeleting(false);
    }
  };

  // 格式化日期
  const formatDate = (date: any) => {
    if (!date) return '';
    return format(new Date(date), 'yyyy-MM-dd HH:mm:ss');
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件图标
  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      );
    } else if (type.startsWith('video/')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      );
    } else if (type === 'application/pdf') {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      );
    } else if (type.includes('word') || type.includes('document')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    } else if (type.includes('excel') || type.includes('spreadsheet')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    } else {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      );
    }
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 加载中
  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="mt-4">
          <Link href="/files" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回文件列表
          </Link>
        </div>
      </div>
    );
  }

  // 文件不存在
  if (!file) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">文件不存在或您没有权限访问</span>
        </div>
        <div className="mt-4">
          <Link href="/files" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回文件列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* 文件标题和操作按钮 */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {file.name}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            上传者: {file.uploader.name} | 上传时间: {formatDate(file.createdAt)}
          </p>
        </div>

        <div className="mt-4 md:mt-0 flex space-x-2">
          <a
            href={`/api/files/${file.id}?download=true`}
            className="btn btn-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            下载
          </a>

          {canEdit && (
            <>
              <Link href={`/files/${id}/edit`} className="btn btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                编辑
              </Link>

              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="btn btn-error"
                disabled={isDeleting}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                {isDeleting ? '删除中...' : '删除'}
              </button>
            </>
          )}
        </div>
      </div>

      {/* 文件详情 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex flex-col md:flex-row">
          <div className="md:w-1/3 flex justify-center items-start mb-6 md:mb-0">
            {getFileIcon(file.type)}
          </div>

          <div className="md:w-2/3">
            <h2 className="text-lg font-semibold mb-4">文件信息</h2>

            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">文件名</h3>
                <p className="mt-1">{file.name}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">文件类型</h3>
                <p className="mt-1">{file.type}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">文件大小</h3>
                <p className="mt-1">{formatFileSize(file.size)}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">上传时间</h3>
                <p className="mt-1">{formatDate(file.createdAt)}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">上传者</h3>
                <p className="mt-1">{file.uploader.name}</p>
              </div>

              {file.description && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">描述</h3>
                  <p className="mt-1 whitespace-pre-line">{file.description}</p>
                </div>
              )}

              {file.project && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">关联项目</h3>
                  <p className="mt-1">
                    <Link href={`/projects/${file.project.id}`} className="text-primary-600 dark:text-primary-400 hover:underline">
                      {file.project.title}
                    </Link>
                  </p>
                </div>
              )}

              {file.task && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">关联任务</h3>
                  <p className="mt-1">
                    <Link href={`/tasks/${file.task.id}`} className="text-primary-600 dark:text-primary-400 hover:underline">
                      {file.task.title}
                    </Link>
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold mb-4">确认删除</h3>
            <p className="mb-6">您确定要删除文件 "{file.name}" 吗？此操作无法撤销。</p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="btn btn-secondary"
                disabled={isDeleting}
              >
                取消
              </button>
              <button
                onClick={deleteFile}
                className="btn btn-error"
                disabled={isDeleting}
              >
                {isDeleting ? '删除中...' : '确认删除'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
