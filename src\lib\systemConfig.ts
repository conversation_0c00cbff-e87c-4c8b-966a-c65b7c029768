import prisma from './prisma';

// 系统配置类型
export interface SystemConfig {
  // 基础设置
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  adminEmail: string;
  
  // 用户设置
  allowUserRegistration: boolean;
  requireEmailVerification: boolean;
  defaultUserRole: 'MEMBER' | 'GUEST';
  maxUsersPerProject: number;
  
  // 文件设置
  maxFileSize: number; // 字节
  allowedFileTypes: string[];
  maxFilesPerProject: number;
  fileStoragePath: string;
  
  // 通知设置
  enableEmailNotifications: boolean;
  enablePushNotifications: boolean;
  notificationRetentionDays: number;
  
  // 安全设置
  sessionTimeout: number; // 分钟
  maxLoginAttempts: number;
  lockoutDuration: number; // 分钟
  requireStrongPasswords: boolean;
  
  // 系统限制
  maxProjectsPerUser: number;
  maxTasksPerProject: number;
  maxChatMessagesPerDay: number;
  
  // 备份设置
  enableAutoBackup: boolean;
  backupInterval: number; // 小时
  backupRetentionDays: number;
  
  // 维护设置
  maintenanceMode: boolean;
  maintenanceMessage: string;
  
  // 分析设置
  enableAnalytics: boolean;
  analyticsRetentionDays: number;
  
  // API设置
  apiRateLimit: number; // 每分钟请求数
  enableApiLogging: boolean;
}

// 默认配置
const DEFAULT_CONFIG: SystemConfig = {
  // 基础设置
  siteName: 'LabSync',
  siteDescription: '实验室管理系统',
  siteUrl: 'http://localhost:3000',
  adminEmail: '<EMAIL>',
  
  // 用户设置
  allowUserRegistration: true,
  requireEmailVerification: false,
  defaultUserRole: 'MEMBER',
  maxUsersPerProject: 50,
  
  // 文件设置
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedFileTypes: [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain', 'text/csv'
  ],
  maxFilesPerProject: 100,
  fileStoragePath: './uploads',
  
  // 通知设置
  enableEmailNotifications: false,
  enablePushNotifications: true,
  notificationRetentionDays: 30,
  
  // 安全设置
  sessionTimeout: 480, // 8小时
  maxLoginAttempts: 5,
  lockoutDuration: 15, // 15分钟
  requireStrongPasswords: true,
  
  // 系统限制
  maxProjectsPerUser: 20,
  maxTasksPerProject: 200,
  maxChatMessagesPerDay: 500,
  
  // 备份设置
  enableAutoBackup: false,
  backupInterval: 24, // 24小时
  backupRetentionDays: 30,
  
  // 维护设置
  maintenanceMode: false,
  maintenanceMessage: '系统正在维护中，请稍后再试。',
  
  // 分析设置
  enableAnalytics: true,
  analyticsRetentionDays: 90,
  
  // API设置
  apiRateLimit: 100, // 每分钟100次请求
  enableApiLogging: true,
};

// 系统配置管理器
export class SystemConfigManager {
  private static configCache: SystemConfig | null = null;
  private static lastCacheUpdate: number = 0;
  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  // 获取系统配置
  static async getConfig(): Promise<SystemConfig> {
    const now = Date.now();
    
    // 检查缓存是否有效
    if (this.configCache && (now - this.lastCacheUpdate) < this.CACHE_TTL) {
      return this.configCache;
    }

    try {
      // 从数据库加载配置（如果有配置表的话）
      // 目前使用默认配置和环境变量
      const config = await this.loadConfigFromDatabase();
      
      // 合并环境变量
      const mergedConfig = this.mergeWithEnvironmentVariables(config);
      
      // 更新缓存
      this.configCache = mergedConfig;
      this.lastCacheUpdate = now;
      
      return mergedConfig;
    } catch (error) {
      console.error('加载系统配置失败:', error);
      return DEFAULT_CONFIG;
    }
  }

  // 更新系统配置
  static async updateConfig(updates: Partial<SystemConfig>): Promise<SystemConfig> {
    try {
      // 获取当前配置
      const currentConfig = await this.getConfig();
      
      // 合并更新
      const newConfig = { ...currentConfig, ...updates };
      
      // 验证配置
      this.validateConfig(newConfig);
      
      // 保存到数据库（如果有配置表的话）
      await this.saveConfigToDatabase(newConfig);
      
      // 清除缓存
      this.clearCache();
      
      return newConfig;
    } catch (error) {
      console.error('更新系统配置失败:', error);
      throw error;
    }
  }

  // 获取特定配置项
  static async getConfigValue<K extends keyof SystemConfig>(key: K): Promise<SystemConfig[K]> {
    const config = await this.getConfig();
    return config[key];
  }

  // 设置特定配置项
  static async setConfigValue<K extends keyof SystemConfig>(key: K, value: SystemConfig[K]): Promise<void> {
    await this.updateConfig({ [key]: value } as Partial<SystemConfig>);
  }

  // 重置配置为默认值
  static async resetToDefaults(): Promise<SystemConfig> {
    await this.saveConfigToDatabase(DEFAULT_CONFIG);
    this.clearCache();
    return DEFAULT_CONFIG;
  }

  // 清除配置缓存
  static clearCache(): void {
    this.configCache = null;
    this.lastCacheUpdate = 0;
  }

  // 从数据库加载配置
  private static async loadConfigFromDatabase(): Promise<SystemConfig> {
    try {
      // 这里应该从配置表加载，目前返回默认配置
      // 可以创建一个 SystemConfig 表来存储配置
      return DEFAULT_CONFIG;
    } catch (error) {
      console.error('从数据库加载配置失败:', error);
      return DEFAULT_CONFIG;
    }
  }

  // 保存配置到数据库
  private static async saveConfigToDatabase(config: SystemConfig): Promise<void> {
    try {
      // 这里应该保存到配置表
      console.log('保存配置到数据库:', config);
    } catch (error) {
      console.error('保存配置到数据库失败:', error);
      throw error;
    }
  }

  // 合并环境变量
  private static mergeWithEnvironmentVariables(config: SystemConfig): SystemConfig {
    return {
      ...config,
      // 从环境变量覆盖配置
      siteName: process.env.SITE_NAME || config.siteName,
      siteUrl: process.env.SITE_URL || config.siteUrl,
      adminEmail: process.env.ADMIN_EMAIL || config.adminEmail,
      maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '') || config.maxFileSize,
      sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '') || config.sessionTimeout,
      enableAnalytics: process.env.ENABLE_ANALYTICS === 'true' || config.enableAnalytics,
      maintenanceMode: process.env.MAINTENANCE_MODE === 'true' || config.maintenanceMode,
    };
  }

  // 验证配置
  private static validateConfig(config: SystemConfig): void {
    // 验证必需字段
    if (!config.siteName || config.siteName.trim().length === 0) {
      throw new Error('站点名称不能为空');
    }

    if (!config.adminEmail || !this.isValidEmail(config.adminEmail)) {
      throw new Error('管理员邮箱格式无效');
    }

    // 验证数值范围
    if (config.maxFileSize <= 0) {
      throw new Error('最大文件大小必须大于0');
    }

    if (config.sessionTimeout <= 0) {
      throw new Error('会话超时时间必须大于0');
    }

    if (config.maxLoginAttempts <= 0) {
      throw new Error('最大登录尝试次数必须大于0');
    }

    // 验证数组
    if (!Array.isArray(config.allowedFileTypes) || config.allowedFileTypes.length === 0) {
      throw new Error('允许的文件类型不能为空');
    }
  }

  // 验证邮箱格式
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // 获取配置摘要（用于前端显示）
  static async getConfigSummary(): Promise<{
    basic: Partial<SystemConfig>;
    security: Partial<SystemConfig>;
    limits: Partial<SystemConfig>;
    features: Partial<SystemConfig>;
  }> {
    const config = await this.getConfig();

    return {
      basic: {
        siteName: config.siteName,
        siteDescription: config.siteDescription,
        adminEmail: config.adminEmail,
        allowUserRegistration: config.allowUserRegistration,
      },
      security: {
        sessionTimeout: config.sessionTimeout,
        maxLoginAttempts: config.maxLoginAttempts,
        lockoutDuration: config.lockoutDuration,
        requireStrongPasswords: config.requireStrongPasswords,
      },
      limits: {
        maxFileSize: config.maxFileSize,
        maxUsersPerProject: config.maxUsersPerProject,
        maxProjectsPerUser: config.maxProjectsPerUser,
        maxTasksPerProject: config.maxTasksPerProject,
      },
      features: {
        enableEmailNotifications: config.enableEmailNotifications,
        enablePushNotifications: config.enablePushNotifications,
        enableAnalytics: config.enableAnalytics,
        maintenanceMode: config.maintenanceMode,
      },
    };
  }
}
