import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const prisma = new PrismaClient();

async function main() {
  console.log('开始初始化数据库...');

  // 创建管理员用户
  const adminPassword = await hash('123456', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: '张教授',
      email: '<EMAIL>',
      password: adminPassword,
      role: 'ADMIN',
      age: 45,
      department: '计算机科学与技术学院',
      position: '教授/博士生导师',
      bio: '计算机科学与技术学院教授，博士生导师，专注于人工智能、机器学习和数据挖掘领域。',
      phone: '13800138000',
      avatar: '/avatars/default-admin.png',
      status: 'APPROVED',
      approvedAt: new Date(),
    },
  });
  console.log('✅ 创建管理员用户:', admin.name, '(', admin.email, ')');

  // 创建研究员用户1
  const researcher1Password = await hash('123456', 12);
  const researcher1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: '李工程师',
      email: '<EMAIL>',
      password: researcher1Password,
      role: 'MEMBER',
      age: 28,
      department: '计算机科学系',
      position: '软件工程师',
      bio: '专注于机器学习和深度学习技术，具有丰富的数据分析经验。',
      phone: '13800138001',
      avatar: '/avatars/default-user1.png',
      status: 'APPROVED',
      approvedAt: new Date(),
    },
  });
  console.log('✅ 创建研究员用户:', researcher1.name, '(', researcher1.email, ')');

  // 创建研究员用户2
  const researcher2Password = await hash('123456', 12);
  const researcher2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: '王博士',
      email: '<EMAIL>',
      password: researcher2Password,
      role: 'MEMBER',
      age: 32,
      department: '人工智能实验室',
      position: '高级工程师',
      bio: '计算机视觉和图像处理专家，发表多篇高质量学术论文。',
      phone: '13800138002',
      avatar: '/avatars/default-user2.png',
      status: 'APPROVED',
      approvedAt: new Date(),
    },
  });
  console.log('✅ 创建研究员用户:', researcher2.name, '(', researcher2.email, ')');

  // 创建示例项目
  const project1 = await prisma.project.upsert({
    where: { id: 'project1' },
    update: {},
    create: {
      id: 'project1',
      title: '人工智能开发项目',
      description: '基于深度学习的图像识别技术开发，旨在构建高精度的图像分类和目标检测系统。',
      startDate: new Date(),
      endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90天后
      status: 'ACTIVE',
      progress: 0.35,
      owner: {
        connect: { id: admin.id },
      },
      members: {
        connect: [{ id: researcher1.id }, { id: researcher2.id }],
      },
    },
  });
  console.log('✅ 创建示例项目:', project1.title);

  // 创建示例任务
  const task1 = await prisma.task.upsert({
    where: { id: 'task1' },
    update: {},
    create: {
      id: 'task1',
      title: '数据收集与预处理',
      description: '收集图像数据集，包括各种类别的图像样本，并进行数据清洗、标注和预处理工作。',
      dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14天后
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      project: {
        connect: { id: project1.id },
      },
      assignee: {
        connect: { id: researcher1.id },
      },
    },
  });
  console.log('✅ 创建示例任务:', task1.title);

  const task2 = await prisma.task.upsert({
    where: { id: 'task2' },
    update: {},
    create: {
      id: 'task2',
      title: '模型架构设计',
      description: '设计深度学习模型架构，包括卷积神经网络的层次结构、激活函数选择和优化策略。',
      dueDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000), // 21天后
      status: 'TODO',
      priority: 'MEDIUM',
      project: {
        connect: { id: project1.id },
      },
      assignee: {
        connect: { id: researcher2.id },
      },
    },
  });
  console.log('✅ 创建示例任务:', task2.title);

  const task3 = await prisma.task.upsert({
    where: { id: 'task3' },
    update: {},
    create: {
      id: 'task3',
      title: '实验结果分析',
      description: '分析模型训练结果，评估模型性能指标，撰写实验报告和技术文档。',
      dueDate: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000), // 35天后
      status: 'TODO',
      priority: 'LOW',
      project: {
        connect: { id: project1.id },
      },
      assignee: {
        connect: { id: researcher1.id },
      },
    },
  });
  console.log('✅ 创建示例任务:', task3.title);

  // 创建初始邀请码
  const inviteCode1 = await prisma.inviteCode.upsert({
    where: { code: 'LABSYNC-2024-001' },
    update: {},
    create: {
      code: 'LABSYNC-2024-001',
      description: '课题组成员邀请码',
      maxUses: 10,
      usedCount: 0,
      isActive: true,
      createdById: admin.id,
    },
  });
  console.log('✅ 创建邀请码:', inviteCode1.code);

  const inviteCode2 = await prisma.inviteCode.upsert({
    where: { code: 'LABSYNC-2024-002' },
    update: {},
    create: {
      code: 'LABSYNC-2024-002',
      description: '博士生专用邀请码',
      maxUses: 5,
      usedCount: 0,
      isActive: true,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
      createdById: admin.id,
    },
  });
  console.log('✅ 创建邀请码:', inviteCode2.code);

  console.log('✅ 数据库初始化完成！');
  console.log('\n📋 测试账户信息:');
  console.log('1. 管理员账户:');
  console.log('   邮箱: <EMAIL>');
  console.log('   密码: 123456');
  console.log('   姓名: 张教授');
  console.log('   角色: 管理员');
  console.log('   年龄: 45岁');
  console.log('');
  console.log('2. 研究员账户1:');
  console.log('   邮箱: <EMAIL>');
  console.log('   密码: 123456');
  console.log('   姓名: 李研究员');
  console.log('   角色: 普通用户');
  console.log('   年龄: 28岁');
  console.log('');
  console.log('3. 研究员账户2:');
  console.log('   邮箱: <EMAIL>');
  console.log('   密码: 123456');
  console.log('   姓名: 王博士');
  console.log('   角色: 普通用户');
  console.log('   年龄: 32岁');
  console.log('');
  console.log('🎯 示例数据:');
  console.log('- 1个示例项目: 人工智能研究项目');
  console.log('- 3个示例任务: 数据收集、模型设计、结果分析');
  console.log('- 完整的项目成员关系和任务分配');
  console.log('');
  console.log('🔐 邀请码信息:');
  console.log('- LABSYNC-2024-001 (课题组成员，10次使用)');
  console.log('- LABSYNC-2024-002 (博士生专用，5次使用，30天有效期)');
}

main()
  .catch((e) => {
    console.error('数据库初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
