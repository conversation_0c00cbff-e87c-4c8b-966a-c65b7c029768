// 测试多人分配任务的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMultiAssign() {
  try {
    console.log('开始测试多人分配任务...');

    // 获取现有的任务
    const task = await prisma.task.findFirst({
      where: { id: 'task3' },
      include: {
        assignee: true,
        assignees: true,
        project: {
          include: {
            owner: true,
            members: true,
          },
        },
      },
    });

    if (!task) {
      console.log('未找到测试任务');
      return;
    }

    console.log('当前任务信息:', {
      title: task.title,
      assignee: task.assignee?.name,
      assignees: task.assignees.map(a => a.name),
    });

    // 获取项目成员
    const projectMembers = [task.project.owner, ...task.project.members];
    console.log('项目成员:', projectMembers.map(m => ({ id: m.id, name: m.name })));

    // 为任务分配多个成员
    const assigneeIds = projectMembers.slice(0, 2).map(m => m.id); // 分配前两个成员

    const updatedTask = await prisma.task.update({
      where: { id: task.id },
      data: {
        assignees: {
          set: assigneeIds.map(id => ({ id })),
        },
      },
      include: {
        assignee: true,
        assignees: true,
      },
    });

    console.log('更新后的任务信息:', {
      title: updatedTask.title,
      assignee: updatedTask.assignee?.name,
      assignees: updatedTask.assignees.map(a => a.name),
    });

    console.log('多人分配测试完成！');
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMultiAssign();
