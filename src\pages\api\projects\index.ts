import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId, isAdmin } from '@/lib/auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const userId = await getCurrentUserId(req, res);
  const isUserAdmin = await isAdmin(req, res);

  // 处理GET请求 - 获取项目列表
  if (req.method === 'GET') {
    try {
      let projects;

      // 管理员可以查看所有项目
      if (isUserAdmin) {
        projects = await prisma.project.findMany({
          include: {
            owner: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            members: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
                age: true,
                department: true,
                position: true,
              },
            },
            _count: {
              select: {
                tasks: true,
                files: true,
              },
            },
          },
          orderBy: {
            updatedAt: 'desc',
          },
        });
      } else {
        // 普通用户只能查看自己创建的或参与的项目
        projects = await prisma.project.findMany({
          where: {
            OR: [
              { ownerId: userId! },
              {
                members: {
                  some: {
                    id: userId!,
                  },
                },
              },
            ],
          },
          include: {
            owner: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            members: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
                age: true,
                department: true,
                position: true,
              },
            },
            _count: {
              select: {
                tasks: true,
                files: true,
              },
            },
          },
          orderBy: {
            updatedAt: 'desc',
          },
        });
      }

      return res.status(200).json(projects);
    } catch (error) {
      console.error('获取项目列表失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理POST请求 - 创建新项目
  if (req.method === 'POST') {
    const { title, description, startDate, endDate, members, leaderId } = req.body;

    // 验证请求数据
    if (!title || !startDate) {
      return res.status(400).json({ message: '请提供项目标题和开始日期' });
    }

    if (!leaderId) {
      return res.status(400).json({ message: '请指定项目负责人' });
    }

    try {
      // 创建新项目
      const project = await prisma.project.create({
        data: {
          title,
          description,
          startDate: new Date(startDate),
          endDate: endDate ? new Date(endDate) : null,
          owner: {
            connect: { id: leaderId },
          },
          // 如果提供了成员列表，则连接这些成员
          ...(members && members.length > 0
            ? {
                members: {
                  connect: members.map((id: string) => ({ id })),
                },
              }
            : {}),
        },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          members: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              age: true,
              department: true,
              position: true,
            },
          },
        },
      });

      return res.status(201).json(project);
    } catch (error) {
      console.error('创建项目失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
