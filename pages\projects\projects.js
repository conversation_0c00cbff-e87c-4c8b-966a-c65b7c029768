// 项目列表页面
const { projectApi } = require('../../utils/api.js');
const { formatTime, getStatusText, calculateProgress } = require('../../utils/util.js');
const app = getApp();

Page({
  data: {
    projects: [],
    loading: true,
    refreshing: false,
    searchKeyword: '',
    filterStatus: 'ALL',
    statusOptions: [
      { value: 'ALL', label: '全部' },
      { value: 'PLANNING', label: '计划中' },
      { value: 'ACTIVE', label: '进行中' },
      { value: 'COMPLETED', label: '已完成' },
      { value: 'ARCHIVED', label: '已归档' }
    ],
    showFilter: false,
    page: 1,
    hasMore: true
  },

  onLoad() {
    this.checkLogin();
  },

  onShow() {
    if (app.isLoggedIn()) {
      this.loadProjects();
    }
  },

  // 检查登录状态
  checkLogin() {
    if (!app.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    this.loadProjects();
  },

  // 加载项目列表
  async loadProjects(refresh = false) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        projects: []
      });
    }

    const { page, searchKeyword, filterStatus } = this.data;

    try {
      this.setData({ loading: refresh ? false : true, refreshing: refresh });

      const params = {
        page,
        limit: 10,
        search: searchKeyword,
        status: filterStatus === 'ALL' ? '' : filterStatus
      };

      const response = await projectApi.getProjectList(params);
      const newProjects = response.data || [];

      this.setData({
        projects: refresh ? newProjects : [...this.data.projects, ...newProjects],
        hasMore: newProjects.length >= 10,
        page: refresh ? 2 : page + 1,
        loading: false,
        refreshing: false
      });

    } catch (error) {
      console.error('加载项目列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
      this.setData({ loading: false, refreshing: false });
    }
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  handleSearch() {
    this.loadProjects(true);
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.loadProjects(true);
  },

  // 切换筛选器显示
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 选择状态筛选
  selectStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      filterStatus: status,
      showFilter: false
    });
    this.loadProjects(true);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadProjects(true);
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadProjects();
    }
  },

  // 跳转到项目详情
  goToProjectDetail(e) {
    const projectId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/project-detail/project-detail?id=${projectId}`
    });
  },

  // 创建新项目
  createProject() {
    wx.showModal({
      title: '创建项目',
      content: '此功能需要在详情页面实现',
      showCancel: false
    });
  },

  // 格式化项目状态
  formatStatus(status) {
    return getStatusText(status, 'project');
  },

  // 格式化时间
  formatTime(time) {
    return formatTime(time, 'YYYY-MM-DD');
  },

  // 计算项目进度
  calculateProgress(tasks) {
    return calculateProgress(tasks);
  },

  // 获取项目状态样式类
  getStatusClass(status) {
    const classMap = {
      'PLANNING': 'status-pending',
      'ACTIVE': 'status-active',
      'COMPLETED': 'status-completed',
      'ARCHIVED': 'status-cancelled'
    };
    return classMap[status] || 'status-pending';
  },

  // 计算项目进度颜色
  getProgressColor(progress) {
    if (progress < 30) return '#EF4444';
    if (progress < 70) return '#F59E0B';
    return '#10B981';
  }
});
