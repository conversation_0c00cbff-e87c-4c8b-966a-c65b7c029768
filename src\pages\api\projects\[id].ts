import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';
import { isProjectMember, isProjectOwner } from '@/lib/permissions';
import {
  notifyProjectStatusChanged,
  notifyProjectCompleted,
  notifyProjectArchived,
  notifyProjectMemberAdded,
  notifyProjectMemberRemoved
} from '@/lib/systemNotifications';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const { id } = req.query;
  const projectId = Array.isArray(id) ? id[0] : id;

  // 验证项目ID
  if (!projectId) {
    return res.status(400).json({ message: '无效的项目ID' });
  }

  // 检查项目是否存在
  const project = await prisma.project.findUnique({
    where: { id: projectId },
  });

  if (!project) {
    return res.status(404).json({ message: '项目不存在' });
  }

  // 检查用户是否有权限访问该项目
  const canAccessProject = await isProjectMember(req, res, projectId);
  if (!canAccessProject) {
    return res.status(403).json({ message: '没有权限访问该项目' });
  }

  // 处理GET请求 - 获取项目详情
  if (req.method === 'GET') {
    try {
      const projectDetails = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          members: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
          tasks: {
            include: {
              assignee: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              updatedAt: 'desc',
            },
          },
          files: {
            include: {
              uploader: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              updatedAt: 'desc',
            },
          },
        },
      });

      return res.status(200).json(projectDetails);
    } catch (error) {
      console.error('获取项目详情失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理PUT请求 - 更新项目
  if (req.method === 'PUT') {
    // 检查用户是否为项目所有者
    const isOwner = await isProjectOwner(req, res, projectId);
    if (!isOwner) {
      return res.status(403).json({ message: '只有项目负责人可以更新项目' });
    }

    const { title, description, startDate, endDate, status, members } = req.body;

    try {
      // 获取更新前的项目信息（用于比较变化）
      const originalProject = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          members: {
            select: { id: true, name: true }
          }
        }
      });

      if (!originalProject) {
        return res.status(404).json({ message: '项目不存在' });
      }

      // 更新项目基本信息
      const updatedProject = await prisma.project.update({
        where: { id: projectId },
        data: {
          ...(title && { title }),
          ...(description !== undefined && { description }),
          ...(startDate && { startDate: new Date(startDate) }),
          ...(endDate && { endDate: new Date(endDate) }),
          ...(status && { status }),
        },
      });

      // 如果提供了成员列表，则更新项目成员
      let addedMembers: string[] = [];
      let removedMembers: string[] = [];

      if (members) {
        const originalMemberIds = originalProject.members.map(m => m.id);
        addedMembers = members.filter((id: string) => !originalMemberIds.includes(id));
        removedMembers = originalMemberIds.filter(id => !members.includes(id));

        // 先清除所有现有成员
        await prisma.project.update({
          where: { id: projectId },
          data: {
            members: {
              set: [],
            },
          },
        });

        // 添加新成员
        if (members.length > 0) {
          await prisma.project.update({
            where: { id: projectId },
            data: {
              members: {
                connect: members.map((id: string) => ({ id })),
              },
            },
          });
        }
      }

      // 获取更新后的项目详情
      const projectDetails = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          members: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
      });

      // 发送通知
      try {
        const currentUser = await prisma.user.findUnique({
          where: { id: await getCurrentUserId(req, res) },
          select: { name: true }
        });

        if (currentUser && projectDetails) {
          // 项目状态变更通知
          if (status && status !== originalProject.status) {
            const allMemberIds = projectDetails.members.map(m => m.id);

            if (status === 'COMPLETED') {
              await notifyProjectCompleted(projectId, allMemberIds, projectDetails.title, currentUser.name);
            } else if (status === 'ARCHIVED') {
              await notifyProjectArchived(projectId, allMemberIds, projectDetails.title, currentUser.name);
            } else {
              await notifyProjectStatusChanged(projectId, allMemberIds, projectDetails.title, status, currentUser.name);
            }
          }

          // 成员添加通知
          for (const memberId of addedMembers) {
            await notifyProjectMemberAdded(projectId, memberId, projectDetails.title, currentUser.name);
          }

          // 成员移除通知
          for (const memberId of removedMembers) {
            await notifyProjectMemberRemoved(projectId, memberId, projectDetails.title, currentUser.name);
          }
        }
      } catch (notificationError) {
        console.error('发送项目更新通知失败:', notificationError);
        // 不影响项目更新，只记录错误
      }

      return res.status(200).json(projectDetails);
    } catch (error) {
      console.error('更新项目失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理DELETE请求 - 删除项目
  if (req.method === 'DELETE') {
    // 检查用户是否为项目所有者
    const isOwner = await isProjectOwner(req, res, projectId);
    if (!isOwner) {
      return res.status(403).json({ message: '只有项目负责人可以删除项目' });
    }

    try {
      // 删除项目
      await prisma.project.delete({
        where: { id: projectId },
      });

      return res.status(200).json({ message: '项目已成功删除' });
    } catch (error) {
      console.error('删除项目失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
