// 测试聊天创建是否会产生重复的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testChatCreation() {
  try {
    console.log('开始测试聊天创建...');

    // 获取用户
    const users = await prisma.user.findMany({
      take: 2,
      select: {
        id: true,
        name: true,
      },
    });

    if (users.length < 2) {
      console.log('需要至少2个用户来测试');
      return;
    }

    const user1 = users[0];
    const user2 = users[1];
    console.log(`测试用户: ${user1.name} 和 ${user2.name}`);

    // 模拟并发创建聊天请求
    console.log('模拟并发创建聊天请求...');
    
    const promises = [];
    for (let i = 0; i < 5; i++) {
      promises.push(createChat(user1.id, user2.id));
    }

    const results = await Promise.allSettled(promises);
    
    console.log('并发请求结果:');
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        console.log(`请求 ${index + 1}: 成功 - 聊天ID: ${result.value.id}`);
      } else {
        console.log(`请求 ${index + 1}: 失败 - ${result.reason.message}`);
      }
    });

    // 检查数据库中的聊天数量
    const chats = await prisma.chat.findMany({
      where: {
        type: 'PRIVATE',
        participants: {
          every: {
            id: {
              in: [user1.id, user2.id],
            },
          },
        },
      },
      include: {
        participants: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            participants: true,
          },
        },
      },
    });

    const exactMatches = chats.filter(chat => 
      chat._count.participants === 2 &&
      chat.participants.every(p => [user1.id, user2.id].includes(p.id))
    );

    console.log(`\n数据库中的聊天记录:`);
    console.log(`总聊天数: ${chats.length}`);
    console.log(`精确匹配的聊天数: ${exactMatches.length}`);

    if (exactMatches.length === 1) {
      console.log('✅ 测试通过：没有创建重复聊天');
    } else {
      console.log('❌ 测试失败：存在重复聊天');
      exactMatches.forEach((chat, index) => {
        console.log(`聊天 ${index + 1}: ${chat.id} - 参与者: ${chat.participants.map(p => p.name).join(', ')}`);
      });
    }

  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function createChat(user1Id, user2Id) {
  // 模拟API调用
  const response = await fetch('http://localhost:3000/api/chats', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cookie': 'next-auth.session-token=test', // 这里需要实际的session token
    },
    body: JSON.stringify({
      participantIds: [user2Id],
      type: 'PRIVATE',
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return await response.json();
}

testChatCreation();
