import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

// 腾讯会议API签名生成
function generateSignature(secretKey: string, timestamp: string, nonce: string) {
  const stringToSign = `${timestamp}\n${nonce}`;
  return crypto.createHmac('sha256', secretKey).update(stringToSign).digest('hex');
}

// 生成随机字符串
function generateNonce(length: number = 16): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.email) {
      return res.status(401).json({ message: '未授权' });
    }

    // 获取当前用户
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!currentUser) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 检查权限（管理员和项目负责人可以创建会议）
    if (currentUser.role !== 'ADMIN' && currentUser.role !== 'LEADER') {
      return res.status(403).json({ message: '权限不足，只有管理员和项目负责人可以创建会议' });
    }

    // 暂时跳过腾讯会议配置检查，创建本地会议记录
    // TODO: 在管理员配置腾讯会议后启用API集成
    const meetingConfig = null;

    const { subject, startTime, endTime, invitees = [], type = 0 } = req.body;

    if (!subject) {
      return res.status(400).json({ message: '会议主题不能为空' });
    }

    if (!startTime || !endTime) {
      return res.status(400).json({ message: '请设置会议开始和结束时间' });
    }

    // 验证时间
    const start = new Date(startTime);
    const end = new Date(endTime);
    const now = new Date();

    if (start <= now) {
      return res.status(400).json({ message: '会议开始时间必须大于当前时间' });
    }

    if (end <= start) {
      return res.status(400).json({ message: '会议结束时间必须大于开始时间' });
    }

    // 暂时跳过签名生成，等待腾讯会议API配置

    // 生成本地会议信息
    const meetingCode = Math.random().toString().slice(2, 11); // 生成9位会议号
    const meetingId = `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const joinUrl = `https://meeting.tencent.com/dm/${meetingCode}`; // 腾讯会议链接格式

    // 保存会议记录到数据库
    const meeting = await prisma.meeting.create({
      data: {
        meetingId: meetingId,
        meetingCode: meetingCode,
        subject: subject,
        joinUrl: joinUrl,
        password: '', // 暂时不设置密码
        startTime: start,
        endTime: end,
        creatorId: currentUser.id,
        status: 'SCHEDULED',
      },
    });

    // 发送通知给邀请的用户
    if (invitees.length > 0) {
      const invitedUsers = await prisma.user.findMany({
        where: {
          email: { in: invitees },
          status: 'APPROVED',
        },
      });

      const notifications = invitedUsers.map(user => ({
        userId: user.id,
        type: 'MEETING_INVITATION',
        title: `会议邀请: ${subject}`,
        message: `您被邀请参加会议"${subject}"，会议时间：${start.toLocaleString('zh-CN')}`,
        relatedType: 'meeting',
        relatedId: meeting.id,
      }));

      if (notifications.length > 0) {
        await prisma.notification.createMany({
          data: notifications,
        });
      }
    }

    return res.status(200).json({
      message: '会议创建成功',
      meeting: {
        id: meeting.id,
        meetingId: meetingId,
        meetingCode: meetingCode,
        subject: subject,
        joinUrl: joinUrl,
        password: '',
        startTime: start.toISOString(),
        endTime: end.toISOString(),
      },
      note: '会议已创建，请手动在腾讯会议中创建对应的会议室。管理员配置腾讯会议API后将支持自动创建。'
    });

  } catch (error) {
    console.error('创建会议API错误:', error);
    return res.status(500).json({ message: '服务器错误' });
  } finally {
    await prisma.$disconnect();
  }
}
