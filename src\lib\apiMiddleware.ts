import { NextApiRequest, NextApiResponse } from 'next';
import { isAuthenticated, isAdmin, getCurrentUserId } from './auth';
import { AdvancedPermissions, Permission } from './permissions';

// API错误响应类型
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// API成功响应类型
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  success: boolean;
}

// 权限检查选项
export interface PermissionCheckOptions {
  permission: Permission;
  resourceId?: string;
  resourceType?: 'project' | 'task' | 'file';
}

// 权限检查中间件
export async function requireAuth(
  req: NextApiRequest,
  res: NextApiResponse,
  next: () => Promise<void>
) {
  try {
    if (!(await isAuthenticated(req, res))) {
      return res.status(401).json({
        success: false,
        message: '请先登录',
        code: 'UNAUTHORIZED'
      });
    }
    await next();
  } catch (error) {
    console.error('认证检查失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
      code: 'INTERNAL_ERROR'
    });
  }
}

// 管理员权限检查中间件
export async function requireAdmin(
  req: NextApiRequest,
  res: NextApiResponse,
  next: () => Promise<void>
) {
  try {
    if (!(await isAuthenticated(req, res))) {
      return res.status(401).json({
        success: false,
        message: '请先登录',
        code: 'UNAUTHORIZED'
      });
    }

    if (!(await isAdmin(req, res))) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限',
        code: 'FORBIDDEN'
      });
    }

    await next();
  } catch (error) {
    console.error('管理员权限检查失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
      code: 'INTERNAL_ERROR'
    });
  }
}

// 用户状态检查中间件
export async function requireApprovedUser(
  req: NextApiRequest,
  res: NextApiResponse,
  next: () => Promise<void>
) {
  try {
    if (!(await isAuthenticated(req, res))) {
      return res.status(401).json({
        success: false,
        message: '请先登录',
        code: 'UNAUTHORIZED'
      });
    }

    const userId = await getCurrentUserId(req, res);
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户信息无效',
        code: 'INVALID_USER'
      });
    }

    // 检查用户状态
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { status: true, role: true }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在',
          code: 'USER_NOT_FOUND'
        });
      }

      if (user.status !== 'APPROVED') {
        return res.status(403).json({
          success: false,
          message: '您的账号尚未通过审核，请联系管理员',
          code: 'USER_NOT_APPROVED'
        });
      }

      await next();
    } finally {
      await prisma.$disconnect();
    }
  } catch (error) {
    console.error('用户状态检查失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
      code: 'INTERNAL_ERROR'
    });
  }
}

// 错误处理中间件
export function handleApiError(
  error: any,
  req: NextApiRequest,
  res: NextApiResponse
) {
  console.error('API错误:', error);

  // 如果是已知的业务错误
  if (error.code) {
    return res.status(error.status || 400).json({
      success: false,
      message: error.message,
      code: error.code
    });
  }

  // 数据库错误
  if (error.code === 'P2002') {
    return res.status(409).json({
      success: false,
      message: '数据冲突，该记录已存在',
      code: 'DUPLICATE_RECORD'
    });
  }

  if (error.code === 'P2025') {
    return res.status(404).json({
      success: false,
      message: '记录不存在',
      code: 'RECORD_NOT_FOUND'
    });
  }

  // 默认错误响应
  return res.status(500).json({
    success: false,
    message: '服务器内部错误，请稍后再试',
    code: 'INTERNAL_ERROR'
  });
}

// 成功响应辅助函数
export function sendSuccess<T>(
  res: NextApiResponse,
  data?: T,
  message?: string,
  status: number = 200
) {
  return res.status(status).json({
    success: true,
    data,
    message
  });
}

// 错误响应辅助函数
export function sendError(
  res: NextApiResponse,
  message: string,
  code?: string,
  status: number = 400
) {
  return res.status(status).json({
    success: false,
    message,
    code
  });
}

// 方法检查中间件
export function requireMethod(allowedMethods: string[]) {
  return (req: NextApiRequest, res: NextApiResponse, next: () => Promise<void>) => {
    if (!allowedMethods.includes(req.method || '')) {
      return res.status(405).json({
        success: false,
        message: `方法 ${req.method} 不被允许`,
        code: 'METHOD_NOT_ALLOWED'
      });
    }
    return next();
  };
}

// 高级权限检查中间件
export async function requirePermission(
  options: PermissionCheckOptions
) {
  return async (req: NextApiRequest, res: NextApiResponse, next: () => Promise<void>) => {
    try {
      const permissions = await AdvancedPermissions.fromRequest(req, res);

      if (!permissions) {
        return res.status(401).json({
          success: false,
          message: '请先登录',
          code: 'UNAUTHORIZED'
        });
      }

      let hasPermission = false;

      // 根据资源类型检查权限
      if (options.resourceType && options.resourceId) {
        switch (options.resourceType) {
          case 'project':
            hasPermission = await permissions.hasProjectPermission(options.permission, options.resourceId);
            break;
          case 'task':
            hasPermission = await permissions.hasTaskPermission(options.permission, options.resourceId);
            break;
          case 'file':
            hasPermission = await permissions.hasFilePermission(options.permission, options.resourceId);
            break;
          default:
            hasPermission = permissions.hasPermission(options.permission);
        }
      } else {
        hasPermission = permissions.hasPermission(options.permission);
      }

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '权限不足',
          code: 'FORBIDDEN'
        });
      }

      await next();
    } catch (error) {
      console.error('权限检查失败:', error);
      return res.status(500).json({
        success: false,
        message: '服务器错误',
        code: 'INTERNAL_ERROR'
      });
    }
  };
}

// 资源权限检查中间件（从请求参数中获取资源ID）
export async function requireResourcePermission(
  permission: Permission,
  resourceType: 'project' | 'task' | 'file',
  resourceIdParam: string = 'id'
) {
  return async (req: NextApiRequest, res: NextApiResponse, next: () => Promise<void>) => {
    const resourceId = req.query[resourceIdParam] as string || req.body[resourceIdParam];

    if (!resourceId) {
      return res.status(400).json({
        success: false,
        message: `缺少${resourceType}ID`,
        code: 'RESOURCE_ID_REQUIRED'
      });
    }

    const permissionMiddleware = await requirePermission({
      permission,
      resourceId,
      resourceType
    });

    return await permissionMiddleware(req, res, next);
  };
}

// 批量权限检查中间件
export async function requireAnyPermission(
  permissions: Permission[]
) {
  return async (req: NextApiRequest, res: NextApiResponse, next: () => Promise<void>) => {
    try {
      const userPermissions = await AdvancedPermissions.fromRequest(req, res);

      if (!userPermissions) {
        return res.status(401).json({
          success: false,
          message: '请先登录',
          code: 'UNAUTHORIZED'
        });
      }

      const hasAnyPermission = permissions.some(permission =>
        userPermissions.hasPermission(permission)
      );

      if (!hasAnyPermission) {
        return res.status(403).json({
          success: false,
          message: '权限不足',
          code: 'FORBIDDEN'
        });
      }

      await next();
    } catch (error) {
      console.error('权限检查失败:', error);
      return res.status(500).json({
        success: false,
        message: '服务器错误',
        code: 'INTERNAL_ERROR'
      });
    }
  };
}

// 条件权限检查中间件
export async function requireConditionalPermission(
  condition: (req: NextApiRequest) => boolean,
  permission: Permission
) {
  return async (req: NextApiRequest, res: NextApiResponse, next: () => Promise<void>) => {
    if (!condition(req)) {
      return await next();
    }

    const permissionMiddleware = await requirePermission({ permission });
    return await permissionMiddleware(req, res, next);
  };
}

// 组合中间件执行器
export async function runMiddleware(
  req: NextApiRequest,
  res: NextApiResponse,
  middlewares: Array<(req: NextApiRequest, res: NextApiResponse, next: () => Promise<void>) => Promise<void>>
) {
  for (const middleware of middlewares) {
    let nextCalled = false;

    await middleware(req, res, async () => {
      nextCalled = true;
    });

    if (!nextCalled) {
      return; // 中间件已经处理了响应
    }
  }
}

// 权限装饰器工厂
export function withPermissions(
  permissions: Permission | Permission[] | PermissionCheckOptions,
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    let middleware;

    if (Array.isArray(permissions)) {
      middleware = await requireAnyPermission(permissions);
    } else if (typeof permissions === 'object' && 'permission' in permissions) {
      middleware = await requirePermission(permissions);
    } else {
      middleware = await requirePermission({ permission: permissions as Permission });
    }

    await runMiddleware(req, res, [
      requireAuth,
      requireApprovedUser,
      middleware
    ]);

    // 如果所有中间件都通过，执行处理器
    if (!res.headersSent) {
      await handler(req, res);
    }
  };
}
