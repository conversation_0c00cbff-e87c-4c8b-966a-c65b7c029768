<!--首页-->
<view class="container">
  <!-- 加载中 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="fade-in">
    <!-- 用户信息卡片 -->
    <view class="card user-card">
      <view class="user-info">
        <view class="avatar">
          <image
            src="{{userInfo.avatar || '/images/default-avatar.svg'}}"
            mode="aspectFill"
          />
        </view>
        <view class="user-details">
          <view class="user-name">{{userInfo.name || '用户'}}</view>
          <view class="user-role">{{userInfo.role || '成员'}}</view>
          <view class="user-department">{{userInfo.department || '未设置部门'}}</view>
        </view>
        <view class="user-actions">
          <text class="notification-icon" bindtap="goToNotifications">🔔</text>
          <view wx:if="{{stats.unreadNotifications > 0}}" class="badge">{{stats.unreadNotifications}}</view>
        </view>
      </view>
    </view>

    <!-- 统计卡片 -->
    <view class="stats-grid">
      <view class="stat-card" bindtap="goToProjects">
        <view class="stat-icon">📊</view>
        <view class="stat-number">{{stats.totalProjects}}</view>
        <view class="stat-label">总项目</view>
        <view class="stat-sublabel">{{stats.activeProjects}} 个进行中</view>
      </view>

      <view class="stat-card" bindtap="goToTasks">
        <view class="stat-icon">✅</view>
        <view class="stat-number">{{stats.pendingTasks}}</view>
        <view class="stat-label">待办任务</view>
        <view class="stat-sublabel">{{stats.completedTasks}} 个已完成</view>
      </view>

      <view class="stat-card" bindtap="goToChat">
        <view class="stat-icon">💬</view>
        <view class="stat-number">{{stats.unreadMessages || 0}}</view>
        <view class="stat-label">未读消息</view>
        <view class="stat-sublabel">聊天消息</view>
      </view>

      <view class="stat-card" bindtap="goToTeam">
        <view class="stat-icon">👥</view>
        <view class="stat-number">{{stats.totalUsers}}</view>
        <view class="stat-label">团队成员</view>
        <view class="stat-sublabel">协作伙伴</view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">快捷操作</view>
      </view>
      <view class="quick-actions">
        <view class="action-item" bindtap="goToProjects">
          <view class="action-icon">📊</view>
          <view class="action-text">项目管理</view>
        </view>
        <view class="action-item" bindtap="goToTasks">
          <view class="action-icon">✅</view>
          <view class="action-text">任务管理</view>
        </view>
        <view class="action-item" bindtap="goToChat">
          <view class="action-icon">💬</view>
          <view class="action-text">团队聊天</view>
        </view>
        <view class="action-item" bindtap="goToTeam">
          <view class="action-icon">👥</view>
          <view class="action-text">团队管理</view>
        </view>
      </view>
    </view>

    <!-- 最近活动 -->
    <view class="card">
      <view class="card-header">
        <view class="card-title">最近活动</view>
      </view>
      <view wx:if="{{recentActivities.length === 0}}" class="empty">
        <view class="empty-text">暂无最近活动</view>
        <view class="empty-desc">开始创建项目和任务吧！</view>
      </view>
      <view wx:else class="activity-list">
        <view
          wx:for="{{recentActivities}}"
          wx:key="id"
          class="activity-item"
        >
          <view class="activity-content">
            <view class="activity-title">{{item.title}}</view>
            <view class="activity-desc">{{item.description}}</view>
            <view class="activity-time">{{formatTime(item.createdAt)}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
