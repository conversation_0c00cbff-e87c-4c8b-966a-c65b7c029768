import type { NextApiRequest, NextApiResponse } from 'next';
import { AnalyticsService, AnalyticsTimeRange } from '@/lib/analytics';
import { withPermissions } from '@/lib/apiMiddleware';
import { Permission } from '@/lib/permissions';
import { sendSuccess, sendError, handleApiError } from '@/lib/apiMiddleware';
import { getCurrentUserId, isAdmin } from '@/lib/auth';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
  }

  try {
    const userId = await getCurrentUserId(req, res);
    const isUserAdmin = await isAdmin(req, res);
    
    const {
      timeRange = AnalyticsTimeRange.LAST_30_DAYS,
      startDate,
      endDate,
      projectId,
      scope = 'personal'
    } = req.query;

    // 检查权限
    if (scope === 'global' && !isUserAdmin) {
      return sendError(res, '权限不足，无法查看全局项目分析', 'FORBIDDEN', 403);
    }

    // 解析日期范围
    let customStart: Date | undefined;
    let customEnd: Date | undefined;
    
    if (timeRange === AnalyticsTimeRange.CUSTOM) {
      if (startDate && endDate) {
        customStart = new Date(startDate as string);
        customEnd = new Date(endDate as string);
        
        if (isNaN(customStart.getTime()) || isNaN(customEnd.getTime())) {
          return sendError(res, '无效的日期格式', 'INVALID_DATE_FORMAT', 400);
        }
      } else {
        return sendError(res, '自定义时间范围需要提供开始和结束日期', 'MISSING_DATE_RANGE', 400);
      }
    }

    const { startDate: rangeStart, endDate: rangeEnd } = AnalyticsService.getDateRange(
      timeRange as AnalyticsTimeRange,
      customStart,
      customEnd
    );

    // 获取项目分析数据
    const projectAnalytics = await AnalyticsService.getProjectAnalytics(
      rangeStart,
      rangeEnd,
      scope === 'personal' ? userId! : undefined
    );

    // 如果指定了特定项目，获取该项目的详细分析
    let projectDetails = null;
    if (projectId) {
      // 这里可以添加特定项目的详细分析逻辑
      // 暂时返回基础信息
      projectDetails = {
        projectId: projectId as string,
        message: '项目详细分析功能开发中'
      };
    }

    const response = {
      timeRange,
      startDate: rangeStart,
      endDate: rangeEnd,
      projectAnalytics,
      projectDetails
    };

    return sendSuccess(res, response, '项目分析数据获取成功');
  } catch (error) {
    console.error('获取项目分析数据失败:', error);
    return handleApiError(error, req, res);
  }
}

export default withPermissions(Permission.PROJECT_READ, handler);
