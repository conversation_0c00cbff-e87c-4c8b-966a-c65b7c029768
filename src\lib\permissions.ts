import { NextApiRequest, NextApiResponse } from 'next';
import prisma from './prisma';
import { getCurrentUserId, isAdmin } from './auth';

// 权限类型枚举
export enum Permission {
  // 项目权限
  PROJECT_CREATE = 'project:create',
  PROJECT_READ = 'project:read',
  PROJECT_UPDATE = 'project:update',
  PROJECT_DELETE = 'project:delete',
  PROJECT_MANAGE_MEMBERS = 'project:manage_members',

  // 任务权限
  TASK_CREATE = 'task:create',
  TASK_READ = 'task:read',
  TASK_UPDATE = 'task:update',
  TASK_DELETE = 'task:delete',
  TASK_ASSIGN = 'task:assign',

  // 文件权限
  FILE_UPLOAD = 'file:upload',
  FILE_READ = 'file:read',
  FILE_DELETE = 'file:delete',

  // 用户管理权限
  USER_MANAGE = 'user:manage',
  USER_APPROVE = 'user:approve',

  // 系统管理权限
  SYSTEM_ADMIN = 'system:admin',
  INVITE_CODE_MANAGE = 'invite_code:manage',
}

// 资源类型枚举
export enum ResourceType {
  PROJECT = 'project',
  TASK = 'task',
  FILE = 'file',
  USER = 'user',
  SYSTEM = 'system',
}

// 高级权限管理类
export class AdvancedPermissions {
  private userId: string;
  private userRole: string;
  private isUserAdmin: boolean;

  constructor(userId: string, userRole: string, isUserAdmin: boolean) {
    this.userId = userId;
    this.userRole = userRole;
    this.isUserAdmin = isUserAdmin;
  }

  // 获取用户ID
  getUserId(): string {
    return this.userId;
  }

  // 检查是否为管理员
  getIsUserAdmin(): boolean {
    return this.isUserAdmin;
  }

  // 静态方法：从请求中创建权限实例
  static async fromRequest(req: NextApiRequest, res: NextApiResponse): Promise<AdvancedPermissions | null> {
    const userId = await getCurrentUserId(req, res);
    if (!userId) return null;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true, status: true }
    });

    if (!user || user.status !== 'APPROVED') return null;

    const isUserAdmin = await isAdmin(req, res);
    return new AdvancedPermissions(userId, user.role, isUserAdmin);
  }

  // 检查基础权限
  hasPermission(permission: Permission): boolean {
    // 管理员拥有所有权限
    if (this.isUserAdmin) return true;

    // 根据角色检查权限
    switch (this.userRole) {
      case 'LEADER':
        return this.hasLeaderPermission(permission);
      case 'MEMBER':
        return this.hasMemberPermission(permission);
      case 'GUEST':
        return this.hasGuestPermission(permission);
      default:
        return false;
    }
  }

  // 检查项目相关权限
  async hasProjectPermission(permission: Permission, projectId: string): Promise<boolean> {
    // 管理员拥有所有权限
    if (this.isUserAdmin) return true;

    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        members: { select: { id: true } }
      }
    });

    if (!project) return false;

    const isOwner = project.ownerId === this.userId;
    const isMember = project.members.some(member => member.id === this.userId);

    switch (permission) {
      case Permission.PROJECT_READ:
        return isOwner || isMember;
      case Permission.PROJECT_UPDATE:
      case Permission.PROJECT_DELETE:
      case Permission.PROJECT_MANAGE_MEMBERS:
        return isOwner || (this.userRole === 'LEADER' && isMember);
      case Permission.TASK_CREATE:
      case Permission.TASK_ASSIGN:
        return isOwner || isMember;
      case Permission.FILE_UPLOAD:
        return isOwner || isMember;
      default:
        return false;
    }
  }

  // 检查任务相关权限
  async hasTaskPermission(permission: Permission, taskId: string): Promise<boolean> {
    // 管理员拥有所有权限
    if (this.isUserAdmin) return true;

    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        project: {
          include: {
            members: { select: { id: true } }
          }
        },
        assignees: { select: { id: true } }
      }
    });

    if (!task) return false;

    const isProjectOwner = task.project.ownerId === this.userId;
    const isProjectMember = task.project.members.some(member => member.id === this.userId);
    const isTaskAssignee = task.assigneeId === this.userId ||
                          task.assignees.some(assignee => assignee.id === this.userId);

    switch (permission) {
      case Permission.TASK_READ:
        return isProjectOwner || isProjectMember;
      case Permission.TASK_UPDATE:
        return isProjectOwner || isTaskAssignee || (this.userRole === 'LEADER' && isProjectMember);
      case Permission.TASK_DELETE:
        return isProjectOwner || (this.userRole === 'LEADER' && isProjectMember);
      case Permission.TASK_ASSIGN:
        return isProjectOwner || (this.userRole === 'LEADER' && isProjectMember);
      default:
        return false;
    }
  }

  // 检查文件相关权限
  async hasFilePermission(permission: Permission, fileId: string): Promise<boolean> {
    // 管理员拥有所有权限
    if (this.isUserAdmin) return true;

    const file = await prisma.file.findUnique({
      where: { id: fileId },
      include: {
        project: {
          include: {
            members: { select: { id: true } }
          }
        }
      }
    });

    if (!file) return false;

    const isUploader = file.uploaderId === this.userId;

    switch (permission) {
      case Permission.FILE_READ:
        if (isUploader) return true;
        if (file.project) {
          const isProjectOwner = file.project.ownerId === this.userId;
          const isProjectMember = file.project.members.some(member => member.id === this.userId);
          return isProjectOwner || isProjectMember;
        }
        return false;
      case Permission.FILE_DELETE:
        if (isUploader) return true;
        if (file.project) {
          const isProjectOwner = file.project.ownerId === this.userId;
          return isProjectOwner || (this.userRole === 'LEADER');
        }
        return false;
      default:
        return false;
    }
  }

  // LEADER角色权限
  private hasLeaderPermission(permission: Permission): boolean {
    const leaderPermissions = [
      Permission.PROJECT_CREATE,
      Permission.PROJECT_READ,
      Permission.PROJECT_UPDATE,
      Permission.TASK_CREATE,
      Permission.TASK_READ,
      Permission.TASK_UPDATE,
      Permission.TASK_ASSIGN,
      Permission.FILE_UPLOAD,
      Permission.FILE_READ,
    ];
    return leaderPermissions.includes(permission);
  }

  // MEMBER角色权限
  private hasMemberPermission(permission: Permission): boolean {
    const memberPermissions = [
      Permission.PROJECT_READ,
      Permission.TASK_READ,
      Permission.TASK_UPDATE, // 只能更新分配给自己的任务
      Permission.FILE_UPLOAD,
      Permission.FILE_READ,
    ];
    return memberPermissions.includes(permission);
  }

  // GUEST角色权限
  private hasGuestPermission(permission: Permission): boolean {
    const guestPermissions = [
      Permission.PROJECT_READ, // 只能读取被邀请的项目
      Permission.TASK_READ,
      Permission.FILE_READ,
    ];
    return guestPermissions.includes(permission);
  }
}

// 兼容性函数：保持原有API不变
export async function isProjectOwner(
  req: NextApiRequest,
  res: NextApiResponse,
  projectId: string
): Promise<boolean> {
  const permissions = await AdvancedPermissions.fromRequest(req, res);
  if (!permissions) return false;

  const project = await prisma.project.findUnique({
    where: { id: projectId },
    select: { ownerId: true }
  });

  return project?.ownerId === permissions.getUserId() || permissions.getIsUserAdmin();
}

export async function isProjectMember(
  req: NextApiRequest,
  res: NextApiResponse,
  projectId: string
): Promise<boolean> {
  const permissions = await AdvancedPermissions.fromRequest(req, res);
  if (!permissions) return false;

  return await permissions.hasProjectPermission(Permission.PROJECT_READ, projectId);
}

export async function isTaskAssignee(
  req: NextApiRequest,
  res: NextApiResponse,
  taskId: string
): Promise<boolean> {
  const permissions = await AdvancedPermissions.fromRequest(req, res);
  if (!permissions) return false;

  return await permissions.hasTaskPermission(Permission.TASK_UPDATE, taskId);
}
