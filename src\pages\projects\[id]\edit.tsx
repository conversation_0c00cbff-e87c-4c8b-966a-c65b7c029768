import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';

export default function EditProject() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { id } = router.query;
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  
  // 表单数据
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startDate: '',
    endDate: '',
    status: 'ACTIVE',
  });

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取项目详情
  useEffect(() => {
    if (id && status === 'authenticated') {
      fetchProject();
    }
  }, [id, status]);

  const fetchProject = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/projects/${id}`);

      if (!response.ok) {
        throw new Error('获取项目详情失败');
      }

      const projectData = await response.json();
      setProject(projectData);
      
      // 设置表单数据
      setFormData({
        title: projectData.title || '',
        description: projectData.description || '',
        startDate: projectData.startDate ? projectData.startDate.split('T')[0] : '',
        endDate: projectData.endDate ? projectData.endDate.split('T')[0] : '',
        status: projectData.status || 'ACTIVE',
      });
    } catch (error) {
      console.error('获取项目详情失败:', error);
      setError('获取项目详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');

    try {
      const response = await fetch(`/api/projects/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '更新项目失败');
      }

      // 返回项目详情页
      router.push(`/projects/${id}`);
    } catch (error) {
      console.error('更新项目失败:', error);
      setError(error.message || '更新项目失败，请稍后再试');
    } finally {
      setSaving(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 加载中
  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 错误状态
  if (error && !project) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="mt-4">
          <Link href="/projects" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回项目列表
          </Link>
        </div>
      </div>
    );
  }

  // 项目不存在或无权限
  if (!project) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">项目不存在或您没有权限编辑</span>
        </div>
        <div className="mt-4">
          <Link href="/projects" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回项目列表
          </Link>
        </div>
      </div>
    );
  }

  // 检查权限
  const canEdit = session?.user?.id === project.owner.id || session?.user?.role === 'ADMIN';
  if (!canEdit) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">您没有权限编辑此项目</span>
        </div>
        <div className="mt-4">
          <Link href={`/projects/${id}`} className="text-primary-600 dark:text-primary-400 hover:underline">
            返回项目详情
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-2">
          <Link href="/projects" className="hover:text-gray-700 dark:hover:text-gray-300">
            项目
          </Link>
          <span>/</span>
          <Link href={`/projects/${id}`} className="hover:text-gray-700 dark:hover:text-gray-300">
            {project.title}
          </Link>
          <span>/</span>
          <span>编辑</span>
        </div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          编辑项目
        </h1>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-6 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* 编辑表单 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 项目标题 */}
          <div>
            <label htmlFor="title" className="form-label">
              项目标题 *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="form-input"
              required
            />
          </div>

          {/* 项目描述 */}
          <div>
            <label htmlFor="description" className="form-label">
              项目描述
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="form-input"
              placeholder="详细描述项目内容和目标..."
            />
          </div>

          {/* 开始日期、结束日期、状态 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label htmlFor="startDate" className="form-label">
                开始日期 *
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                className="form-input"
                required
              />
            </div>

            <div>
              <label htmlFor="endDate" className="form-label">
                结束日期
              </label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={formData.endDate}
                onChange={handleInputChange}
                className="form-input"
              />
            </div>

            <div>
              <label htmlFor="status" className="form-label">
                项目状态
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="form-input"
              >
                <option value="PLANNING">规划中</option>
                <option value="ACTIVE">进行中</option>
                <option value="COMPLETED">已完成</option>
                <option value="ARCHIVED">已归档</option>
              </select>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-4">
            <Link href={`/projects/${id}`} className="btn btn-secondary">
              取消
            </Link>
            <button
              type="submit"
              disabled={saving}
              className="btn btn-primary"
            >
              {saving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  保存中...
                </>
              ) : (
                '保存更改'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
