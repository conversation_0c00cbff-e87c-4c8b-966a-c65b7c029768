/* 登录页面样式 - 与网页版保持一致 */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%); /* 与网页版的 bg-gray-50 渐变一致 */
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  margin-bottom: 30rpx;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  color: #111827; /* 与网页版的 text-gray-900 一致 */
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #6b7280; /* 与网页版的 text-gray-600 一致 */
}

/* 表单容器 */
.form-container {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}

/* 输入框容器 */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  flex: 1;
  padding: 24rpx 60rpx 24rpx 24rpx;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #F9FAFB;
  box-sizing: border-box;
  transition: all 0.2s;
}

.form-input:focus {
  border-color: #3B82F6;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}

.input-icon {
  position: absolute;
  right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  cursor: pointer;
}

.icon {
  font-size: 32rpx;
  color: #9CA3AF;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #D1D5DB;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.2s;
}

.checkbox.checked {
  background-color: #3B82F6;
  border-color: #3B82F6;
}

.checkmark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.checkbox-label {
  font-size: 26rpx;
  color: #6B7280;
}

.forgot-password {
  font-size: 26rpx;
  color: #3B82F6;
  cursor: pointer;
}

/* 按钮样式 */
.login-btn {
  margin-top: 20rpx;
  padding: 28rpx;
  font-size: 32rpx;
  font-weight: 600;
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background-color: #E5E7EB;
}

.divider-text {
  margin: 0 20rpx;
  font-size: 24rpx;
  color: #9CA3AF;
}

/* 微信登录按钮 */
.wechat-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.wechat-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 注册链接 */
.register-link {
  text-align: center;
  font-size: 26rpx;
  color: #6B7280;
}

.link {
  color: #3B82F6;
  margin-left: 8rpx;
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 40rpx 30rpx;
  }

  .form-container {
    padding: 40rpx 30rpx;
  }

  .title {
    font-size: 42rpx;
  }
}
