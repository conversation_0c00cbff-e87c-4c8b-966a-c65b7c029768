import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const currentUserId = await getCurrentUserId(req, res);

  // 验证用户ID
  if (!currentUserId) {
    return res.status(401).json({ message: '用户未认证' });
  }

  // 处理GET请求 - 获取用户的聊天列表
  if (req.method === 'GET') {
    try {
      // 确保系统通知聊天存在
      let systemChat = await prisma.chat.findFirst({
        where: {
          type: 'SYSTEM',
          participants: {
            some: {
              id: currentUserId,
            },
          },
        },
      });

      if (!systemChat) {
        // 创建系统通知聊天
        systemChat = await prisma.chat.create({
          data: {
            type: 'SYSTEM',
            name: '系统通知',
            participants: {
              connect: { id: currentUserId },
            },
          },
        });
      }

      // 获取用户参与的所有聊天
      const chats = await prisma.chat.findMany({
        where: {
          participants: {
            some: {
              id: currentUserId,
            },
          },
        },
        include: {
          participants: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          messages: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
            select: {
              id: true,
              content: true,
              type: true,
              isSystem: true,
              createdAt: true,
              senderId: true,
              sender: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          _count: {
            select: {
              messages: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });

      // 处理聊天数据，添加未读消息数等信息
      const processedChats = await Promise.all(chats.map(async chat => {
        const otherParticipants = chat.participants.filter(p => p.id !== currentUserId);
        const lastMessage = chat.messages[0] || null;

        let chatName;
        if (chat.type === 'SYSTEM') {
          chatName = '系统通知';
        } else if (chat.name) {
          chatName = chat.name;
        } else if (otherParticipants.length === 1) {
          chatName = otherParticipants[0].name;
        } else {
          chatName = '群聊';
        }

        // 使用MessageRead表计算真正的未读消息数
        const unreadCount = await prisma.chatMessage.count({
          where: {
            chatId: chat.id,
            senderId: {
              not: currentUserId, // 不包括自己发送的消息
            },
            readBy: {
              none: {
                userId: currentUserId, // 用户未读的消息
              },
            },
          },
        });

        return {
          id: chat.id,
          type: chat.type,
          name: chatName,
          participants: chat.participants,
          otherParticipants,
          lastMessage,
          messageCount: chat._count.messages,
          unreadCount,
          updatedAt: chat.updatedAt,
          createdAt: chat.createdAt,
        };
      }));

      return res.status(200).json(processedChats);
    } catch (error) {
      console.error('获取聊天列表失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理POST请求 - 创建新聊天
  if (req.method === 'POST') {
    try {
      const { participantIds, type = 'PRIVATE', name } = req.body;

      // 验证参与者
      if (!participantIds || !Array.isArray(participantIds) || participantIds.length === 0) {
        return res.status(400).json({ message: '请指定聊天参与者' });
      }

      // 确保当前用户在参与者列表中
      const uniqueIds = new Set([currentUserId, ...participantIds]);
      const allParticipantIds = Array.from(uniqueIds);

      // 验证所有参与者都存在
      const participants = await prisma.user.findMany({
        where: {
          id: {
            in: allParticipantIds,
          },
        },
      });

      if (participants.length !== allParticipantIds.length) {
        return res.status(400).json({ message: '部分参与者不存在' });
      }

      // 对于私聊，使用事务来确保不会创建重复聊天
      if (type === 'PRIVATE' && allParticipantIds.length === 2) {
        // 使用事务来原子性地检查和创建聊天
        const result = await prisma.$transaction(async (tx) => {
          // 在事务中查找现有聊天
          const existingChats = await tx.chat.findMany({
            where: {
              type: 'PRIVATE',
              participants: {
                every: {
                  id: {
                    in: allParticipantIds,
                  },
                },
              },
            },
            include: {
              participants: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true,
                },
              },
              _count: {
                select: {
                  participants: true,
                },
              },
            },
          });

          // 找到确切匹配的聊天（只有这两个参与者）
          const exactMatch = existingChats.find(chat =>
            chat._count.participants === 2 &&
            chat.participants.every(p => allParticipantIds.includes(p.id))
          );

          if (exactMatch) {
            // 返回现有聊天
            const otherParticipants = exactMatch.participants.filter(p => p.id !== currentUserId);
            return {
              ...exactMatch,
              otherParticipants,
              isExisting: true,
            };
          }

          // 创建新聊天
          const newChat = await tx.chat.create({
            data: {
              type,
              name: type === 'GROUP' ? name : null,
              participants: {
                connect: allParticipantIds.map(id => ({ id })),
              },
            },
            include: {
              participants: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true,
                },
              },
              messages: {
                orderBy: {
                  createdAt: 'desc',
                },
                take: 1,
                include: {
                  sender: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
          });

          const otherParticipants = newChat.participants.filter(p => p.id !== currentUserId);
          return {
            ...newChat,
            otherParticipants,
            isExisting: false,
          };
        });

        const { isExisting, ...processedChat } = result;
        return res.status(isExisting ? 200 : 201).json(processedChat);
      }

      // 创建新聊天
      const chat = await prisma.chat.create({
        data: {
          type,
          name: type === 'GROUP' ? name : null,
          participants: {
            connect: allParticipantIds.map(id => ({ id })),
          },
        },
        include: {
          participants: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          messages: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      // 添加 otherParticipants 字段
      const otherParticipants = chat.participants.filter(p => p.id !== currentUserId);
      const processedChat = {
        ...chat,
        otherParticipants,
      };

      return res.status(201).json(processedChat);
    } catch (error) {
      console.error('创建聊天失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理DELETE请求 - 删除聊天
  if (req.method === 'DELETE') {
    try {
      const { chatId } = req.body;

      if (!chatId) {
        return res.status(400).json({ message: '请提供聊天ID' });
      }

      // 检查聊天是否存在以及用户是否有权限删除
      const chat = await prisma.chat.findUnique({
        where: { id: chatId },
        include: {
          participants: {
            select: {
              id: true,
            },
          },
        },
      });

      if (!chat) {
        return res.status(404).json({ message: '聊天不存在' });
      }

      // 检查用户是否是聊天参与者
      const isParticipant = chat.participants.some(p => p.id === currentUserId);
      if (!isParticipant) {
        return res.status(403).json({ message: '您没有权限删除此聊天' });
      }

      // 删除聊天（数据库会自动级联删除相关消息）
      await prisma.chat.delete({
        where: { id: chatId },
      });

      return res.status(200).json({ message: '聊天删除成功' });
    } catch (error) {
      console.error('删除聊天失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
