import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';
import { updateProjectProgress } from '@/lib/project-utils';
import { isProjectMember, isTaskAssignee } from '@/lib/permissions';
import { notifyTaskAssigned, notifyTaskCompleted } from '@/lib/notifications';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const { id } = req.query;
  const taskId = Array.isArray(id) ? id[0] : id;

  // 验证任务ID
  if (!taskId) {
    return res.status(400).json({ message: '无效的任务ID' });
  }

  // 检查任务是否存在
  const task = await prisma.task.findUnique({
    where: { id: taskId },
    include: { project: true },
  });

  if (!task) {
    return res.status(404).json({ message: '任务不存在' });
  }

  // 检查用户是否有权限访问该任务
  const canAccessProject = await isProjectMember(req, res, task.projectId);
  if (!canAccessProject) {
    return res.status(403).json({ message: '没有权限访问该任务' });
  }

  // 处理GET请求 - 获取任务详情
  if (req.method === 'GET') {
    try {
      const taskDetails = await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          project: {
            select: {
              id: true,
              title: true,
              ownerId: true,
              members: {
                select: {
                  id: true,
                },
              },
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          assignees: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          files: {
            include: {
              uploader: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!taskDetails) {
        return res.status(404).json({ message: '任务不存在' });
      }

      // 获取当前用户ID
      const currentUserId = await getCurrentUserId(req, res);
      if (!currentUserId) {
        return res.status(401).json({ message: '用户未认证' });
      }

      // 检查用户是否有权限查看此任务
      const currentUser = await prisma.user.findUnique({
        where: { id: currentUserId },
        select: { role: true },
      });
      const isUserAdmin = currentUser?.role === 'ADMIN';

      const hasAccess =
        isUserAdmin ||  // 管理员
        taskDetails.assignee?.id === currentUserId ||  // 任务负责人
        taskDetails.project.ownerId === currentUserId ||  // 项目所有者
        taskDetails.project.members.some(member => member.id === currentUserId);  // 项目成员

      if (!hasAccess) {
        return res.status(403).json({ message: '您没有权限查看此任务' });
      }

      return res.status(200).json(taskDetails);
    } catch (error) {
      console.error('获取任务详情失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理PUT请求 - 更新任务
  if (req.method === 'PUT') {
    // 检查用户是否为任务负责人、项目所有者或项目成员
    const canEditTask = await isTaskAssignee(req, res, taskId) ||
                        await isProjectMember(req, res, task.projectId);

    if (!canEditTask) {
      return res.status(403).json({ message: '没有权限更新该任务' });
    }

    const { title, description, dueDate, status, priority, assigneeId, assigneeIds } = req.body;

    try {
      // 更新任务
      const updatedTask = await prisma.task.update({
        where: { id: taskId },
        data: {
          ...(title && { title }),
          ...(description !== undefined && { description }),
          ...(dueDate && { dueDate: new Date(dueDate) }),
          ...(status && { status }),
          ...(priority && { priority }),
          ...(assigneeId !== undefined && {
            assignee: assigneeId ? {
              connect: { id: assigneeId },
            } : {
              disconnect: true,
            },
          }),
          ...(assigneeIds !== undefined && {
            assignees: {
              set: assigneeIds.map((id: string) => ({ id })),
            },
          }),
        },
        include: {
          project: {
            select: {
              id: true,
              title: true,
              ownerId: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          assignees: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          files: {
            include: {
              uploader: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      // 如果任务状态发生变化，更新项目进度
      if (status && status !== task.status) {
        await updateProjectProgress(task.projectId);

        // 如果任务状态变为已完成，通知项目负责人
        if (status === 'DONE' && task.status !== 'DONE' && updatedTask.assignee) {
          try {
            // 获取当前用户ID（任务执行者）
            const currentUserId = await getCurrentUserId(req, res);

            // 只有当任务执行者不是项目负责人时才发送通知
            if (currentUserId !== updatedTask.project.ownerId) {
              await notifyTaskCompleted(
                updatedTask.id,
                updatedTask.project.ownerId,
                updatedTask.assignee.name,
                updatedTask.title,
                updatedTask.project.title
              );
            }
          } catch (notificationError) {
            console.error('发送任务完成通知失败:', notificationError);
            // 不影响任务更新，只记录错误
          }
        }
      }

      // 如果任务重新分配给了其他用户，发送通知
      if (assigneeId && assigneeId !== task.assigneeId && updatedTask.assignee) {
        try {
          // 获取当前用户ID
          const currentUserId = await getCurrentUserId(req, res);

          // 获取当前用户信息（任务编辑者）
          const currentUser = await prisma.user.findUnique({
            where: { id: currentUserId! },
            select: { name: true },
          });

          if (currentUser && assigneeId !== currentUserId) {
            await notifyTaskAssigned(
              updatedTask.id,
              assigneeId,
              currentUser.name,
              updatedTask.title,
              updatedTask.project.title
            );
          }
        } catch (notificationError) {
          console.error('发送任务重新分配通知失败:', notificationError);
          // 不影响任务更新，只记录错误
        }
      }

      return res.status(200).json(updatedTask);
    } catch (error) {
      console.error('更新任务失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理DELETE请求 - 删除任务
  if (req.method === 'DELETE') {
    try {
      // 检查任务是否存在
      const taskToDelete = await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          project: {
            select: {
              id: true,
              ownerId: true,
            },
          },
          assignee: {
            select: {
              id: true,
            },
          },
        },
      });

      if (!taskToDelete) {
        return res.status(404).json({ message: '任务不存在' });
      }

      // 获取当前用户ID
      const currentUserId = await getCurrentUserId(req, res);
      if (!currentUserId) {
        return res.status(401).json({ message: '用户未认证' });
      }

      // 检查用户是否为管理员
      const currentUser = await prisma.user.findUnique({
        where: { id: currentUserId },
        select: { role: true },
      });
      const isUserAdmin = currentUser?.role === 'ADMIN';

      // 检查用户权限：任务负责人、项目所有者或管理员可以删除
      const canDelete =
        currentUserId === taskToDelete.assignee?.id ||  // 任务负责人
        currentUserId === taskToDelete.project.ownerId ||  // 项目所有者
        isUserAdmin;  // 管理员

      if (!canDelete) {
        return res.status(403).json({ message: '您没有权限删除此任务' });
      }

      // 删除任务（数据库会自动级联删除相关文件和通知）
      await prisma.task.delete({
        where: { id: taskId },
      });

      // 更新项目进度
      await updateProjectProgress(taskToDelete.project.id);

      return res.status(200).json({ message: '任务删除成功' });
    } catch (error) {
      console.error('删除任务失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}


