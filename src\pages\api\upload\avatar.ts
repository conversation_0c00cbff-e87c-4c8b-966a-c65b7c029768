import type { NextApiRequest, NextApiResponse } from 'next';
import { IncomingForm } from 'formidable';
import fs from 'fs';
import path from 'path';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

// 禁用默认的bodyParser，以便我们可以使用formidable解析multipart/form-data
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const userId = await getCurrentUserId(req, res);

  // 验证用户ID
  if (!userId) {
    return res.status(401).json({ message: '用户未认证' });
  }

  // 处理POST请求 - 上传头像
  if (req.method === 'POST') {
    return new Promise((resolve, reject) => {
      // 创建头像上传目录（如果不存在）
      const avatarDir = path.join(process.cwd(), 'public', 'avatars');
      if (!fs.existsSync(avatarDir)) {
        fs.mkdirSync(avatarDir, { recursive: true });
      }

      const form = new IncomingForm({
        uploadDir: avatarDir,
        keepExtensions: true,
        maxFileSize: 5 * 1024 * 1024, // 5MB限制
      });

      form.parse(req, async (err, fields, files) => {
        if (err) {
          console.error('头像上传失败:', err);
          let errorMessage = '头像上传失败';

          if (err.code === 'LIMIT_FILE_SIZE') {
            errorMessage = '头像文件大小不能超过5MB';
          } else if (err.message.includes('filter')) {
            errorMessage = '只支持图片格式的头像文件';
          } else if (err.message) {
            errorMessage = `头像上传失败: ${err.message}`;
          }

          res.status(500).json({ message: errorMessage });
          return resolve(undefined);
        }

        try {
          // 处理文件数组或单个文件
          let file;
          if (Array.isArray(files.avatar)) {
            file = files.avatar[0];
          } else {
            file = files.avatar;
          }

          if (!file) {
            res.status(400).json({ message: '请选择头像文件' });
            return resolve(undefined);
          }

          console.log('上传的文件信息:', {
            originalFilename: file.originalFilename,
            mimetype: file.mimetype,
            size: file.size,
            filepath: file.filepath,
            newFilename: file.newFilename
          });

          // 验证文件类型
          const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
          if (!allowedTypes.includes(file.mimetype || '')) {
            res.status(400).json({ message: '只支持 JPEG、PNG、GIF、WebP 格式的图片' });
            return resolve(undefined);
          }

          // 生成新的文件名
          const fileExtension = path.extname(file.originalFilename || '');
          const newFileName = `${userId}_${Date.now()}${fileExtension}`;
          const newFilePath = path.join(avatarDir, newFileName);

          // 移动文件到新位置
          fs.renameSync(file.filepath, newFilePath);

          // 生成相对URL路径
          const avatarUrl = `/avatars/${newFileName}`;

          // 删除用户之前的头像文件（如果存在）
          const currentUser = await prisma.user.findUnique({
            where: { id: userId },
            select: { avatar: true },
          });

          if (currentUser?.avatar && currentUser.avatar.startsWith('/avatars/')) {
            const oldAvatarPath = path.join(process.cwd(), 'public', currentUser.avatar);
            if (fs.existsSync(oldAvatarPath)) {
              fs.unlinkSync(oldAvatarPath);
            }
          }

          // 更新用户头像
          const updatedUser = await prisma.user.update({
            where: { id: userId },
            data: { avatar: avatarUrl },
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          });

          res.status(200).json({
            message: '头像上传成功',
            avatar: avatarUrl,
            user: updatedUser,
          });
          return resolve(undefined);
        } catch (error) {
          console.error('保存头像失败:', error);
          res.status(500).json({ message: '服务器错误，请稍后再试' });
          return resolve(undefined);
        }
      });
    });
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
