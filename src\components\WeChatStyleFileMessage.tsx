import React, { useState } from 'react';
import { FileIcon, defaultStyles } from 'react-file-icon';
import { 
  ArrowDownTrayIcon,
  EyeIcon,
  PhotoIcon,
  PlayIcon,
  DocumentIcon,
  MusicalNoteIcon,
  ArchiveBoxIcon,
  CodeBracketIcon
} from '@heroicons/react/24/outline';

interface WeChatStyleFileMessageProps {
  fileName: string;
  fileSize: number;
  fileUrl: string;
  fileType: string;
  isOwnMessage: boolean;
  onPreview?: () => void;
  onDownload?: () => void;
}

export default function WeChatStyleFileMessage({
  fileName,
  fileSize,
  fileUrl,
  fileType,
  isOwnMessage,
  onPreview,
  onDownload
}: WeChatStyleFileMessageProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // 获取文件扩展名
  const getFileExtension = (name: string) => {
    return name.split('.').pop()?.toLowerCase() || '';
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 获取文件类型信息（微信风格）
  const getFileTypeInfo = (name: string, type: string) => {
    const ext = getFileExtension(name);
    
    // 图片文件
    if (type.startsWith('image/')) {
      return {
        icon: PhotoIcon,
        color: '#10B981', // green-500
        bgColor: 'bg-green-50 dark:bg-green-900/20',
        borderColor: 'border-green-200 dark:border-green-700',
        label: '图片',
        category: 'image'
      };
    }
    
    // 视频文件
    if (type.startsWith('video/')) {
      return {
        icon: PlayIcon,
        color: '#EF4444', // red-500
        bgColor: 'bg-red-50 dark:bg-red-900/20',
        borderColor: 'border-red-200 dark:border-red-700',
        label: '视频',
        category: 'video'
      };
    }
    
    // 音频文件
    if (type.startsWith('audio/')) {
      return {
        icon: MusicalNoteIcon,
        color: '#8B5CF6', // violet-500
        bgColor: 'bg-violet-50 dark:bg-violet-900/20',
        borderColor: 'border-violet-200 dark:border-violet-700',
        label: '音频',
        category: 'audio'
      };
    }
    
    // PDF文件
    if (type === 'application/pdf') {
      return {
        icon: DocumentIcon,
        color: '#DC2626', // red-600
        bgColor: 'bg-red-50 dark:bg-red-900/20',
        borderColor: 'border-red-200 dark:border-red-700',
        label: 'PDF',
        category: 'document'
      };
    }
    
    // Office文档
    if (['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(ext)) {
      return {
        icon: DocumentIcon,
        color: '#2563EB', // blue-600
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        borderColor: 'border-blue-200 dark:border-blue-700',
        label: ext.toUpperCase(),
        category: 'document'
      };
    }
    
    // 代码文件
    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'html', 'css', 'json'].includes(ext)) {
      return {
        icon: CodeBracketIcon,
        color: '#7C3AED', // violet-600
        bgColor: 'bg-violet-50 dark:bg-violet-900/20',
        borderColor: 'border-violet-200 dark:border-violet-700',
        label: '代码',
        category: 'code'
      };
    }
    
    // 压缩文件
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
      return {
        icon: ArchiveBoxIcon,
        color: '#D97706', // amber-600
        bgColor: 'bg-amber-50 dark:bg-amber-900/20',
        borderColor: 'border-amber-200 dark:border-amber-700',
        label: '压缩包',
        category: 'archive'
      };
    }
    
    // 默认文件
    return {
      icon: DocumentIcon,
      color: '#6B7280', // gray-500
      bgColor: 'bg-gray-50 dark:bg-gray-800',
      borderColor: 'border-gray-200 dark:border-gray-600',
      label: '文件',
      category: 'file'
    };
  };

  const isImage = fileType.startsWith('image/');
  const isPreviewable = isImage || fileType === 'application/pdf' || fileType.startsWith('text/');
  
  const fileInfo = getFileTypeInfo(fileName, fileType);

  // 如果是图片，显示微信风格的图片预览
  if (isImage) {
    return (
      <div className={`max-w-xs ${isOwnMessage ? 'ml-auto' : 'mr-auto'}`}>
        <div className="relative group cursor-pointer rounded-lg overflow-hidden" onClick={onPreview}>
          {!imageLoaded && !imageError && (
            <div className="w-48 h-32 bg-gray-100 dark:bg-gray-800 flex items-center justify-center rounded-lg">
              <div className="animate-pulse flex flex-col items-center space-y-2">
                <PhotoIcon className="w-8 h-8 text-gray-400" />
                <span className="text-xs text-gray-500">加载中...</span>
              </div>
            </div>
          )}
          
          {imageError ? (
            <div className="w-48 h-32 bg-gray-100 dark:bg-gray-800 flex flex-col items-center justify-center rounded-lg border border-gray-200 dark:border-gray-700">
              <PhotoIcon className="w-8 h-8 text-gray-400 mb-2" />
              <span className="text-xs text-gray-500 text-center px-2 mb-2">
                图片加载失败
              </span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDownload?.();
                }}
                className="text-xs text-blue-500 hover:text-blue-600 px-2 py-1 rounded bg-white dark:bg-gray-700"
              >
                点击下载
              </button>
            </div>
          ) : (
            <div className="relative">
              <img
                src={fileUrl}
                alt={fileName}
                className={`max-w-48 max-h-48 rounded-lg object-cover transition-opacity duration-200 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
              />
              
              {/* 微信风格的悬停遮罩 */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 rounded-lg flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-3">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onPreview?.();
                    }}
                    className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all shadow-lg"
                    title="查看原图"
                  >
                    <EyeIcon className="w-5 h-5 text-gray-700" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDownload?.();
                    }}
                    className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all shadow-lg"
                    title="下载图片"
                  >
                    <ArrowDownTrayIcon className="w-5 h-5 text-gray-700" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* 图片信息 */}
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center space-x-2">
          <span className="truncate flex-1">{fileName}</span>
          <span>{formatFileSize(fileSize)}</span>
        </div>
      </div>
    );
  }

  // 其他文件类型显示微信风格的文件卡片
  return (
    <div className={`max-w-xs ${isOwnMessage ? 'ml-auto' : 'mr-auto'}`}>
      <div className={`
        flex items-center p-3 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02]
        ${isOwnMessage 
          ? 'bg-primary-500 text-white border-primary-600 shadow-primary-500/20' 
          : `bg-white dark:bg-gray-800 ${fileInfo.borderColor} shadow-sm hover:shadow-md`
        }
      `}>
        {/* 文件图标区域 */}
        <div className={`
          flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center mr-3 relative
          ${isOwnMessage ? 'bg-white bg-opacity-20' : fileInfo.bgColor}
        `}>
          {/* 使用react-file-icon */}
          <div className="w-8 h-8">
            <FileIcon 
              extension={getFileExtension(fileName)}
              {...defaultStyles[getFileExtension(fileName) as keyof typeof defaultStyles]}
            />
          </div>
          
          {/* 文件类型标签 */}
          <div className={`
            absolute -top-1 -right-1 px-1 py-0.5 rounded text-xs font-bold
            ${isOwnMessage 
              ? 'bg-white text-primary-600' 
              : 'text-white'
            }
          `} style={{ backgroundColor: isOwnMessage ? 'white' : fileInfo.color }}>
            {fileInfo.label}
          </div>
        </div>
        
        {/* 文件信息 */}
        <div className="flex-1 min-w-0">
          <div className={`
            text-sm font-medium truncate mb-1
            ${isOwnMessage ? 'text-white' : 'text-gray-900 dark:text-gray-100'}
          `}>
            {fileName}
          </div>
          <div className={`
            text-xs flex items-center space-x-2
            ${isOwnMessage ? 'text-white text-opacity-80' : 'text-gray-500 dark:text-gray-400'}
          `}>
            <span>{formatFileSize(fileSize)}</span>
            <span>•</span>
            <span className="uppercase font-semibold">{getFileExtension(fileName)}</span>
          </div>
        </div>
        
        {/* 操作按钮 */}
        <div className="flex-shrink-0 flex items-center space-x-1 ml-2">
          {isPreviewable && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onPreview?.();
              }}
              className={`
                p-2 rounded-full transition-all duration-200 hover:scale-110
                ${isOwnMessage 
                  ? 'text-white hover:bg-white hover:bg-opacity-20' 
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                }
              `}
              title="预览文件"
            >
              <EyeIcon className="w-4 h-4" />
            </button>
          )}
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDownload?.();
            }}
            className={`
              p-2 rounded-full transition-all duration-200 hover:scale-110
              ${isOwnMessage 
                ? 'text-white hover:bg-white hover:bg-opacity-20' 
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
              }
            `}
            title="下载文件"
          >
            <ArrowDownTrayIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
