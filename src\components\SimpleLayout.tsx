import { ReactNode } from 'react';
import Head from 'next/head';
import Navbar from './Navbar';

interface SimpleLayoutProps {
  children: ReactNode;
  title?: string;
}

export default function SimpleLayout({ children, title = 'LabSync - 团队协作管理系统' }: SimpleLayoutProps) {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content="高校计算机类团队协作管理平台" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
        <Navbar />
        
        <main className="flex-1 p-4 md:p-6">
          {children}
        </main>
      </div>
    </>
  );
}
