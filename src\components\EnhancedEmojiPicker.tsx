import React, { useState, useRef, useEffect } from 'react';
import EmojiPickerReact, { EmojiClickData, Theme } from 'emoji-picker-react';
import { FaceSmileIcon } from '@heroicons/react/24/outline';

interface EnhancedEmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  className?: string;
  buttonClassName?: string;
  disabled?: boolean;
}

export default function EnhancedEmojiPicker({
  onEmojiSelect,
  className = '',
  buttonClassName = '',
  disabled = false
}: EnhancedEmojiPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [pickerPosition, setPickerPosition] = useState({ top: 0, left: 0, placement: 'top' });
  const pickerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // 检测暗色主题
  useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };

    checkDarkMode();

    // 监听主题变化
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  // 处理表情选择
  const handleEmojiClick = (emojiData: EmojiClickData) => {
    onEmojiSelect(emojiData.emoji);
    setIsOpen(false);
  };

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 计算表情选择器位置
  const calculatePosition = () => {
    if (!buttonRef.current) return;

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const pickerWidth = 350;
    const pickerHeight = 400;
    const margin = 8;

    let top = buttonRect.top - pickerHeight - margin;
    let left = buttonRect.left + buttonRect.width / 2 - pickerWidth / 2;
    let placement = 'top';

    // 如果上方空间不够，放在下方
    if (top < margin) {
      top = buttonRect.bottom + margin;
      placement = 'bottom';
    }

    // 如果右侧空间不够，向左调整
    if (left + pickerWidth > window.innerWidth - margin) {
      left = window.innerWidth - pickerWidth - margin;
    }

    // 如果左侧空间不够，向右调整
    if (left < margin) {
      left = margin;
    }

    setPickerPosition({ top, left, placement });
  };

  const togglePicker = () => {
    if (!disabled) {
      if (!isOpen) {
        calculatePosition();
      }
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* 表情按钮 */}
      <button
        ref={buttonRef}
        type="button"
        onClick={togglePicker}
        disabled={disabled}
        className={`
          p-2 rounded-full transition-all duration-200 hover:scale-110 group
          ${disabled
            ? 'opacity-50 cursor-not-allowed'
            : 'text-gray-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700'
          }
          ${buttonClassName}
        `}
        title="添加表情"
      >
        <FaceSmileIcon className="w-5 h-5 transition-transform group-hover:scale-110" />
      </button>

      {/* 表情选择器 */}
      {isOpen && (
        <div
          ref={pickerRef}
          className="fixed z-[9999] shadow-2xl rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800"
          style={{
            top: `${pickerPosition.top}px`,
            left: `${pickerPosition.left}px`,
            maxHeight: '400px',
            maxWidth: '350px'
          }}
        >
          <EmojiPickerReact
            onEmojiClick={handleEmojiClick}
            theme={isDarkMode ? Theme.DARK : Theme.LIGHT}
            width={350}
            height={400}
            searchDisabled={false}
            skinTonesDisabled={false}
            previewConfig={{
              defaultEmoji: '1f60a',
              defaultCaption: '选择一个表情',
              showPreview: true
            }}
            lazyLoadEmojis={true}
          />
        </div>
      )}
    </div>
  );
}

// 常用表情快捷组件（微信风格）
export function WeChatQuickEmojis({
  onEmojiSelect,
  className = ''
}: {
  onEmojiSelect: (emoji: string) => void;
  className?: string;
}) {
  // 微信常用表情
  const quickEmojis = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
    '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
    '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
    '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
    '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
    '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
    '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
    '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
    '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
    '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑',
    '🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻',
    '💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸',
    '😹', '😻', '😼', '😽', '🙀', '😿', '😾', '👋',
    '🤚', '🖐️', '✋', '🖖', '👌', '🤌', '🤏', '✌️',
    '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕',
    '👇', '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜',
    '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💅'
  ];

  return (
    <div className={`${className}`}>
      <div className="p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-w-xs">
        <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 mb-2 text-center">
          常用表情
        </div>
        <div className="grid grid-cols-8 gap-1 max-h-32 overflow-y-auto">
          {quickEmojis.map((emoji, index) => (
            <button
              key={index}
              type="button"
              onClick={() => onEmojiSelect(emoji)}
              className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-lg hover:scale-110 transform duration-150"
              title={`添加 ${emoji}`}
            >
              {emoji}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}

// 表情渲染组件（增强版）
export function EnhancedEmojiRenderer({
  text,
  className = ''
}: {
  text: string;
  className?: string;
}) {
  // 更全面的表情代码映射
  const emojiMap: Record<string, string> = {
    // 基础表情
    ':)': '😊',
    ':-)': '😊',
    ':(': '😞',
    ':-(': '😞',
    ':D': '😃',
    ':-D': '😃',
    ':P': '😛',
    ':-P': '😛',
    ';)': '😉',
    ';-)': '😉',
    ':o': '😮',
    ':-o': '😮',
    ':O': '😲',
    ':-O': '😲',

    // 文字表情代码
    ':smile:': '😀',
    ':grin:': '😁',
    ':joy:': '😂',
    ':laugh:': '😂',
    ':heart_eyes:': '😍',
    ':thinking:': '🤔',
    ':cry:': '😢',
    ':angry:': '😡',
    ':thumbs_up:': '👍',
    ':thumbs_down:': '👎',
    ':heart:': '❤️',
    ':party:': '🎉',
    ':fire:': '🔥',
    ':100:': '💯',
    ':ok_hand:': '👌',
    ':clap:': '👏',
    ':pray:': '🙏',
    ':muscle:': '💪',
    ':eyes:': '👀',
    ':see_no_evil:': '🙈',
    ':hear_no_evil:': '🙉',
    ':speak_no_evil:': '🙊'
  };

  let renderedText = text;

  // 替换表情代码
  Object.entries(emojiMap).forEach(([code, emoji]) => {
    renderedText = renderedText.replace(new RegExp(code.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), emoji);
  });

  return <span className={className}>{renderedText}</span>;
}
