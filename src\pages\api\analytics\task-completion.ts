import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, isAdmin, isProjectLeader } from '@/lib/auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  // 检查用户是否为管理员或项目负责人
  const isAdminUser = await isAdmin(req, res);
  const isLeader = await isProjectLeader(req, res);

  if (!isAdminUser && !isLeader) {
    return res.status(403).json({ message: '只有管理员或项目负责人可以查看任务完成情况统计' });
  }

  // 处理GET请求 - 获取任务完成情况统计
  if (req.method === 'GET') {
    try {
      const {
        period = 'month', // week, month, year
        userId,
        projectId,
        startDate,
        endDate
      } = req.query;

      // 计算时间范围
      const now = new Date();
      let dateFilter: any = {};

      if (startDate && endDate) {
        dateFilter = {
          createdAt: {
            gte: new Date(startDate as string),
            lte: new Date(endDate as string),
          },
        };
      } else {
        switch (period) {
          case 'week':
            const weekStart = new Date(now);
            weekStart.setDate(now.getDate() - 7);
            dateFilter = {
              createdAt: {
                gte: weekStart,
                lte: now,
              },
            };
            break;
          case 'month':
            const monthStart = new Date(now);
            monthStart.setMonth(now.getMonth() - 1);
            dateFilter = {
              createdAt: {
                gte: monthStart,
                lte: now,
              },
            };
            break;
          case 'year':
            const yearStart = new Date(now);
            yearStart.setFullYear(now.getFullYear() - 1);
            dateFilter = {
              createdAt: {
                gte: yearStart,
                lte: now,
              },
            };
            break;
        }
      }

      // 构建查询条件
      const whereCondition: any = {
        ...dateFilter,
      };

      if (userId) {
        whereCondition.assigneeId = userId as string;
      }

      if (projectId) {
        whereCondition.projectId = projectId as string;
      }

      // 获取任务数据
      const tasks = await prisma.task.findMany({
        where: whereCondition,
        include: {
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          project: {
            select: {
              id: true,
              title: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // 按用户统计任务完成情况
      const userStats: Record<string, any> = {};
      const projectStats: Record<string, any> = {};
      const dailyStats: Record<string, any> = {};

      tasks.forEach(task => {
        const assigneeId = task.assigneeId || 'unassigned';
        const projectId = task.projectId;
        const dateKey = task.createdAt.toISOString().split('T')[0]; // YYYY-MM-DD

        // 用户统计
        if (!userStats[assigneeId]) {
          userStats[assigneeId] = {
            user: task.assignee,
            total: 0,
            completed: 0,
            inProgress: 0,
            todo: 0,
            review: 0,
            completionRate: 0,
          };
        }

        userStats[assigneeId].total++;
        switch (task.status) {
          case 'COMPLETED':
            userStats[assigneeId].completed++;
            break;
          case 'IN_PROGRESS':
            userStats[assigneeId].inProgress++;
            break;
          case 'TODO':
            userStats[assigneeId].todo++;
            break;
          case 'REVIEW':
            userStats[assigneeId].review++;
            break;
        }

        // 项目统计
        if (!projectStats[projectId]) {
          projectStats[projectId] = {
            project: task.project,
            total: 0,
            completed: 0,
            completionRate: 0,
          };
        }

        projectStats[projectId].total++;
        if (task.status === 'COMPLETED') {
          projectStats[projectId].completed++;
        }

        // 每日统计
        if (!dailyStats[dateKey]) {
          dailyStats[dateKey] = {
            date: dateKey,
            total: 0,
            completed: 0,
          };
        }

        dailyStats[dateKey].total++;
        if (task.status === 'COMPLETED') {
          dailyStats[dateKey].completed++;
        }
      });

      // 计算完成率
      Object.values(userStats).forEach((stat: any) => {
        stat.completionRate = stat.total > 0 ? (stat.completed / stat.total) * 100 : 0;
      });

      Object.values(projectStats).forEach((stat: any) => {
        stat.completionRate = stat.total > 0 ? (stat.completed / stat.total) * 100 : 0;
      });

      // 获取所有用户列表（用于筛选）
      const users = await prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          avatar: true,
        },
        orderBy: {
          name: 'asc',
        },
      });

      // 获取所有项目列表（用于筛选）
      const projects = await prisma.project.findMany({
        select: {
          id: true,
          title: true,
        },
        orderBy: {
          title: 'asc',
        },
      });

      return res.status(200).json({
        userStats: Object.values(userStats),
        projectStats: Object.values(projectStats),
        dailyStats: Object.values(dailyStats).sort((a: any, b: any) => a.date.localeCompare(b.date)),
        summary: {
          totalTasks: tasks.length,
          completedTasks: tasks.filter(t => t.status === 'COMPLETED').length,
          overallCompletionRate: tasks.length > 0 ? (tasks.filter(t => t.status === 'COMPLETED').length / tasks.length) * 100 : 0,
        },
        filters: {
          users,
          projects,
        },
      });
    } catch (error) {
      console.error('获取任务完成情况统计失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
