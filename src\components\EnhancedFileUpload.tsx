import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import { CloudArrowUpIcon, DocumentIcon, PhotoIcon } from '@heroicons/react/24/outline';

interface EnhancedFileUploadProps {
  projectId?: string;
  taskId?: string;
  chatId?: string;
  onUploadComplete?: (file: any) => void;
  onUploadStart?: () => void;
  onUploadError?: (error: string) => void;
  className?: string;
  maxFiles?: number;
  maxSize?: number; // bytes
  accept?: Record<string, string[]>;
  disabled?: boolean;
}

export default function EnhancedFileUpload({
  projectId,
  taskId,
  chatId,
  onUploadComplete,
  onUploadStart,
  onUploadError,
  className = '',
  maxFiles = 10,
  maxSize = 10 * 1024 * 1024, // 10MB
  accept = {
    'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
    'application/pdf': ['.pdf'],
    'text/*': ['.txt', '.md'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'application/zip': ['.zip'],
    'application/json': ['.json']
  },
  disabled = false
}: EnhancedFileUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  const uploadFile = async (file: File) => {
    const fileId = `${file.name}-${Date.now()}`;
    setUploadProgress(prev => ({ ...prev, [fileId]: 0 }));

    try {
      const formData = new FormData();
      formData.append('file', file);

      if (projectId) formData.append('projectId', projectId);
      if (taskId) formData.append('taskId', taskId);
      if (chatId) formData.append('chatId', chatId);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[fileId] || 0;
          const newProgress = Math.min(currentProgress + 10, 90);
          return { ...prev, [fileId]: newProgress };
        });
      }, 100);

      let uploadUrl = '/api/files';
      if (chatId) {
        uploadUrl = '/api/upload/chat-file';
      }

      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '文件上传失败');
      }

      const result = await response.json();

      // 完成上传
      setUploadProgress(prev => ({ ...prev, [fileId]: 100 }));

      // 显示成功通知
      toast.success(`${file.name} 上传成功！`, {
        duration: 3000,
        position: 'top-right',
      });

      // 延迟移除进度条
      setTimeout(() => {
        setUploadProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[fileId];
          return newProgress;
        });
      }, 1000);

      onUploadComplete?.(result);

    } catch (error) {
      console.error('文件上传失败:', error);
      const errorMessage = error instanceof Error ? error.message : '文件上传失败';

      // 显示错误通知
      toast.error(`${file.name} 上传失败: ${errorMessage}`, {
        duration: 4000,
        position: 'top-right',
      });

      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileId];
        return newProgress;
      });
      onUploadError?.(errorMessage);
    }
  };

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
    acceptedFiles,
    fileRejections
  } = useDropzone({
    accept,
    maxFiles,
    maxSize,
    disabled: disabled || uploading,
    onDrop: async (acceptedFiles) => {
      if (acceptedFiles.length === 0) return;

      setUploading(true);
      onUploadStart?.();

      try {
        // 并发上传文件
        await Promise.all(acceptedFiles.map(uploadFile));
      } finally {
        setUploading(false);
      }
    }
  });

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <PhotoIcon className="w-5 h-5 text-blue-500" />;
    }
    return <DocumentIcon className="w-5 h-5 text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getBorderColor = () => {
    if (isDragReject) return 'border-red-400 bg-red-50 dark:bg-red-900/20';
    if (isDragAccept) return 'border-green-400 bg-green-50 dark:bg-green-900/20';
    if (isDragActive) return 'border-primary-400 bg-primary-50 dark:bg-primary-900/20';
    return 'border-gray-300 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600';
  };

  return (
    <div className={className}>
      {/* 拖拽上传区域 */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200
          ${getBorderColor()}
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />

        <div className="space-y-3">
          <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />

          <div>
            <p className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {isDragActive
                ? isDragAccept
                  ? '释放文件以上传'
                  : '不支持的文件类型'
                : '拖拽文件到此处或点击选择'
              }
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              支持图片、PDF、文档等格式，单个文件最大 {formatFileSize(maxSize)}
            </p>
          </div>
        </div>
      </div>

      {/* 文件列表和上传进度 */}
      {(acceptedFiles.length > 0 || Object.keys(uploadProgress).length > 0) && (
        <div className="mt-4 space-y-2">
          {acceptedFiles.map((file, index) => {
            const fileId = `${file.name}-${Date.now()}`;
            const progress = uploadProgress[fileId] || 0;

            return (
              <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                {getFileIcon(file)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {formatFileSize(file.size)}
                  </p>
                  {progress > 0 && progress < 100 && (
                    <div className="mt-1">
                      <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
                        <div
                          className="bg-primary-500 h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {progress}%
                      </p>
                    </div>
                  )}
                </div>
                {progress === 100 && (
                  <div className="text-green-500">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      {/* 错误信息 */}
      {fileRejections.length > 0 && (
        <div className="mt-4 space-y-2">
          {fileRejections.map(({ file, errors }, index) => (
            <div key={index} className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm font-medium text-red-800 dark:text-red-200">
                {file.name}
              </p>
              <ul className="text-xs text-red-600 dark:text-red-400 mt-1 space-y-1">
                {errors.map((error, errorIndex) => (
                  <li key={errorIndex}>
                    {error.code === 'file-too-large' && `文件过大，最大支持 ${formatFileSize(maxSize)}`}
                    {error.code === 'file-invalid-type' && '不支持的文件类型'}
                    {error.code === 'too-many-files' && `最多只能上传 ${maxFiles} 个文件`}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
