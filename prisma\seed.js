const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('开始数据库种子数据...');

  // 创建示例用户
  const users = [
    {
      name: '张三',
      email: '<PERSON><PERSON><PERSON>@example.com',
      password: await bcrypt.hash('123456', 10),
      role: 'ADMIN',
      avatar: 'https://ui-avatars.com/api/?name=张三&background=6366f1&color=fff&size=128',
      age: 28,
      department: '技术部',
      position: '技术总监',
      bio: '负责技术架构和团队管理，有丰富的项目经验。',
      phone: '13800138001'
    },
    {
      name: '李四',
      email: '<EMAIL>',
      password: await bcrypt.hash('123456', 10),
      role: 'LEADER',
      avatar: 'https://ui-avatars.com/api/?name=李四&background=10b981&color=fff&size=128',
      age: 26,
      department: '产品部',
      position: '产品经理',
      bio: '专注于产品设计和用户体验，善于需求分析。',
      phone: '13800138002'
    },
    {
      name: '王五',
      email: '<EMAIL>',
      password: await bcrypt.hash('123456', 10),
      role: 'MEMBER',
      avatar: 'https://ui-avatars.com/api/?name=王五&background=f59e0b&color=fff&size=128',
      age: 24,
      department: '技术部',
      position: '前端开发工程师',
      bio: '专业的前端开发工程师，熟悉React、Vue等框架。',
      phone: '13800138003'
    },
    {
      name: '赵六',
      email: '<EMAIL>',
      password: await bcrypt.hash('123456', 10),
      role: 'MEMBER',
      avatar: 'https://ui-avatars.com/api/?name=赵六&background=ef4444&color=fff&size=128',
      age: 25,
      department: '技术部',
      position: '后端开发工程师',
      bio: '后端开发专家，精通Node.js、Python等技术栈。',
      phone: '13800138004'
    },
    {
      name: '孙七',
      email: '<EMAIL>',
      password: await bcrypt.hash('123456', 10),
      role: 'MEMBER',
      avatar: 'https://ui-avatars.com/api/?name=孙七&background=8b5cf6&color=fff&size=128',
      age: 23,
      department: '设计部',
      position: 'UI/UX设计师',
      bio: '创意设计师，专注于用户界面和用户体验设计。',
      phone: '13800138005'
    },
    {
      name: '周八',
      email: '<EMAIL>',
      password: await bcrypt.hash('123456', 10),
      role: 'MEMBER',
      avatar: 'https://ui-avatars.com/api/?name=周八&background=06b6d4&color=fff&size=128',
      age: 27,
      department: '测试部',
      position: '测试工程师',
      bio: '质量保证专家，确保产品质量和用户体验。',
      phone: '13800138006'
    }
  ];

  // 检查用户是否已存在，如果不存在则创建
  for (const userData of users) {
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email }
    });

    if (!existingUser) {
      const user = await prisma.user.create({
        data: userData
      });
      console.log(`创建用户: ${user.name} (${user.email})`);
    } else {
      console.log(`用户已存在: ${userData.name} (${userData.email})`);
    }
  }

  console.log('数据库种子数据完成！');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
