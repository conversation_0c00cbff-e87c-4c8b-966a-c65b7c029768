// 清理测试数据，保留三个用户
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanupTestData() {
  console.log('🧹 开始清理测试数据...\n');

  try {
    // 1. 获取所有用户
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    console.log(`📊 当前系统中有 ${allUsers.length} 个用户`);

    // 选择要保留的三个用户（管理员、项目负责人、成员各一个）
    const usersToKeep = [];

    // 保留一个管理员
    const admin = allUsers.find(user => user.role === 'ADMIN');
    if (admin) {
      usersToKeep.push(admin);
      console.log(`✅ 保留管理员: ${admin.name} (${admin.email})`);
    }

    // 保留一个项目负责人
    const leader = allUsers.find(user => user.role === 'LEADER' && user.id !== admin?.id);
    if (leader) {
      usersToKeep.push(leader);
      console.log(`✅ 保留项目负责人: ${leader.name} (${leader.email})`);
    }

    // 保留一个成员
    const member = allUsers.find(user => user.role === 'MEMBER' && user.id !== admin?.id && user.id !== leader?.id);
    if (member) {
      usersToKeep.push(member);
      console.log(`✅ 保留成员: ${member.name} (${member.email})`);
    }

    const keepUserIds = usersToKeep.map(user => user.id);
    const usersToDelete = allUsers.filter(user => !keepUserIds.includes(user.id));

    console.log(`\n🗑️  将删除 ${usersToDelete.length} 个用户:`);
    usersToDelete.forEach(user => {
      console.log(`  - ${user.name} (${user.email}) - ${user.role}`);
    });

    // 2. 删除相关数据
    console.log('\n🧹 开始清理数据...');

    // 删除聊天消息
    const deletedChatMessages = await prisma.chatMessage.deleteMany({});
    console.log(`✅ 删除了 ${deletedChatMessages.count} 条聊天消息`);

    // 删除聊天
    const deletedChats = await prisma.chat.deleteMany({});
    console.log(`✅ 删除了 ${deletedChats.count} 个聊天`);

    // 删除项目消息
    const deletedMessages = await prisma.message.deleteMany({});
    console.log(`✅ 删除了 ${deletedMessages.count} 条项目消息`);

    // 删除文件
    const deletedFiles = await prisma.file.deleteMany({});
    console.log(`✅ 删除了 ${deletedFiles.count} 个文件`);

    // 删除任务
    const deletedTasks = await prisma.task.deleteMany({});
    console.log(`✅ 删除了 ${deletedTasks.count} 个任务`);

    // 删除项目
    const deletedProjects = await prisma.project.deleteMany({});
    console.log(`✅ 删除了 ${deletedProjects.count} 个项目`);

    // 删除多余用户
    if (usersToDelete.length > 0) {
      const deleteUserIds = usersToDelete.map(user => user.id);
      const deletedUsers = await prisma.user.deleteMany({
        where: {
          id: {
            in: deleteUserIds,
          },
        },
      });
      console.log(`✅ 删除了 ${deletedUsers.count} 个用户`);
    }

    // 3. 创建基础数据
    console.log('\n🏗️  创建基础数据...');

    // 确保保留的用户状态正确
    for (const user of usersToKeep) {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          status: 'APPROVED',
        },
      });
    }

    // 创建一个示例项目
    if (usersToKeep.length >= 2) {
      const projectOwner = usersToKeep.find(u => u.role === 'LEADER') || usersToKeep[0];
      const projectMember = usersToKeep.find(u => u.id !== projectOwner.id);

      const project = await prisma.project.create({
        data: {
          title: 'LabSync 系统开发',
          description: '研究组协作管理系统的开发和维护项目',
          status: 'ACTIVE',
          startDate: new Date(),
          endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90天后
          progress: 0,
          ownerId: projectOwner.id,
          members: {
            connect: projectMember ? [{ id: projectMember.id }] : [],
          },
        },
      });

      console.log(`✅ 创建示例项目: ${project.title}`);

      // 创建示例任务
      const task = await prisma.task.create({
        data: {
          title: '系统功能测试',
          description: '测试系统的各项功能是否正常工作',
          status: 'TODO',
          priority: 'MEDIUM',
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
          projectId: project.id,
          assigneeId: projectMember?.id || projectOwner.id,
        },
      });

      console.log(`✅ 创建示例任务: ${task.title}`);
    }

    // 4. 验证清理结果
    console.log('\n📊 清理后的系统状态:');

    const finalStats = {
      users: await prisma.user.count(),
      projects: await prisma.project.count(),
      tasks: await prisma.task.count(),
      files: await prisma.file.count(),
      chats: await prisma.chat.count(),
      messages: await prisma.message.count(),
      chatMessages: await prisma.chatMessage.count(),
    };

    console.log(`✅ 最终统计:`);
    console.log(`  - 用户: ${finalStats.users} 个`);
    console.log(`  - 项目: ${finalStats.projects} 个`);
    console.log(`  - 任务: ${finalStats.tasks} 个`);
    console.log(`  - 文件: ${finalStats.files} 个`);
    console.log(`  - 聊天: ${finalStats.chats} 个`);
    console.log(`  - 项目消息: ${finalStats.messages} 条`);
    console.log(`  - 聊天消息: ${finalStats.chatMessages} 条`);

    // 显示保留的用户信息
    console.log('\n👥 保留的用户:');
    const remainingUsers = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
      },
    });

    remainingUsers.forEach(user => {
      console.log(`  - ${user.name} (${user.email}) - ${user.role} - ${user.status}`);
    });

    console.log('\n🎉 测试数据清理完成！');
    console.log('\n💡 系统现在有一个干净的环境，包含:');
    console.log('  • 3个核心用户（管理员、项目负责人、成员）');
    console.log('  • 1个示例项目');
    console.log('  • 1个示例任务');
    console.log('  • 清空的聊天和文件数据');
    console.log('\n🚀 可以开始测试新功能了！');

  } catch (error) {
    console.error('❌ 清理失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupTestData();
