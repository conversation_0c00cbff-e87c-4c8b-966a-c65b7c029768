<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E5E7EB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D1D5DB;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="60" cy="60" r="60" fill="url(#avatarGradient)"/>
  
  <!-- 用户图标 -->
  <g transform="translate(30, 25)">
    <!-- 头部 -->
    <circle cx="30" cy="25" r="15" fill="#9CA3AF"/>
    
    <!-- 身体 -->
    <path d="M 10 55 Q 10 45 20 45 L 40 45 Q 50 45 50 55 L 50 70 L 10 70 Z" fill="#9CA3AF"/>
  </g>
</svg>
