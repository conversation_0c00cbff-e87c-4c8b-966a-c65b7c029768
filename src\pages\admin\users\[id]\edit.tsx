import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';

type FormData = {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: string;
};

// 用户类型定义
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

export default function EditUser() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { id } = router.query;

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FormData>();

  const password = watch('password');

  // 如果未登录或不是管理员，重定向到首页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    } else if (status === 'authenticated' && session?.user?.role !== 'ADMIN') {
      router.push('/');
    }
  }, [status, session, router]);

  // 获取用户详情
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role === 'ADMIN' && id) {
      fetchUserDetails();
    }
  }, [status, session, id]);

  const fetchUserDetails = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/users/${id}`);

      if (!response.ok) {
        throw new Error('获取用户详情失败');
      }

      const data = await response.json();
      setUser(data);

      // 设置表单默认值
      setValue('name', data.name);
      setValue('email', data.email);
      setValue('role', data.role);
    } catch (error) {
      console.error('获取用户详情失败:', error);
      setError('获取用户详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: FormData) => {
    setSubmitting(true);
    setError('');

    try {
      const updateData: any = {
        name: data.name,
        email: data.email,
        role: data.role,
      };

      // 只有在填写了密码的情况下才更新密码
      if (data.password) {
        updateData.password = data.password;
      }

      const response = await fetch(`/api/users/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '更新用户失败');
      }

      // 更新成功后跳转到用户详情页
      router.push(`/admin/users/${id}`);
    } catch (error) {
      console.error('更新用户失败:', error);
      setError(error instanceof Error ? error.message : '更新用户失败，请稍后再试');
    } finally {
      setSubmitting(false);
    }
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated' || (status === 'authenticated' && session?.user?.role !== 'ADMIN')) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 加载中
  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 用户不存在
  if (!user) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">用户不存在或您没有权限访问</span>
        </div>
        <div className="mt-4">
          <Link href="/admin/users" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回用户列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          编辑用户
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          修改用户 "{user.name}" 的信息
        </p>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <label className="form-label" htmlFor="name">
              姓名 <span className="text-red-500">*</span>
            </label>
            <input
              id="name"
              type="text"
              className="form-input"
              placeholder="输入用户姓名"
              {...register('name', {
                required: '请输入姓名',
                minLength: {
                  value: 2,
                  message: '姓名至少需要2个字符',
                },
              })}
            />
            {errors.name && (
              <p className="form-error">{errors.name.message}</p>
            )}
          </div>

          <div className="mb-4">
            <label className="form-label" htmlFor="email">
              邮箱地址 <span className="text-red-500">*</span>
            </label>
            <input
              id="email"
              type="email"
              className="form-input"
              placeholder="输入邮箱地址"
              {...register('email', {
                required: '请输入邮箱地址',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: '请输入有效的邮箱地址',
                },
              })}
            />
            {errors.email && (
              <p className="form-error">{errors.email.message}</p>
            )}
          </div>

          <div className="mb-4">
            <label className="form-label" htmlFor="password">
              密码 <span className="text-gray-500">(留空表示不修改)</span>
            </label>
            <input
              id="password"
              type="password"
              className="form-input"
              placeholder="输入新密码"
              {...register('password', {
                minLength: {
                  value: 6,
                  message: '密码长度至少为6个字符',
                },
              })}
            />
            {errors.password && (
              <p className="form-error">{errors.password.message}</p>
            )}
          </div>

          <div className="mb-4">
            <label className="form-label" htmlFor="confirmPassword">
              确认密码
            </label>
            <input
              id="confirmPassword"
              type="password"
              className="form-input"
              placeholder="再次输入新密码"
              {...register('confirmPassword', {
                validate: value => !password || value === password || '两次输入的密码不一致',
              })}
            />
            {errors.confirmPassword && (
              <p className="form-error">{errors.confirmPassword.message}</p>
            )}
          </div>

          <div className="mb-6">
            <label className="form-label" htmlFor="role">
              用户角色 <span className="text-red-500">*</span>
            </label>
            <select
              id="role"
              className="form-input"
              {...register('role', {
                required: '请选择用户角色',
              })}
            >
              <option value="ADMIN">管理员</option>
              <option value="LEADER">项目负责人</option>
              <option value="MEMBER">项目成员</option>
              <option value="GUEST">访客</option>
            </select>
            {errors.role && (
              <p className="form-error">{errors.role.message}</p>
            )}
          </div>

          <div className="flex justify-end space-x-4">
            <Link
              href={`/admin/users/${id}`}
              className="btn btn-secondary"
            >
              取消
            </Link>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={submitting}
            >
              {submitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  更新中...
                </>
              ) : '更新用户'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
