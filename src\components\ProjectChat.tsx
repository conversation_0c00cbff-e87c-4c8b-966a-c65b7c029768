import React, { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { gsap } from 'gsap';
import {
  PaperClipIcon,
  PhotoIcon,
  DocumentIcon,
  ArrowDownTrayIcon,
  XMarkIcon,
  PaperAirplaneIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import Avatar from './Avatar';
import SimpleFileUpload from './SimpleFileUpload';
import ChatFilePreview from './ChatFilePreview';
import WeChatStyleFileMessage from './WeChatStyleFileMessage';
import EnhancedEmojiPicker, { EnhancedEmojiRenderer, WeChatQuickEmojis } from './EnhancedEmojiPicker';
import ImageLightbox from './ImageLightbox';

interface Message {
  id: string;
  content: string;
  type: string;
  fileName?: string;
  fileUrl?: string;
  fileSize?: number;
  createdAt: string;
  sender: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
}

interface ProjectChatProps {
  projectId: string;
  projectTitle: string;
}

const ProjectChat: React.FC<ProjectChatProps> = ({ projectId, projectTitle }) => {
  const { data: session } = useSession();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [showFileMenu, setShowFileMenu] = useState(false);
  const [error, setError] = useState('');
  const [previewFile, setPreviewFile] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [lightboxImages, setLightboxImages] = useState<Array<{src: string; alt?: string; title?: string}>>([]);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [showLightbox, setShowLightbox] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const fileMenuRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 获取消息
  const fetchMessages = async () => {
    try {
      setError('');
      const response = await fetch(`/api/projects/${projectId}/messages`);

      if (!response.ok) {
        throw new Error('获取消息失败');
      }

      const data = await response.json();
      setMessages(data.messages || []);
    } catch (error) {
      console.error('获取消息失败:', error);
      setError('获取消息失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 发送消息
  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || sending) return;

    setSending(true);
    setError('');

    try {
      const response = await fetch(`/api/projects/${projectId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newMessage.trim(),
          type: 'TEXT',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '发送消息失败');
      }

      const message = await response.json();
      setMessages(prev => [...prev, message]);
      setNewMessage('');

      // 使用GSAP动画滚动到底部
      setTimeout(() => {
        if (messagesEndRef.current) {
          gsap.to(chatContainerRef.current, {
            scrollTop: chatContainerRef.current?.scrollHeight,
            duration: 0.5,
            ease: "power2.out"
          });
        }
      }, 100);
    } catch (error) {
      console.error('发送消息失败:', error);
      setError(error instanceof Error ? error.message : '发送消息失败，请稍后再试');
    } finally {
      setSending(false);
    }
  };

  // 添加表情到消息
  const handleEmojiSelect = (emoji: string) => {
    setNewMessage(prev => prev + emoji);
  };

  // 处理图片预览
  const handleImagePreview = (message: Message) => {
    if (!message.fileUrl || !message.fileName) return;

    // 收集所有图片消息
    const imageMessages = messages.filter(msg =>
      msg.type === 'FILE' &&
      msg.fileName &&
      getFileType(msg.fileName).startsWith('image/')
    );

    const images = imageMessages.map(msg => ({
      src: msg.fileUrl!,
      alt: msg.fileName!,
      title: msg.fileName!
    }));

    const currentIndex = imageMessages.findIndex(msg => msg.id === message.id);

    setLightboxImages(images);
    setLightboxIndex(Math.max(0, currentIndex));
    setShowLightbox(true);
  };

  // 处理文件下载
  const handleFileDownload = (fileUrl: string, fileName?: string) => {
    const a = document.createElement('a');
    a.href = fileUrl;
    a.download = fileName || 'download';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    if (!file) return;

    setIsUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('file', file);

      const uploadResponse = await fetch('/api/upload/file', {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.message || '文件上传失败');
      }

      const fileData = await uploadResponse.json();

      // 发送文件消息
      const messageResponse = await fetch(`/api/projects/${projectId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: fileData.fileName,
          type: 'FILE',
          fileName: fileData.fileName,
          fileUrl: fileData.fileUrl,
          fileSize: fileData.fileSize,
        }),
      });

      if (!messageResponse.ok) {
        const errorData = await messageResponse.json();
        throw new Error(errorData.message || '发送文件消息失败');
      }

      const message = await messageResponse.json();
      setMessages(prev => [...prev, message]);

      // 动画滚动到底部
      setTimeout(() => {
        if (messagesEndRef.current) {
          gsap.to(chatContainerRef.current, {
            scrollTop: chatContainerRef.current?.scrollHeight,
            duration: 0.5,
            ease: "power2.out"
          });
        }
      }, 100);

    } catch (error) {
      console.error('文件上传失败:', error);
      setError(error instanceof Error ? error.message : '文件上传失败，请稍后再试');
    } finally {
      setIsUploading(false);
      setShowFileMenu(false);
    }
  };

  // 文件选择处理
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
    // 重置input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };



  // 格式化时间
  const formatTime = (dateString: string) => {
    return format(new Date(dateString), 'HH:mm', { locale: zhCN });
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件图标
  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext || '')) {
      return <PhotoIcon className="w-6 h-6" />;
    }
    return <DocumentIcon className="w-6 h-6" />;
  };

  // 检查文件是否可预览
  const isPreviewable = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'txt', 'md', 'json'].includes(ext || '');
  };

  // 预览文件
  const previewChatFile = (message: Message) => {
    if (!message.fileUrl || !message.fileName) return;

    // 构造文件对象用于预览
    const fileForPreview = {
      id: message.id,
      name: message.fileName,
      type: getFileType(message.fileName),
      size: message.fileSize || 0,
      path: message.fileUrl,
      url: message.fileUrl
    };

    setPreviewFile(fileForPreview);
    setShowPreview(true);
  };

  // 获取文件类型
  const getFileType = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'pdf':
        return 'application/pdf';
      case 'txt':
        return 'text/plain';
      case 'md':
        return 'text/markdown';
      case 'json':
        return 'application/json';
      default:
        return 'application/octet-stream';
    }
  };

  useEffect(() => {
    fetchMessages();
  }, [projectId]);

  useEffect(() => {
    // 新消息动画效果
    if (messages.length > 0) {
      const lastMessage = chatContainerRef.current?.lastElementChild?.previousElementSibling;
      if (lastMessage) {
        gsap.fromTo(lastMessage,
          { opacity: 0, y: 20, scale: 0.95 },
          { opacity: 1, y: 0, scale: 1, duration: 0.3, ease: "back.out(1.7)" }
        );
      }
      scrollToBottom();
    }
  }, [messages]);

  // 文件菜单动画
  useEffect(() => {
    if (showFileMenu && fileMenuRef.current) {
      gsap.fromTo(fileMenuRef.current,
        { opacity: 0, scale: 0.8, y: 10 },
        { opacity: 1, scale: 1, y: 0, duration: 0.2, ease: "back.out(1.7)" }
      );
    }
  }, [showFileMenu]);

  // 定期刷新消息（简单的轮询）
  useEffect(() => {
    const interval = setInterval(() => {
      fetchMessages();
    }, 10000); // 每10秒刷新一次

    return () => clearInterval(interval);
  }, [projectId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden backdrop-blur-sm">
      {/* 聊天头部 - 紧凑设计 */}
      <div className="flex-shrink-0 px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 via-white to-gray-50 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800 rounded-t-2xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
            <div className="relative flex-shrink-0">
              <div className="w-3 h-3 sm:w-4 sm:h-4 bg-gradient-to-r from-green-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
              <div className="absolute inset-0 w-3 h-3 sm:w-4 sm:h-4 bg-green-400 rounded-full animate-ping opacity-75"></div>
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-gray-100 truncate">
                💬 {projectTitle}
              </h3>
              <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 flex items-center space-x-1">
                <span>{messages.length} 条消息</span>
                <span className="hidden sm:inline">•</span>
                <span className="hidden sm:inline">实时讨论</span>
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2 flex-shrink-0">
            <div className="px-2 sm:px-3 py-1 sm:py-1.5 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 rounded-full text-xs font-semibold border border-green-200 dark:border-green-800">
              <div className="flex items-center space-x-1">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                <span className="hidden sm:inline">在线</span>
                <span className="sm:hidden">●</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="flex-shrink-0 mx-4 mt-2 p-2 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 rounded text-sm">
          {error}
        </div>
      )}

      {/* 消息列表 - 确保占用剩余空间 */}
      <div
        ref={chatContainerRef}
        className="flex-1 min-h-0 overflow-y-auto p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6 bg-gradient-to-b from-gray-50/50 via-white to-gray-50/50 dark:from-gray-900/50 dark:via-gray-800 dark:to-gray-900/50 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent"
        style={{
          backgroundImage: `
            radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.05) 0%, transparent 50%)
          `
        }}
      >
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-16">
            <div className="relative mx-auto mb-6 w-20 h-20">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
                <svg className="w-10 h-10 text-blue-500 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">💬</span>
              </div>
            </div>
            <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">开始项目讨论</h4>
            <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto leading-relaxed">
              与团队成员实时交流项目进展、分享想法和文件。支持文本消息和文件上传。
            </p>
            <div className="mt-6 flex justify-center space-x-4 text-xs text-gray-400 dark:text-gray-500">
              <div className="flex items-center space-x-1">
                <span>💬</span>
                <span>实时消息</span>
              </div>
              <div className="flex items-center space-x-1">
                <span>📎</span>
                <span>文件分享</span>
              </div>
              <div className="flex items-center space-x-1">
                <span>⚡</span>
                <span>快速响应</span>
              </div>
            </div>
          </div>
        ) : (
          messages.map((message) => {
            const isOwnMessage = message.sender.id === session?.user?.id;

            return (
              <div
                key={message.id}
                className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex max-w-xs lg:max-w-md ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
                  {/* 头像 */}
                  <div className="flex-shrink-0">
                    <Avatar
                      user={{
                        id: message.sender.id,
                        name: message.sender.name,
                        avatar: message.sender.avatar
                      }}
                      size="md"
                      className="border-2 border-white dark:border-gray-800 shadow-sm"
                    />
                  </div>

                  {/* 消息内容 */}
                  <div className={`mx-3 ${isOwnMessage ? 'text-right' : 'text-left'}`}>
                    <div className={`inline-block px-5 py-3 rounded-2xl shadow-lg max-w-xs sm:max-w-md lg:max-w-lg ${
                      isOwnMessage
                        ? 'bg-gradient-to-br from-primary-500 to-primary-600 text-white rounded-br-md shadow-primary-500/25'
                        : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700 rounded-bl-md shadow-gray-200/50 dark:shadow-gray-900/50'
                    }`}>
                      {message.type === 'FILE' ? (
                        <WeChatStyleFileMessage
                          fileName={message.fileName || ''}
                          fileSize={message.fileSize || 0}
                          fileUrl={message.fileUrl || ''}
                          fileType={getFileType(message.fileName || '')}
                          isOwnMessage={isOwnMessage}
                          onPreview={() => {
                            const fileType = getFileType(message.fileName || '');
                            if (fileType.startsWith('image/')) {
                              handleImagePreview(message);
                            } else {
                              previewChatFile(message);
                            }
                          }}
                          onDownload={() => handleFileDownload(message.fileUrl || '', message.fileName)}
                        />
                      ) : (
                        <EnhancedEmojiRenderer
                          text={message.content}
                          className="text-sm whitespace-pre-wrap leading-relaxed font-medium"
                        />
                      )}
                    </div>
                    <div className={`text-xs text-gray-500 dark:text-gray-400 mt-2 px-1 ${
                      isOwnMessage ? 'text-right' : 'text-left'
                    }`}>
                      {!isOwnMessage && (
                        <span className="font-semibold text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full mr-2">
                          {message.sender.name}
                        </span>
                      )}
                      <span className="opacity-75">{formatTime(message.createdAt)}</span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 消息输入框 - 紧凑设计 */}
      <div className="flex-shrink-0 p-3 sm:p-4 lg:p-6 border-t border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-b-2xl">
        {/* 文件上传进度 */}
        {isUploading && (
          <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-200 dark:border-blue-800 rounded-xl shadow-sm">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent"></div>
                <div className="absolute inset-0 rounded-full bg-blue-500/20 animate-pulse"></div>
              </div>
              <div className="flex-1">
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">正在上传文件...</span>
                <div className="mt-1 w-full bg-blue-200 dark:bg-blue-800 rounded-full h-1.5">
                  <div className="bg-blue-500 h-1.5 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={sendMessage} className="relative">
          <div className="flex items-end space-x-2 sm:space-x-3">
            {/* 文件上传按钮 */}
            <div className="relative flex-shrink-0">
              <button
                type="button"
                onClick={() => setShowFileMenu(!showFileMenu)}
                disabled={isUploading || sending}
                className="group relative p-2 sm:p-3 text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95"
                title="上传文件"
              >
                <PaperClipIcon className="w-4 h-4 sm:w-5 sm:h-5 transition-transform group-hover:rotate-12" />
                <div className="absolute inset-0 rounded-xl bg-primary-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
              </button>

              {/* 文件菜单 */}
              {showFileMenu && (
                <div
                  ref={fileMenuRef}
                  className="absolute bottom-full left-0 mb-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl py-2 min-w-[180px] z-20 backdrop-blur-sm"
                  style={{ boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' }}
                >
                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b border-gray-100 dark:border-gray-700">
                    选择文件类型
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      fileInputRef.current?.click();
                      setShowFileMenu(false);
                    }}
                    className="w-full px-4 py-3 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 flex items-center space-x-3 transition-all duration-200 group"
                  >
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-800/40 transition-colors">
                      <DocumentIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <div className="font-medium">上传文档</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">PDF, Word, Excel, TXT</div>
                    </div>
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      fileInputRef.current?.click();
                      setShowFileMenu(false);
                    }}
                    className="w-full px-4 py-3 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/20 dark:hover:to-emerald-900/20 flex items-center space-x-3 transition-all duration-200 group"
                  >
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg group-hover:bg-green-200 dark:group-hover:bg-green-800/40 transition-colors">
                      <PhotoIcon className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <div className="font-medium">上传图片</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">JPG, PNG, GIF, WebP</div>
                    </div>
                  </button>
                </div>
              )}
            </div>

            {/* 消息输入框容器 */}
            <div className="flex-1 relative min-w-0">
              <div className="relative">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      sendMessage(e);
                    }
                  }}
                  placeholder="输入消息... (Enter发送)"
                  className="w-full pl-3 sm:pl-4 pr-12 sm:pr-16 py-3 sm:py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl sm:rounded-2xl shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 placeholder-gray-400 dark:placeholder-gray-500 resize-none text-sm sm:text-base"
                  maxLength={1000}
                  disabled={sending || isUploading}
                />
                {/* 字符计数 */}
                <div className={`absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 text-xs transition-colors duration-200 ${
                  newMessage.length > 900
                    ? 'text-red-500 dark:text-red-400'
                    : newMessage.length > 800
                    ? 'text-yellow-500 dark:text-yellow-400'
                    : 'text-gray-400 dark:text-gray-500'
                }`}>
                  <span className="hidden sm:inline">{newMessage.length}/1000</span>
                  <span className="sm:hidden">{newMessage.length}</span>
                </div>
              </div>
            </div>

            {/* 表情选择器 */}
            <div className="flex-shrink-0">
              <EnhancedEmojiPicker
                onEmojiSelect={handleEmojiSelect}
                disabled={sending || isUploading}
                className="mr-2"
              />
            </div>

            {/* 发送按钮 */}
            <div className="relative flex-shrink-0">
              <button
                type="submit"
                disabled={!newMessage.trim() || sending || isUploading}
                className="group relative p-3 sm:p-4 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-xl sm:rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 disabled:transform-none overflow-hidden"
              >
                {/* 背景动画效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary-400 to-primary-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* 发送状态指示 */}
                <div className="relative z-10 flex items-center justify-center">
                  {sending ? (
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-2 border-white border-t-transparent"></div>
                      <span className="text-xs sm:text-sm font-medium hidden sm:block">发送中</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <PaperAirplaneIcon className="w-4 h-4 sm:w-5 sm:h-5 transform group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-200" />
                      <span className="text-xs sm:text-sm font-medium hidden lg:block">发送</span>
                    </div>
                  )}
                </div>

                {/* 涟漪效果 */}
                <div className="absolute inset-0 rounded-xl sm:rounded-2xl opacity-0 group-active:opacity-100 transition-opacity duration-150">
                  <div className="absolute inset-0 bg-white/20 rounded-xl sm:rounded-2xl animate-ping"></div>
                </div>
              </button>

              {/* 快捷键提示 */}
              {!sending && !isUploading && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-gray-800 dark:bg-gray-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap hidden sm:block">
                  Enter 发送
                </div>
              )}
            </div>
          </div>
        </form>

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,.pdf,.doc,.docx,.txt,.zip"
        />
      </div>

      {/* 文件预览模态框 */}
      {previewFile && (
        <ChatFilePreview
          file={previewFile}
          isOpen={showPreview}
          onClose={() => {
            setShowPreview(false);
            setPreviewFile(null);
          }}
        />
      )}

      {/* 图片查看器 */}
      {showLightbox && (
        <ImageLightbox
          images={lightboxImages}
          currentIndex={lightboxIndex}
          onClose={() => setShowLightbox(false)}
          onDownload={handleFileDownload}
        />
      )}
    </div>
  );
};

export default ProjectChat;
