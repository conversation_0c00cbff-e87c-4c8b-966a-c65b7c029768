// 修复用户状态的脚本
// 这个脚本会检查所有用户的状态，并修复那些状态不正确的用户

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixUserStatus() {
  try {
    console.log('🔍 检查用户状态...');
    
    // 获取所有用户
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        createdAt: true,
        usedInviteCodeId: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    console.log(`📊 找到 ${users.length} 个用户`);
    
    let fixedCount = 0;
    
    for (const user of users) {
      console.log(`\n👤 检查用户: ${user.name} (${user.email})`);
      console.log(`   当前状态: ${user.status || 'NULL'}`);
      console.log(`   角色: ${user.role}`);
      console.log(`   创建时间: ${user.createdAt.toISOString()}`);
      console.log(`   邀请码ID: ${user.usedInviteCodeId || 'NULL'}`);
      
      let shouldUpdate = false;
      let newStatus = user.status;
      let reason = '';
      
      // 检查状态是否需要修复
      if (!user.status) {
        // 如果状态为空，根据角色和创建方式判断
        if (user.role === 'ADMIN') {
          newStatus = 'APPROVED';
          reason = '管理员用户自动审核通过';
          shouldUpdate = true;
        } else if (!user.usedInviteCodeId) {
          // 没有邀请码ID，可能是管理员创建的
          newStatus = 'APPROVED';
          reason = '管理员创建的用户自动审核通过';
          shouldUpdate = true;
        } else {
          // 有邀请码ID，是通过注册的，设置为待审核
          newStatus = 'PENDING';
          reason = '注册用户设置为待审核状态';
          shouldUpdate = true;
        }
      } else if (user.status === 'PENDING' && user.role === 'ADMIN') {
        // 管理员不应该是待审核状态
        newStatus = 'APPROVED';
        reason = '管理员用户自动审核通过';
        shouldUpdate = true;
      }
      
      if (shouldUpdate) {
        console.log(`   🔧 需要更新: ${user.status || 'NULL'} -> ${newStatus}`);
        console.log(`   📝 原因: ${reason}`);
        
        try {
          await prisma.user.update({
            where: { id: user.id },
            data: { 
              status: newStatus,
              approvedAt: newStatus === 'APPROVED' ? new Date() : null,
            },
          });
          
          console.log(`   ✅ 更新成功`);
          fixedCount++;
        } catch (error) {
          console.log(`   ❌ 更新失败: ${error.message}`);
        }
      } else {
        console.log(`   ✅ 状态正确，无需更新`);
      }
    }
    
    console.log(`\n🎉 修复完成！`);
    console.log(`📊 总用户数: ${users.length}`);
    console.log(`🔧 修复用户数: ${fixedCount}`);
    
    // 显示最终状态统计
    const finalStats = await prisma.user.groupBy({
      by: ['status'],
      _count: {
        id: true,
      },
    });
    
    console.log(`\n📈 最终用户状态统计:`);
    finalStats.forEach(stat => {
      console.log(`   ${stat.status || 'NULL'}: ${stat._count.id} 个用户`);
    });
    
  } catch (error) {
    console.error('❌ 修复过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行修复脚本
fixUserStatus();
