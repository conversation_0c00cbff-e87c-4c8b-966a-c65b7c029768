# LabSync 微信小程序手动测试清单

## 🧪 测试环境准备

### 1. 开发工具设置
- [ ] 微信开发者工具已安装
- [ ] 项目已导入（目录：`_miniprogram`）
- [ ] 已勾选"不校验合法域名"
- [ ] 当前环境设置为 `development`

### 2. 编译检查
- [ ] 项目编译无错误
- [ ] 控制台无严重警告
- [ ] 模拟器正常显示

## 🔐 登录流程测试

### 登录页面 (`pages/login`)
- [ ] 页面正常加载
- [ ] 输入框可以正常输入
- [ ] 表单验证正常工作
- [ ] 使用测试账号登录：
  - 邮箱: `<EMAIL>`
  - 密码: `123456`
- [ ] 登录成功提示显示
- [ ] 自动跳转到首页

### 预期结果
```
✅ 显示 "登录成功（模拟）"
✅ 跳转到首页
✅ 首页显示开发模式指示器
```

## 🏠 首页功能测试

### 页面加载 (`pages/index`)
- [ ] 开发模式指示器显示："🔧 开发模式 - 使用模拟数据"
- [ ] 用户信息正确显示
- [ ] 统计卡片数据显示
- [ ] 最近活动列表显示

### 统计数据检查
- [ ] 总项目数: 应显示数字
- [ ] 活跃项目: 应显示数字
- [ ] 待办任务: 应显示数字
- [ ] 已完成任务: 应显示数字
- [ ] 未读消息: 应显示数字

### 快捷操作测试
- [ ] 点击"新建项目"按钮
- [ ] 点击"我的任务"按钮
- [ ] 点击"团队聊天"按钮
- [ ] 点击"文件管理"按钮

### 最近活动
- [ ] 活动列表显示
- [ ] 时间格式正确
- [ ] 活动内容显示完整

## 📋 项目管理测试

### 项目列表 (`pages/projects`)
- [ ] 切换到项目Tab成功
- [ ] 项目列表正常显示
- [ ] 项目卡片信息完整
- [ ] 进度条显示正确

### 搜索功能
- [ ] 搜索框可以输入
- [ ] 输入"LabSync"进行搜索
- [ ] 搜索结果正确筛选

### 筛选功能
- [ ] 状态筛选器工作正常
- [ ] 筛选"进行中"项目
- [ ] 筛选"已完成"项目
- [ ] 清除筛选条件

### 项目详情
- [ ] 点击项目卡片进入详情
- [ ] 项目信息显示完整
- [ ] 成员列表显示
- [ ] 任务列表显示
- [ ] 返回按钮正常

## ✅ 任务管理测试

### 任务列表 (`pages/tasks`)
- [ ] 切换到任务Tab成功
- [ ] 任务列表正常显示
- [ ] 任务状态标识清晰
- [ ] 优先级显示正确

### 状态筛选
- [ ] "全部"状态显示所有任务
- [ ] "待办"状态筛选
- [ ] "进行中"状态筛选
- [ ] "已完成"状态筛选

### 任务详情
- [ ] 点击任务进入详情页
- [ ] 任务信息完整显示
- [ ] 状态可以更新
- [ ] 返回功能正常

## 💬 聊天功能测试

### 聊天列表 (`pages/chat`)
- [ ] 切换到聊天Tab成功
- [ ] 聊天列表正常显示
- [ ] 群聊和私聊区分明确
- [ ] 未读消息数量显示

### 聊天功能
- [ ] 最后消息预览显示
- [ ] 时间格式正确
- [ ] 群聊显示发送者姓名
- [ ] 点击进入聊天详情

### 聊天详情
- [ ] 聊天详情页正常加载
- [ ] 消息列表显示
- [ ] 输入框可以输入
- [ ] 发送按钮响应

## 👤 个人中心测试

### 个人信息 (`pages/profile`)
- [ ] 切换到我的Tab成功
- [ ] 用户头像显示
- [ ] 用户信息完整
- [ ] 统计数据显示

### 功能菜单
- [ ] 消息通知入口
- [ ] 我的文件入口
- [ ] 团队管理入口
- [ ] 设置菜单入口

### 设置功能
- [ ] 点击设置按钮
- [ ] 设置菜单弹出
- [ ] 各设置项可点击
- [ ] 退出登录功能

### 头像上传
- [ ] 点击头像
- [ ] 选择图片功能
- [ ] 上传成功提示

## 🔄 页面导航测试

### TabBar导航
- [ ] 首页Tab切换正常
- [ ] 项目Tab切换正常
- [ ] 任务Tab切换正常
- [ ] 聊天Tab切换正常
- [ ] 我的Tab切换正常

### 页面跳转
- [ ] 详情页跳转正常
- [ ] 返回按钮正常
- [ ] 页面栈管理正常

## 🌐 网络状态测试

### 模拟网络异常
- [ ] 在开发者工具中模拟网络断开
- [ ] 检查错误提示
- [ ] 检查模拟数据是否正常使用
- [ ] 网络恢复后功能正常

## 📱 设备兼容性测试

### 不同设备测试
- [ ] iPhone模拟器测试
- [ ] Android模拟器测试
- [ ] 不同屏幕尺寸适配

### 真机测试
- [ ] 扫码在真机上预览
- [ ] 功能在真机上正常
- [ ] 性能表现良好

## 🐛 错误处理测试

### 异常情况
- [ ] 网络错误处理
- [ ] API错误处理
- [ ] 数据为空的处理
- [ ] 用户操作错误处理

## ✅ 测试结果记录

### 通过的测试项
- [ ] 登录流程: ✅/❌
- [ ] 首页功能: ✅/❌
- [ ] 项目管理: ✅/❌
- [ ] 任务管理: ✅/❌
- [ ] 聊天功能: ✅/❌
- [ ] 个人中心: ✅/❌
- [ ] 页面导航: ✅/❌
- [ ] 网络处理: ✅/❌

### 发现的问题
```
问题1: [描述问题]
重现步骤: [步骤]
预期结果: [预期]
实际结果: [实际]

问题2: [描述问题]
...
```

### 测试总结
- 总测试项: ___
- 通过项: ___
- 失败项: ___
- 通过率: ___%

---

**测试完成时间**: ___________
**测试人员**: ___________
**测试环境**: 微信开发者工具 + 开发模式
