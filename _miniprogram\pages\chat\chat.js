// 聊天列表页面
const { chatApi } = require('../../utils/api.js');
const { formatRelativeTime } = require('../../utils/util.js');
const app = getApp();

Page({
  data: {
    chats: [],
    loading: true,
    refreshing: false,
    searchKeyword: '',
    unreadCount: 0
  },

  onLoad() {
    this.checkLogin();
  },

  onShow() {
    if (app.isLoggedIn()) {
      this.loadChats();
      this.loadUnreadCount();
    }
  },

  // 检查登录状态
  checkLogin() {
    if (!app.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    this.loadChats();
  },

  // 加载聊天列表
  async loadChats(refresh = false) {
    this.setData({ loading: refresh ? false : true, refreshing: refresh });

    try {
      const response = await chatApi.getChatList();
      const chats = response.data || [];

      this.setData({
        chats: chats,
        loading: false,
        refreshing: false
      });

    } catch (error) {
      // 检查是否为API不可用错误
      if (error.message === 'API_UNAVAILABLE') {
        console.log('API服务器未连接，使用模拟数据');
      } else {
        console.error('加载聊天列表失败:', error);
      }

      // 使用模拟数据
      const mockChats = this.getMockChats();
      this.setData({
        chats: mockChats,
        loading: false,
        refreshing: false
      });
    }
  },

  // 加载未读消息数量
  async loadUnreadCount() {
    try {
      const response = await chatApi.getUnreadCount();
      this.setData({
        unreadCount: response.data?.count || 0
      });
    } catch (error) {
      // 静默处理错误
      this.setData({
        unreadCount: 3 // 模拟数据
      });
    }
  },

  // 获取模拟聊天数据
  getMockChats() {
    return [
      {
        id: 1,
        name: 'LabSync项目组',
        type: 'GROUP',
        avatar: '',
        lastMessage: {
          content: '今天的会议改到下午3点',
          sendTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          sender: { name: '张三' }
        },
        unreadCount: 2,
        participants: [
          { id: 1, name: '张三' },
          { id: 2, name: '李四' },
          { id: 3, name: '王五' }
        ]
      },
      {
        id: 2,
        name: '李四',
        type: 'PRIVATE',
        avatar: '',
        lastMessage: {
          content: '设计稿已经完成，请查看',
          sendTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          sender: { name: '李四' }
        },
        unreadCount: 1,
        participants: [
          { id: 2, name: '李四' }
        ]
      },
      {
        id: 3,
        name: '数据分析小组',
        type: 'GROUP',
        avatar: '',
        lastMessage: {
          content: '新的数据集已上传',
          sendTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          sender: { name: '王五' }
        },
        unreadCount: 0,
        participants: [
          { id: 3, name: '王五' },
          { id: 4, name: '赵六' }
        ]
      }
    ];
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.filterChats();
  },

  // 筛选聊天
  filterChats() {
    // 这里可以实现本地搜索逻辑
    // 或者调用API进行服务器端搜索
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadChats(true);
    this.loadUnreadCount();
    wx.stopPullDownRefresh();
  },

  // 跳转到聊天详情
  goToChatDetail(e) {
    const chatId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/chat-detail/chat-detail?id=${chatId}`
    });
  },

  // 创建新聊天
  createChat() {
    wx.showActionSheet({
      itemList: ['创建群聊', '发起私聊'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.createGroupChat();
        } else if (res.tapIndex === 1) {
          this.createPrivateChat();
        }
      }
    });
  },

  // 创建群聊
  createGroupChat() {
    wx.showModal({
      title: '创建群聊',
      content: '此功能需要选择成员后创建',
      showCancel: false
    });
  },

  // 创建私聊
  createPrivateChat() {
    wx.showModal({
      title: '发起私聊',
      content: '此功能需要选择联系人后创建',
      showCancel: false
    });
  },

  // 格式化时间
  formatTime(time) {
    if (!time) return '';
    const date = typeof time === 'string' ? new Date(time) : time;
    return formatRelativeTime(date);
  },

  // 获取聊天类型图标
  getChatTypeIcon(type) {
    return type === 'GROUP' ? '👥' : '👤';
  }
});