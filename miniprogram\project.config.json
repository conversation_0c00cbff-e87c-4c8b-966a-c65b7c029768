{"description": "LabSync 实验室管理系统微信小程序版", "packOptions": {"ignore": [], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": false, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false, "compileWorklet": false, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wx06897c27f1ac1a10", "projectname": "LabSync", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}}