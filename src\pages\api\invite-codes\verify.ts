import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { sendSuccess, sendError, handleApiError } from '@/lib/apiMiddleware';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
  }

  const { code } = req.body;

  if (!code) {
    return sendError(res, '请提供邀请码', 'INVITE_CODE_REQUIRED', 400);
  }

  try {
    // 查找邀请码
    const inviteCode = await prisma.inviteCode.findUnique({
      where: { code: code.toUpperCase() },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!inviteCode) {
      return sendError(res, '邀请码不存在', 'INVITE_CODE_NOT_FOUND', 404);
    }

    // 检查邀请码是否激活
    if (!inviteCode.isActive) {
      return sendError(res, '邀请码已被禁用', 'INVITE_CODE_DISABLED', 400);
    }

    // 检查是否过期
    if (inviteCode.expiresAt && new Date() > inviteCode.expiresAt) {
      return sendError(res, '邀请码已过期', 'INVITE_CODE_EXPIRED', 400);
    }

    // 检查使用次数
    if (inviteCode.usedCount >= inviteCode.maxUses) {
      return sendError(res, '邀请码使用次数已达上限，无法继续使用', 'INVITE_CODE_EXHAUSTED', 400);
    }

    // 邀请码有效
    return sendSuccess(res, {
      valid: true,
      inviteCode: {
        id: inviteCode.id,
        code: inviteCode.code,
        description: inviteCode.description,
        createdBy: inviteCode.createdBy,
        remainingUses: inviteCode.maxUses - inviteCode.usedCount,
        status: 'valid'
      }
    }, '邀请码验证成功');

  } catch (error) {
    return handleApiError(error, req, res);
  }
}
