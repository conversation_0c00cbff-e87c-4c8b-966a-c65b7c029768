import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface SearchResult {
  id: string;
  title: string;
  type: 'project' | 'task' | 'user' | 'file';
  description?: string;
  url: string;
}

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function GlobalSearch({ isOpen, onClose }: GlobalSearchProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (results[selectedIndex]) {
            handleResultClick(results[selectedIndex]);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex, onClose]);

  useEffect(() => {
    if (query.trim().length < 2) {
      setResults([]);
      return;
    }

    const searchTimeout = setTimeout(async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
        if (response.ok) {
          const data = await response.json();
          // 处理旧API格式的响应
          if (data.results) {
            setResults(data.results);
          } else {
            // 处理旧格式的响应
            const allResults: SearchResult[] = [
              ...(data.projects || []).map((item: any) => ({ ...item, type: 'project' })),
              ...(data.tasks || []).map((item: any) => ({ ...item, type: 'task' })),
              ...(data.files || []).map((item: any) => ({ ...item, type: 'file' })),
              ...(data.users || []).map((item: any) => ({ ...item, type: 'user' })),
            ];
            setResults(allResults);
          }
          setSelectedIndex(0);
        } else {
          console.error('搜索请求失败:', response.status);
          setResults([]);
        }
      } catch (error) {
        console.error('搜索失败:', error);
        setResults([]);
      } finally {
        setLoading(false);
      }
    }, 300);

    return () => clearTimeout(searchTimeout);
  }, [query]);

  const handleResultClick = (result: SearchResult) => {
    router.push(result.url);
    onClose();
    setQuery('');
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'project':
        return '📊';
      case 'task':
        return '✅';
      case 'user':
        return '👤';
      case 'file':
        return '📁';
      default:
        return '🔍';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'project':
        return '项目';
      case 'task':
        return '任务';
      case 'user':
        return '用户';
      case 'file':
        return '文件';
      default:
        return '';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-start justify-center p-4 pt-16">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />

        <div className="relative w-full max-w-2xl">
          <div className="card-modern p-0 overflow-hidden">
            {/* 搜索输入框 */}
            <div className="flex items-center p-4 border-b border-gray-200 dark:border-gray-700">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 mr-3" />
              <input
                ref={inputRef}
                type="text"
                placeholder="搜索项目、任务、用户或文件..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="flex-1 bg-transparent border-none outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500"
              />
              <button
                onClick={onClose}
                className="ml-3 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {/* 搜索结果 */}
            <div className="max-h-96 overflow-y-auto">
              {loading && (
                <div className="flex items-center justify-center p-8">
                  <div className="loading-spinner h-6 w-6" />
                  <span className="ml-2 text-gray-500">搜索中...</span>
                </div>
              )}

              {!loading && query.trim().length >= 2 && results.length === 0 && (
                <div className="p-8 text-center text-gray-500">
                  <MagnifyingGlassIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>未找到相关结果</p>
                  <p className="text-sm mt-1">尝试使用不同的关键词</p>
                </div>
              )}

              {results.map((result, index) => (
                <div
                  key={result.id}
                  className={`p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer transition-colors ${
                    index === selectedIndex
                      ? 'bg-blue-50 dark:bg-blue-900/20'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                  onClick={() => handleResultClick(result)}
                >
                  <div className="flex items-start space-x-3">
                    <span className="text-lg">{getTypeIcon(result.type)}</span>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                          {result.title}
                        </h3>
                        <span className="badge-info text-xs">
                          {getTypeLabel(result.type)}
                        </span>
                      </div>
                      {result.description && (
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                          {result.description}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 快捷键提示 */}
            {results.length > 0 && (
              <div className="p-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <div className="flex items-center space-x-4">
                    <span>↑↓ 导航</span>
                    <span>↵ 选择</span>
                    <span>ESC 关闭</span>
                  </div>
                  <span>{results.length} 个结果</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
