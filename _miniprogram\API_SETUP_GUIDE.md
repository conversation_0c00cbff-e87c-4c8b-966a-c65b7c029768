# 微信小程序 API 接入配置指南

## 1. 配置 API 服务器地址

### 修改 `app.js` 中的 baseUrl

```javascript
globalData: {
  // 生产环境配置
  baseUrl: 'https://your-domain.com', // 替换为你的实际域名
  isDevelopment: false,
  useMockData: false,
  
  // 开发环境配置（用于测试）
  // baseUrl: 'http://localhost:3000',
  // isDevelopment: true,
  // useMockData: true,
}
```

## 2. 微信小程序后台配置

### 2.1 登录微信公众平台
1. 访问 https://mp.weixin.qq.com/
2. 使用小程序账号登录

### 2.2 配置服务器域名
1. 进入 **开发** -> **开发管理** -> **开发设置**
2. 找到 **服务器域名** 部分
3. 配置以下域名：

**request合法域名**：
```
https://your-domain.com
```

**uploadFile合法域名**：
```
https://your-domain.com
```

**downloadFile合法域名**：
```
https://your-domain.com
```

### 2.3 注意事项
- 域名必须是 HTTPS 协议
- 域名必须备案
- 每月只能修改5次
- 配置后需要重新发布小程序

## 3. 服务器端配置

### 3.1 CORS 配置
确保你的 Next.js 服务器支持跨域请求：

```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,POST,PUT,DELETE,OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type,Authorization' },
        ],
      },
    ];
  },
};
```

### 3.2 API 兼容性
确保以下 API 端点存在并正常工作：

- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/dashboard/stats` - 仪表盘数据
- `GET /api/projects` - 项目列表
- `GET /api/tasks` - 任务列表
- `GET /api/chats` - 聊天列表
- `GET /api/files` - 文件列表
- `GET /api/notifications` - 通知列表

## 4. 开发调试

### 4.1 本地开发
在开发阶段，可以使用模拟数据：

```javascript
// app.js
globalData: {
  baseUrl: 'http://localhost:3000',
  isDevelopment: true,
  useMockData: true, // 使用模拟数据
}
```

### 4.2 真机调试
1. 在微信开发者工具中开启 **不校验合法域名**
2. 或者配置内网穿透工具（如 ngrok）

## 5. 部署上线

### 5.1 生产环境配置
```javascript
// app.js
globalData: {
  baseUrl: 'https://your-production-domain.com',
  isDevelopment: false,
  useMockData: false,
}
```

### 5.2 版本发布
1. 在微信开发者工具中点击 **上传**
2. 在微信公众平台提交审核
3. 审核通过后发布

## 6. 错误处理

小程序已内置完善的错误处理机制：
- API 不可用时自动使用模拟数据
- 网络错误时显示友好提示
- 认证失败时自动跳转登录页

## 7. 安全注意事项

1. **Token 管理**：确保 JWT token 安全存储
2. **HTTPS**：生产环境必须使用 HTTPS
3. **数据验证**：服务器端必须验证所有输入数据
4. **权限控制**：确保 API 有适当的权限验证

## 8. 性能优化

1. **缓存策略**：合理使用本地存储缓存数据
2. **图片优化**：使用适当的图片格式和大小
3. **请求优化**：避免重复请求，使用防抖和节流
4. **分页加载**：大量数据使用分页加载

## 9. 监控和日志

建议在生产环境中添加：
- 错误监控（如 Sentry）
- 性能监控
- 用户行为分析
- API 调用日志

## 10. 常见问题

### Q: 小程序无法连接到服务器
A: 检查域名配置、HTTPS 证书、CORS 设置

### Q: 登录失败
A: 检查 API 端点、请求格式、服务器日志

### Q: 图片无法显示
A: 检查图片域名是否在白名单中

### Q: 上传文件失败
A: 检查 uploadFile 域名配置和文件大小限制
