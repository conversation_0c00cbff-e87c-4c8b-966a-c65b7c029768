// 测试消息系统已读/未读逻辑的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMessageSystem() {
  console.log('🧪 开始测试消息系统已读/未读逻辑...\n');

  try {
    // 1. 检查数据库模式
    console.log('1. 检查数据库模式...');
    
    // 检查ChatMessage表是否有isRead字段
    const chatMessageFields = await prisma.$queryRaw`PRAGMA table_info(ChatMessage)`;
    const hasIsReadField = chatMessageFields.some(field => field.name === 'isRead');
    console.log(`   ✅ ChatMessage表${hasIsReadField ? '已有' : '缺少'}isRead字段`);

    // 检查MessageRead表是否存在
    try {
      await prisma.messageRead.findFirst();
      console.log('   ✅ MessageRead表存在');
    } catch (error) {
      console.log('   ❌ MessageRead表不存在');
    }

    // 2. 创建测试数据
    console.log('\n2. 创建测试数据...');
    
    // 查找现有用户或创建测试用户
    let user1 = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (!user1) {
      user1 = await prisma.user.create({
        data: {
          name: '测试用户1',
          email: '<EMAIL>',
          password: 'hashedpassword',
          status: 'APPROVED'
        }
      });
    }

    let user2 = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (!user2) {
      user2 = await prisma.user.create({
        data: {
          name: '测试用户2',
          email: '<EMAIL>',
          password: 'hashedpassword',
          status: 'APPROVED'
        }
      });
    }

    console.log(`   ✅ 测试用户创建完成: ${user1.name} (${user1.id}), ${user2.name} (${user2.id})`);

    // 创建测试聊天
    let testChat = await prisma.chat.findFirst({
      where: {
        type: 'PRIVATE',
        participants: {
          every: {
            id: { in: [user1.id, user2.id] }
          }
        }
      }
    });

    if (!testChat) {
      testChat = await prisma.chat.create({
        data: {
          type: 'PRIVATE',
          participants: {
            connect: [{ id: user1.id }, { id: user2.id }]
          }
        }
      });
    }

    console.log(`   ✅ 测试聊天创建完成: ${testChat.id}`);

    // 3. 测试消息发送和已读状态
    console.log('\n3. 测试消息发送和已读状态...');

    // 用户1发送消息
    const message1 = await prisma.chatMessage.create({
      data: {
        content: '这是一条测试消息',
        type: 'TEXT',
        chatId: testChat.id,
        senderId: user1.id
      }
    });

    console.log(`   ✅ 消息1发送成功: ${message1.id}`);

    // 检查用户2的未读消息数
    const unreadCount1 = await prisma.chatMessage.count({
      where: {
        chatId: testChat.id,
        senderId: { not: user2.id },
        readBy: {
          none: { userId: user2.id }
        }
      }
    });

    console.log(`   📊 用户2的未读消息数: ${unreadCount1}`);

    // 用户2标记消息为已读
    await prisma.messageRead.create({
      data: {
        userId: user2.id,
        messageId: message1.id
      }
    });

    console.log(`   ✅ 用户2标记消息为已读`);

    // 再次检查未读消息数
    const unreadCount2 = await prisma.chatMessage.count({
      where: {
        chatId: testChat.id,
        senderId: { not: user2.id },
        readBy: {
          none: { userId: user2.id }
        }
      }
    });

    console.log(`   📊 标记已读后用户2的未读消息数: ${unreadCount2}`);

    // 4. 测试系统消息的isRead字段
    console.log('\n4. 测试系统消息的isRead字段...');

    if (hasIsReadField) {
      // 创建系统消息
      const systemMessage = await prisma.chatMessage.create({
        data: {
          content: '这是一条系统通知',
          type: 'SYSTEM',
          isSystem: true,
          isRead: false,
          chatId: testChat.id
        }
      });

      console.log(`   ✅ 系统消息创建成功: ${systemMessage.id}, isRead: ${systemMessage.isRead}`);

      // 标记系统消息为已读
      await prisma.chatMessage.update({
        where: { id: systemMessage.id },
        data: { isRead: true }
      });

      const updatedSystemMessage = await prisma.chatMessage.findUnique({
        where: { id: systemMessage.id }
      });

      console.log(`   ✅ 系统消息标记已读: isRead: ${updatedSystemMessage.isRead}`);
    }

    console.log('\n🎉 消息系统测试完成！所有功能正常工作。');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testMessageSystem();
