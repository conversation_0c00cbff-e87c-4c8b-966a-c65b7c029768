import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, isAdmin, getCurrentUserId } from '@/lib/auth';
import { hashPassword } from '@/lib/auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const { id } = req.query;
  const userId = Array.isArray(id) ? id[0] : id;
  const currentUserId = await getCurrentUserId(req, res);
  const isUserAdmin = await isAdmin(req, res);

  // 检查用户是否存在
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    return res.status(404).json({ message: '用户不存在' });
  }

  // 检查权限：管理员、用户本人或同项目成员可以查看基本信息
  let canViewFullInfo = isUserAdmin || currentUserId === userId;
  let canViewBasicInfo = canViewFullInfo;

  if (!canViewBasicInfo) {
    // 检查是否在同一个项目中
    const sharedProjects = await prisma.project.findFirst({
      where: {
        OR: [
          {
            ownerId: currentUserId,
            members: {
              some: { id: userId },
            },
          },
          {
            ownerId: userId,
            members: {
              some: { id: currentUserId },
            },
          },
          {
            members: {
              some: { id: currentUserId },
            },
            AND: {
              members: {
                some: { id: userId },
              },
            },
          },
        ],
      },
    });

    canViewBasicInfo = !!sharedProjects;
  }

  // 处理GET请求 - 获取用户详情
  if (req.method === 'GET') {
    if (!canViewBasicInfo) {
      return res.status(403).json({ message: '权限不足' });
    }

    try {
      const baseSelect = {
        id: true,
        name: true,
        email: true,
        role: true,
        avatar: true,
        age: true,
        bio: canViewFullInfo ? true : false,
        phone: canViewFullInfo ? true : false,
        department: canViewFullInfo ? true : false,
        position: canViewFullInfo ? true : false,
        status: true,
        createdAt: true,
        updatedAt: canViewFullInfo ? true : false,
      };

      const extendedSelect = canViewFullInfo ? {
        ...baseSelect,
        ownedProjects: {
          select: {
            id: true,
            title: true,
            status: true,
          },
        },
        memberProjects: {
          select: {
            id: true,
            title: true,
            status: true,
          },
        },
        assignedTasks: {
          select: {
            id: true,
            title: true,
            status: true,
            project: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        },
        assignedToTasks: {
          select: {
            id: true,
            title: true,
            status: true,
            project: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        },
      } : baseSelect;

      const userDetails = await prisma.user.findUnique({
        where: { id: userId },
        select: extendedSelect,
      });

      return res.status(200).json(userDetails);
    } catch (error) {
      console.error('获取用户详情失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }



  // 处理DELETE请求 - 删除用户（仅限管理员）
  if (req.method === 'DELETE') {
    // 检查是否为管理员
    if (!isUserAdmin) {
      return res.status(403).json({ message: '只有管理员可以删除用户' });
    }

    // 不允许删除自己
    if (currentUserId === userId) {
      return res.status(400).json({ message: '不能删除当前登录的用户' });
    }

    try {
      // 删除用户
      await prisma.user.delete({
        where: { id: userId },
      });

      return res.status(200).json({ message: '用户已成功删除' });
    } catch (error) {
      console.error('删除用户失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理PUT请求 - 更新用户信息
  if (req.method === 'PUT') {
    // 只有用户本人或管理员可以更新用户信息
    if (!canViewFullInfo) {
      return res.status(403).json({ message: '没有权限更新该用户信息' });
    }

    try {
      const { name, email, avatar, age, bio, phone, department, position } = req.body;

      // 验证必填字段
      if (!name || !email) {
        return res.status(400).json({ message: '姓名和邮箱为必填项' });
      }

      // 检查邮箱是否已被其他用户使用
      const existingUser = await prisma.user.findFirst({
        where: {
          email,
          NOT: {
            id: userId,
          },
        },
      });

      if (existingUser) {
        return res.status(400).json({ message: '该邮箱已被其他用户使用' });
      }

      // 更新用户信息
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          name,
          email,
          avatar: avatar || null,
          age: age ? (typeof age === 'string' ? parseInt(age) : age) : null,
          bio: bio || null,
          phone: phone || null,
          department: department || null,
          position: position || null,
        },
        select: {
          id: true,
          name: true,
          email: true,
          avatar: true,
          age: true,
          bio: true,
          phone: true,
          department: true,
          position: true,
          role: true,
          updatedAt: true,
        },
      });

      return res.status(200).json(updatedUser);
    } catch (error) {
      console.error('更新用户信息失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
