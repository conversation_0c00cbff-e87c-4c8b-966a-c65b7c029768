import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface NotificationCounts {
  unreadChats: number;
  unreadTasks: number;
  unreadProjectMessages: number;
  unreadNotifications: number;
  total: number;
}

export function useNotifications() {
  const { data: session } = useSession();
  const [counts, setCounts] = useState<NotificationCounts>({
    unreadChats: 0,
    unreadTasks: 0,
    unreadProjectMessages: 0,
    unreadNotifications: 0,
    total: 0,
  });
  const [loading, setLoading] = useState(false);

  // 获取通知数量
  const fetchNotificationCounts = async () => {
    if (!session?.user?.id) return;

    setLoading(true);
    try {
      const response = await fetch('/api/notifications/counts');
      if (response.ok) {
        const data = await response.json();
        setCounts(data);
      }
    } catch (error) {
      console.error('获取通知数量失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 标记消息为已读
  const markAsRead = async (type: 'chat' | 'task' | 'project', id?: string) => {
    try {
      const response = await fetch('/api/notifications/mark-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type, id }),
      });

      if (response.ok) {
        // 重新获取通知数量
        await fetchNotificationCounts();
      }
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };

  // 初始加载和定期刷新
  useEffect(() => {
    if (session?.user?.id) {
      fetchNotificationCounts();

      // 每30秒刷新一次通知数量
      const interval = setInterval(fetchNotificationCounts, 30000);

      // 监听刷新通知事件
      const handleRefreshNotifications = () => {
        fetchNotificationCounts();
      };

      window.addEventListener('refreshNotifications', handleRefreshNotifications);

      return () => {
        clearInterval(interval);
        window.removeEventListener('refreshNotifications', handleRefreshNotifications);
      };
    }
  }, [session?.user?.id]);

  return {
    counts,
    loading,
    fetchNotificationCounts,
    markAsRead,
  };
}
