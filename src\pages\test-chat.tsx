import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import Layout from '../components/Layout';
import WeChatStyleFileMessage from '../components/WeChatStyleFileMessage';
import EnhancedEmojiPicker, { EnhancedEmojiRenderer, WeChatQuickEmojis } from '../components/EnhancedEmojiPicker';

export default function TestChatPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [testMessage, setTestMessage] = useState('Hello! 😊 这是一个测试消息 :smile: :heart:');

  if (status === 'loading') {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </Layout>
    );
  }

  if (!session) {
    router.push('/login');
    return null;
  }

  const handleEmojiSelect = (emoji: string) => {
    setTestMessage(prev => prev + emoji);
  };

  // 测试文件数据
  const testFiles = [
    {
      fileName: 'project-document.pdf',
      fileSize: 2048576, // 2MB
      fileUrl: '/test-document.pdf',
      fileType: 'application/pdf'
    },
    {
      fileName: 'screenshot.png',
      fileSize: 1024000, // 1MB
      fileUrl: '/uploads/test-image.png',
      fileType: 'image/png'
    },
    {
      fileName: 'presentation.pptx',
      fileSize: 5242880, // 5MB
      fileUrl: '/uploads/presentation.pptx',
      fileType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    },
    {
      fileName: 'code.js',
      fileSize: 15360, // 15KB
      fileUrl: '/uploads/code.js',
      fileType: 'application/javascript'
    },
    {
      fileName: 'archive.zip',
      fileSize: 10485760, // 10MB
      fileUrl: '/uploads/archive.zip',
      fileType: 'application/zip'
    }
  ];

  return (
    <Layout>
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* 页面标题 */}
          <div className="bg-gradient-to-r from-primary-500 to-primary-600 text-white p-6">
            <h1 className="text-2xl font-bold mb-2">聊天功能测试页面</h1>
            <p className="text-primary-100">测试微信风格的文件显示和增强表情系统</p>
          </div>

          <div className="p-6 space-y-8">
            {/* 表情系统测试 */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">表情系统测试</h2>
              
              {/* 表情选择器 */}
              <div className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">表情选择器:</span>
                <EnhancedEmojiPicker
                  onEmojiSelect={handleEmojiSelect}
                  className="flex-shrink-0"
                />
                <span className="text-sm text-gray-500 dark:text-gray-400">点击选择表情</span>
              </div>

              {/* 常用表情快捷栏 */}
              <div className="space-y-2">
                <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">常用表情快捷栏</h3>
                <WeChatQuickEmojis onEmojiSelect={handleEmojiSelect} />
              </div>

              {/* 表情渲染测试 */}
              <div className="space-y-2">
                <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">表情渲染测试</h3>
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="mb-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      输入文本 (支持表情代码如 :smile: :heart: 等):
                    </label>
                    <input
                      type="text"
                      value={testMessage}
                      onChange={(e) => setTestMessage(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                      placeholder="输入包含表情的文本..."
                    />
                  </div>
                  <div className="p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md">
                    <span className="text-sm text-gray-500 dark:text-gray-400">渲染结果:</span>
                    <div className="mt-1">
                      <EnhancedEmojiRenderer 
                        text={testMessage}
                        className="text-base text-gray-900 dark:text-gray-100"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 文件显示测试 */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">微信风格文件显示测试</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 自己发送的消息 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">自己发送的文件</h3>
                  <div className="space-y-3">
                    {testFiles.map((file, index) => (
                      <div key={index} className="flex justify-end">
                        <WeChatStyleFileMessage
                          fileName={file.fileName}
                          fileSize={file.fileSize}
                          fileUrl={file.fileUrl}
                          fileType={file.fileType}
                          isOwnMessage={true}
                          onPreview={() => alert(`预览文件: ${file.fileName}`)}
                          onDownload={() => alert(`下载文件: ${file.fileName}`)}
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* 他人发送的消息 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">他人发送的文件</h3>
                  <div className="space-y-3">
                    {testFiles.map((file, index) => (
                      <div key={index} className="flex justify-start">
                        <WeChatStyleFileMessage
                          fileName={file.fileName}
                          fileSize={file.fileSize}
                          fileUrl={file.fileUrl}
                          fileType={file.fileType}
                          isOwnMessage={false}
                          onPreview={() => alert(`预览文件: ${file.fileName}`)}
                          onDownload={() => alert(`下载文件: ${file.fileName}`)}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* 功能说明 */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">功能特点</h3>
              <ul className="space-y-2 text-blue-800 dark:text-blue-200">
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span><strong>微信风格文件显示:</strong> 清晰的文件类型标识，不同类型文件使用不同颜色和图标</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span><strong>图片直接预览:</strong> 图片文件直接在聊天中显示缩略图，支持悬停操作</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span><strong>增强表情系统:</strong> 支持表情选择器、常用表情快捷栏和表情代码渲染</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span><strong>开源组件集成:</strong> 使用 emoji-picker-react 和 react-file-icon 等安全的开源组件</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span><strong>响应式设计:</strong> 适配不同屏幕尺寸，支持暗色主题</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
