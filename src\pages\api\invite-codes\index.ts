import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { requireAuth, requireAdmin, sendSuccess, sendError, handleApiError } from '@/lib/apiMiddleware';
import { getCurrentUserId } from '@/lib/auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === 'GET') {
    // 获取邀请码列表（需要管理员权限）
    try {
      await requireAdmin(req, res, async () => {});
    } catch (error) {
      return;
    }

    try {
      const inviteCodes = await prisma.inviteCode.findMany({
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          usedBy: {
            select: {
              id: true,
              name: true,
              email: true,
              createdAt: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return sendSuccess(res, inviteCodes, '邀请码列表获取成功');
    } catch (error) {
      return handleApiError(error, req, res);
    }
  }

  if (req.method === 'POST') {
    // 创建邀请码（需要管理员权限）
    try {
      await requireAdmin(req, res, async () => {});
    } catch (error) {
      return;
    }

    const { description, maxUses = 1, expiresAt } = req.body;
    const userId = await getCurrentUserId(req, res);

    if (!userId) {
      return sendError(res, '用户未认证', 'USER_NOT_AUTHENTICATED', 401);
    }

    try {
      // 生成唯一的邀请码
      const code = generateInviteCode();

      const inviteCode = await prisma.inviteCode.create({
        data: {
          code,
          description,
          maxUses: parseInt(maxUses),
          expiresAt: expiresAt ? new Date(expiresAt) : null,
          createdById: userId
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      return sendSuccess(res, inviteCode, '邀请码创建成功', 201);
    } catch (error) {
      return handleApiError(error, req, res);
    }
  }

  if (req.method === 'DELETE') {
    // 删除邀请码（需要管理员权限）
    try {
      await requireAdmin(req, res, async () => {});
    } catch (error) {
      return;
    }

    const { id } = req.body;

    if (!id) {
      return sendError(res, '请提供邀请码ID', 'INVITE_CODE_ID_REQUIRED', 400);
    }

    try {
      // 检查邀请码是否存在
      const existingCode = await prisma.inviteCode.findUnique({
        where: { id },
        include: {
          usedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      if (!existingCode) {
        return sendError(res, '邀请码不存在', 'INVITE_CODE_NOT_FOUND', 404);
      }

      // 如果邀请码已被使用，不允许删除，而是禁用
      if (existingCode.usedCount > 0) {
        const updatedCode = await prisma.inviteCode.update({
          where: { id },
          data: { isActive: false },
          include: {
            createdBy: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        });

        return sendSuccess(res, updatedCode, '邀请码已禁用（因为已被使用，无法删除）');
      }

      // 如果未被使用，可以直接删除
      await prisma.inviteCode.delete({
        where: { id }
      });

      return sendSuccess(res, { id }, '邀请码删除成功');
    } catch (error) {
      return handleApiError(error, req, res);
    }
  }

  if (req.method === 'PATCH') {
    // 更新邀请码状态（需要管理员权限）
    try {
      await requireAdmin(req, res, async () => {});
    } catch (error) {
      return;
    }

    const { id, isActive } = req.body;

    if (!id) {
      return sendError(res, '请提供邀请码ID', 'INVITE_CODE_ID_REQUIRED', 400);
    }

    if (typeof isActive !== 'boolean') {
      return sendError(res, '请提供有效的状态值', 'INVALID_STATUS', 400);
    }

    try {
      // 检查邀请码是否存在
      const existingCode = await prisma.inviteCode.findUnique({
        where: { id }
      });

      if (!existingCode) {
        return sendError(res, '邀请码不存在', 'INVITE_CODE_NOT_FOUND', 404);
      }

      // 更新邀请码状态
      const updatedCode = await prisma.inviteCode.update({
        where: { id },
        data: { isActive },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      const statusText = isActive ? '启用' : '禁用';
      return sendSuccess(res, updatedCode, `邀请码已${statusText}`);
    } catch (error) {
      return handleApiError(error, req, res);
    }
  }

  return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
}

// 生成邀请码
function generateInviteCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

  // 生成格式：LABSYNC-YYYY-XXX (与现有预置邀请码格式一致)
  const year = new Date().getFullYear();
  const randomSuffix = Array.from({ length: 3 }, () =>
    chars.charAt(Math.floor(Math.random() * chars.length))
  ).join('');

  return `LABSYNC-${year}-${randomSuffix}`;
}
