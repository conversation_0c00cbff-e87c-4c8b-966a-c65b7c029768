import type { NextApiRequest, NextApiResponse } from 'next';
import { SystemConfigManager, SystemConfig } from '@/lib/systemConfig';
import { withPermissions } from '@/lib/apiMiddleware';
import { Permission } from '@/lib/permissions';
import { sendSuccess, sendError, handleApiError } from '@/lib/apiMiddleware';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        return await handleGetConfig(req, res);
      case 'PUT':
        return await handleUpdateConfig(req, res);
      case 'POST':
        return await handleResetConfig(req, res);
      default:
        return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
    }
  } catch (error) {
    console.error('系统配置API错误:', error);
    return handleApiError(error, req, res);
  }
}

// 获取系统配置
async function handleGetConfig(req: NextApiRequest, res: NextApiResponse) {
  const { summary } = req.query;

  if (summary === 'true') {
    // 返回配置摘要
    const configSummary = await SystemConfigManager.getConfigSummary();
    return sendSuccess(res, configSummary, '配置摘要获取成功');
  } else {
    // 返回完整配置
    const config = await SystemConfigManager.getConfig();
    return sendSuccess(res, config, '系统配置获取成功');
  }
}

// 更新系统配置
async function handleUpdateConfig(req: NextApiRequest, res: NextApiResponse) {
  const updates = req.body;

  if (!updates || typeof updates !== 'object') {
    return sendError(res, '无效的配置数据', 'INVALID_CONFIG_DATA', 400);
  }

  try {
    // 验证更新数据
    validateConfigUpdates(updates);

    // 更新配置
    const newConfig = await SystemConfigManager.updateConfig(updates);

    return sendSuccess(res, newConfig, '系统配置更新成功');
  } catch (error) {
    if (error instanceof Error) {
      return sendError(res, error.message, 'CONFIG_VALIDATION_ERROR', 400);
    }
    throw error;
  }
}

// 重置系统配置
async function handleResetConfig(req: NextApiRequest, res: NextApiResponse) {
  const { action } = req.body;

  if (action !== 'reset') {
    return sendError(res, '无效的操作', 'INVALID_ACTION', 400);
  }

  try {
    const defaultConfig = await SystemConfigManager.resetToDefaults();
    return sendSuccess(res, defaultConfig, '系统配置已重置为默认值');
  } catch (error) {
    throw error;
  }
}

// 验证配置更新数据
function validateConfigUpdates(updates: Partial<SystemConfig>) {
  // 验证站点名称
  if (updates.siteName !== undefined) {
    if (typeof updates.siteName !== 'string' || updates.siteName.trim().length === 0) {
      throw new Error('站点名称不能为空');
    }
    if (updates.siteName.length > 100) {
      throw new Error('站点名称不能超过100个字符');
    }
  }

  // 验证管理员邮箱
  if (updates.adminEmail !== undefined) {
    if (typeof updates.adminEmail !== 'string' || !isValidEmail(updates.adminEmail)) {
      throw new Error('管理员邮箱格式无效');
    }
  }

  // 验证文件大小限制
  if (updates.maxFileSize !== undefined) {
    if (typeof updates.maxFileSize !== 'number' || updates.maxFileSize <= 0) {
      throw new Error('最大文件大小必须是正数');
    }
    if (updates.maxFileSize > 100 * 1024 * 1024) { // 100MB
      throw new Error('最大文件大小不能超过100MB');
    }
  }

  // 验证会话超时
  if (updates.sessionTimeout !== undefined) {
    if (typeof updates.sessionTimeout !== 'number' || updates.sessionTimeout <= 0) {
      throw new Error('会话超时时间必须是正数');
    }
    if (updates.sessionTimeout > 24 * 60) { // 24小时
      throw new Error('会话超时时间不能超过24小时');
    }
  }

  // 验证最大登录尝试次数
  if (updates.maxLoginAttempts !== undefined) {
    if (typeof updates.maxLoginAttempts !== 'number' || updates.maxLoginAttempts <= 0) {
      throw new Error('最大登录尝试次数必须是正数');
    }
    if (updates.maxLoginAttempts > 20) {
      throw new Error('最大登录尝试次数不能超过20次');
    }
  }

  // 验证锁定持续时间
  if (updates.lockoutDuration !== undefined) {
    if (typeof updates.lockoutDuration !== 'number' || updates.lockoutDuration <= 0) {
      throw new Error('锁定持续时间必须是正数');
    }
    if (updates.lockoutDuration > 24 * 60) { // 24小时
      throw new Error('锁定持续时间不能超过24小时');
    }
  }

  // 验证用户限制
  if (updates.maxUsersPerProject !== undefined) {
    if (typeof updates.maxUsersPerProject !== 'number' || updates.maxUsersPerProject <= 0) {
      throw new Error('每个项目最大用户数必须是正数');
    }
    if (updates.maxUsersPerProject > 1000) {
      throw new Error('每个项目最大用户数不能超过1000');
    }
  }

  // 验证项目限制
  if (updates.maxProjectsPerUser !== undefined) {
    if (typeof updates.maxProjectsPerUser !== 'number' || updates.maxProjectsPerUser <= 0) {
      throw new Error('每个用户最大项目数必须是正数');
    }
    if (updates.maxProjectsPerUser > 100) {
      throw new Error('每个用户最大项目数不能超过100');
    }
  }

  // 验证任务限制
  if (updates.maxTasksPerProject !== undefined) {
    if (typeof updates.maxTasksPerProject !== 'number' || updates.maxTasksPerProject <= 0) {
      throw new Error('每个项目最大任务数必须是正数');
    }
    if (updates.maxTasksPerProject > 10000) {
      throw new Error('每个项目最大任务数不能超过10000');
    }
  }

  // 验证文件限制
  if (updates.maxFilesPerProject !== undefined) {
    if (typeof updates.maxFilesPerProject !== 'number' || updates.maxFilesPerProject <= 0) {
      throw new Error('每个项目最大文件数必须是正数');
    }
    if (updates.maxFilesPerProject > 10000) {
      throw new Error('每个项目最大文件数不能超过10000');
    }
  }

  // 验证允许的文件类型
  if (updates.allowedFileTypes !== undefined) {
    if (!Array.isArray(updates.allowedFileTypes) || updates.allowedFileTypes.length === 0) {
      throw new Error('允许的文件类型不能为空');
    }
    for (const fileType of updates.allowedFileTypes) {
      if (typeof fileType !== 'string' || fileType.trim().length === 0) {
        throw new Error('文件类型格式无效');
      }
    }
  }

  // 验证保留天数
  if (updates.notificationRetentionDays !== undefined) {
    if (typeof updates.notificationRetentionDays !== 'number' || updates.notificationRetentionDays <= 0) {
      throw new Error('通知保留天数必须是正数');
    }
    if (updates.notificationRetentionDays > 365) {
      throw new Error('通知保留天数不能超过365天');
    }
  }

  if (updates.analyticsRetentionDays !== undefined) {
    if (typeof updates.analyticsRetentionDays !== 'number' || updates.analyticsRetentionDays <= 0) {
      throw new Error('分析数据保留天数必须是正数');
    }
    if (updates.analyticsRetentionDays > 365) {
      throw new Error('分析数据保留天数不能超过365天');
    }
  }

  // 验证API速率限制
  if (updates.apiRateLimit !== undefined) {
    if (typeof updates.apiRateLimit !== 'number' || updates.apiRateLimit <= 0) {
      throw new Error('API速率限制必须是正数');
    }
    if (updates.apiRateLimit > 10000) {
      throw new Error('API速率限制不能超过每分钟10000次');
    }
  }

  // 验证维护消息
  if (updates.maintenanceMessage !== undefined) {
    if (typeof updates.maintenanceMessage !== 'string') {
      throw new Error('维护消息必须是字符串');
    }
    if (updates.maintenanceMessage.length > 500) {
      throw new Error('维护消息不能超过500个字符');
    }
  }

  // 验证布尔值
  const booleanFields = [
    'allowUserRegistration',
    'requireEmailVerification',
    'enableEmailNotifications',
    'enablePushNotifications',
    'requireStrongPasswords',
    'enableAutoBackup',
    'maintenanceMode',
    'enableAnalytics',
    'enableApiLogging'
  ];

  for (const field of booleanFields) {
    if (updates[field as keyof SystemConfig] !== undefined && typeof updates[field as keyof SystemConfig] !== 'boolean') {
      throw new Error(`${field} 必须是布尔值`);
    }
  }
}

// 验证邮箱格式
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 导出带权限检查的处理器（只有管理员可以访问）
export default withPermissions(Permission.SYSTEM_ADMIN, handler);
