// 注册页面
const { userApi, teamApi } = require('../../utils/api.js');
const { validateEmail, validatePassword, validatePhone } = require('../../utils/util.js');
const app = getApp();

Page({
  data: {
    currentStep: 1,
    totalSteps: 3,

    // 第一步：基本信息
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    showPassword: false,
    showConfirmPassword: false,

    // 第二步：详细信息
    phone: '',
    department: '',
    position: '',
    bio: '',

    // 第三步：邀请码
    inviteCode: '',

    loading: false,
    departments: [
      '研发部', '产品部', '设计部', '运营部', '市场部', '人事部', '财务部', '其他'
    ]
  },

  onLoad(options) {
    // 检查是否已登录
    if (app.isLoggedIn()) {
      wx.switchTab({
        url: '/pages/index/index'
      });
      return;
    }

    // 从URL参数获取邀请码
    if (options.inviteCode) {
      this.setData({
        inviteCode: options.inviteCode
      });
    }
  },

  // 输入处理
  onNameInput(e) {
    this.setData({ name: e.detail.value });
  },

  onEmailInput(e) {
    this.setData({ email: e.detail.value });
  },

  onPasswordInput(e) {
    this.setData({ password: e.detail.value });
  },

  onConfirmPasswordInput(e) {
    this.setData({ confirmPassword: e.detail.value });
  },

  onPhoneInput(e) {
    this.setData({ phone: e.detail.value });
  },

  onDepartmentChange(e) {
    const index = e.detail.value;
    this.setData({
      department: this.data.departments[index]
    });
  },

  onPositionInput(e) {
    this.setData({ position: e.detail.value });
  },

  onBioInput(e) {
    this.setData({ bio: e.detail.value });
  },

  onInviteCodeInput(e) {
    this.setData({ inviteCode: e.detail.value });
  },

  // 切换密码显示
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  toggleConfirmPasswordVisibility() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    });
  },

  // 验证第一步
  validateStep1() {
    const { name, email, password, confirmPassword } = this.data;

    if (!name.trim()) {
      wx.showToast({ title: '请输入姓名', icon: 'error' });
      return false;
    }

    if (!email.trim()) {
      wx.showToast({ title: '请输入邮箱', icon: 'error' });
      return false;
    }

    if (!validateEmail(email)) {
      wx.showToast({ title: '邮箱格式不正确', icon: 'error' });
      return false;
    }

    if (!password.trim()) {
      wx.showToast({ title: '请输入密码', icon: 'error' });
      return false;
    }

    const passwordValidation = validatePassword(password);
    if (!passwordValidation.valid) {
      wx.showToast({ title: passwordValidation.message, icon: 'error' });
      return false;
    }

    if (password !== confirmPassword) {
      wx.showToast({ title: '两次密码输入不一致', icon: 'error' });
      return false;
    }

    return true;
  },

  // 验证第二步
  validateStep2() {
    const { phone, department } = this.data;

    if (phone && !validatePhone(phone)) {
      wx.showToast({ title: '手机号格式不正确', icon: 'error' });
      return false;
    }

    if (!department) {
      wx.showToast({ title: '请选择部门', icon: 'error' });
      return false;
    }

    return true;
  },

  // 验证第三步
  validateStep3() {
    const { inviteCode } = this.data;

    if (!inviteCode.trim()) {
      wx.showToast({ title: '请输入邀请码', icon: 'error' });
      return false;
    }

    return true;
  },

  // 下一步
  nextStep() {
    const { currentStep } = this.data;

    if (currentStep === 1 && !this.validateStep1()) {
      return;
    }

    if (currentStep === 2 && !this.validateStep2()) {
      return;
    }

    if (currentStep < this.data.totalSteps) {
      this.setData({
        currentStep: currentStep + 1
      });
    }
  },

  // 上一步
  prevStep() {
    const { currentStep } = this.data;
    if (currentStep > 1) {
      this.setData({
        currentStep: currentStep - 1
      });
    }
  },

  // 提交注册
  async handleRegister() {
    if (!this.validateStep3()) {
      return;
    }

    const { name, email, password, phone, department, position, bio, inviteCode } = this.data;

    this.setData({ loading: true });

    try {
      // 先验证邀请码
      await teamApi.verifyInviteCode(inviteCode);

      // 注册用户
      const response = await userApi.register({
        name: name.trim(),
        email: email.trim(),
        password,
        phone: phone.trim(),
        department,
        position: position.trim(),
        bio: bio.trim(),
        inviteCode: inviteCode.trim()
      });

      wx.showToast({
        title: '注册成功',
        icon: 'success'
      });

      // 跳转到登录页面
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/login/login'
        });
      }, 1500);

    } catch (error) {
      console.error('注册失败:', error);
      wx.showToast({
        title: error.message || '注册失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 返回登录页面
  goToLogin() {
    wx.navigateBack();
  }
});
