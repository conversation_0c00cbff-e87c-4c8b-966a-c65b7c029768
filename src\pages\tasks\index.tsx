import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import TaskCard from '@/components/TaskCard';

export default function Tasks() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'todo', 'in_progress', 'review', 'completed'
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState('all');

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取任务列表和项目列表
  useEffect(() => {
    if (status === 'authenticated') {
      fetchTasks();
      fetchProjects();
    }
  }, [status]);

  const fetchTasks = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/tasks');
      
      if (!response.ok) {
        throw new Error('获取任务列表失败');
      }
      
      const data = await response.json();
      setTasks(data);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      setError('获取任务列表失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects');
      
      if (!response.ok) {
        throw new Error('获取项目列表失败');
      }
      
      const data = await response.json();
      setProjects(data);
    } catch (error) {
      console.error('获取项目列表失败:', error);
    }
  };

  // 过滤任务
  const filteredTasks = tasks.filter(task => {
    // 按状态过滤
    if (filter !== 'all' && task.status !== filter.toUpperCase()) {
      return false;
    }
    
    // 按项目过滤
    if (selectedProject !== 'all' && task.project?.id !== selectedProject) {
      return false;
    }
    
    return true;
  });

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            任务管理
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            查看和管理您的所有任务
          </p>
        </div>
      </div>

      {/* 过滤器 */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 rounded-md text-sm ${
              filter === 'all'
                ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            全部
          </button>
          <button
            onClick={() => setFilter('todo')}
            className={`px-3 py-1 rounded-md text-sm ${
              filter === 'todo'
                ? 'bg-gray-300 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            待处理
          </button>
          <button
            onClick={() => setFilter('in_progress')}
            className={`px-3 py-1 rounded-md text-sm ${
              filter === 'in_progress'
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            进行中
          </button>
          <button
            onClick={() => setFilter('review')}
            className={`px-3 py-1 rounded-md text-sm ${
              filter === 'review'
                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            审核中
          </button>
          <button
            onClick={() => setFilter('completed')}
            className={`px-3 py-1 rounded-md text-sm ${
              filter === 'completed'
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            已完成
          </button>
        </div>

        {/* 项目选择器 */}
        {projects.length > 0 && (
          <div className="flex items-center">
            <label htmlFor="project-filter" className="mr-2 text-sm text-gray-700 dark:text-gray-300">
              项目:
            </label>
            <select
              id="project-filter"
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="form-input py-1 px-2 text-sm rounded-md w-auto"
            >
              <option value="all">全部项目</option>
              {projects.map(project => (
                <option key={project.id} value={project.id}>
                  {project.title}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* 加载中 */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      ) : (
        <>
          {/* 任务列表 */}
          {filteredTasks.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTasks.map(task => (
                <TaskCard key={task.id} task={task} />
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">没有找到任务</h3>
              <p className="mt-2 text-gray-500 dark:text-gray-400">
                {filter === 'all' && selectedProject === 'all'
                  ? '您还没有任何任务。'
                  : '没有符合筛选条件的任务。'}
              </p>
              <div className="mt-6">
                <button
                  onClick={() => {
                    setFilter('all');
                    setSelectedProject('all');
                  }}
                  className="btn btn-primary"
                >
                  查看所有任务
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
