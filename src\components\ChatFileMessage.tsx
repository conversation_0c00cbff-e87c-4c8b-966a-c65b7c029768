import React, { useState } from 'react';
import { 
  DocumentIcon, 
  PhotoIcon, 
  VideoCameraIcon,
  MusicalNoteIcon,
  ArchiveBoxIcon,
  DocumentTextIcon,
  PresentationChartBarIcon,
  CodeBracketIcon,
  ArrowDownTrayIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface ChatFileMessageProps {
  fileName: string;
  fileSize: number;
  fileUrl: string;
  fileType: string;
  isOwnMessage: boolean;
  onPreview?: () => void;
  onDownload?: () => void;
}

export default function ChatFileMessage({
  fileName,
  fileSize,
  fileUrl,
  fileType,
  isOwnMessage,
  onPreview,
  onDownload
}: ChatFileMessageProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // 获取文件扩展名
  const getFileExtension = (name: string) => {
    return name.split('.').pop()?.toLowerCase() || '';
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 获取文件图标和颜色
  const getFileIconAndColor = (name: string, type: string) => {
    const ext = getFileExtension(name);
    
    // 图片文件
    if (type.startsWith('image/')) {
      return {
        icon: PhotoIcon,
        color: 'text-green-500',
        bgColor: 'bg-green-100 dark:bg-green-900/30',
        borderColor: 'border-green-200 dark:border-green-800'
      };
    }
    
    // 视频文件
    if (type.startsWith('video/')) {
      return {
        icon: VideoCameraIcon,
        color: 'text-red-500',
        bgColor: 'bg-red-100 dark:bg-red-900/30',
        borderColor: 'border-red-200 dark:border-red-800'
      };
    }
    
    // 音频文件
    if (type.startsWith('audio/')) {
      return {
        icon: MusicalNoteIcon,
        color: 'text-purple-500',
        bgColor: 'bg-purple-100 dark:bg-purple-900/30',
        borderColor: 'border-purple-200 dark:border-purple-800'
      };
    }
    
    // PDF文件
    if (type === 'application/pdf') {
      return {
        icon: DocumentTextIcon,
        color: 'text-red-600',
        bgColor: 'bg-red-100 dark:bg-red-900/30',
        borderColor: 'border-red-200 dark:border-red-800'
      };
    }
    
    // Office文档
    if (['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(ext)) {
      return {
        icon: PresentationChartBarIcon,
        color: 'text-blue-600',
        bgColor: 'bg-blue-100 dark:bg-blue-900/30',
        borderColor: 'border-blue-200 dark:border-blue-800'
      };
    }
    
    // 代码文件
    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'html', 'css', 'json'].includes(ext)) {
      return {
        icon: CodeBracketIcon,
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-100 dark:bg-indigo-900/30',
        borderColor: 'border-indigo-200 dark:border-indigo-800'
      };
    }
    
    // 压缩文件
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
      return {
        icon: ArchiveBoxIcon,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
        borderColor: 'border-yellow-200 dark:border-yellow-800'
      };
    }
    
    // 默认文件
    return {
      icon: DocumentIcon,
      color: 'text-gray-500',
      bgColor: 'bg-gray-100 dark:bg-gray-700',
      borderColor: 'border-gray-200 dark:border-gray-600'
    };
  };

  const isImage = fileType.startsWith('image/');
  const isPreviewable = isImage || fileType === 'application/pdf' || fileType.startsWith('text/');
  
  const { icon: FileIcon, color, bgColor, borderColor } = getFileIconAndColor(fileName, fileType);

  // 如果是图片，显示图片预览
  if (isImage) {
    return (
      <div className={`max-w-xs ${isOwnMessage ? 'ml-auto' : 'mr-auto'}`}>
        <div className="relative group cursor-pointer" onClick={onPreview}>
          {!imageLoaded && !imageError && (
            <div className={`w-48 h-32 rounded-lg flex items-center justify-center ${bgColor} ${borderColor} border`}>
              <div className="animate-pulse flex items-center space-x-2">
                <PhotoIcon className="w-6 h-6 text-gray-400" />
                <span className="text-sm text-gray-500">加载中...</span>
              </div>
            </div>
          )}
          
          {imageError ? (
            <div className={`w-48 h-32 rounded-lg flex flex-col items-center justify-center ${bgColor} ${borderColor} border`}>
              <PhotoIcon className="w-8 h-8 text-gray-400 mb-2" />
              <span className="text-xs text-gray-500 text-center px-2">
                图片加载失败
              </span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDownload?.();
                }}
                className="mt-2 text-xs text-blue-500 hover:text-blue-600"
              >
                点击下载
              </button>
            </div>
          ) : (
            <div className="relative">
              <img
                src={fileUrl}
                alt={fileName}
                className={`max-w-48 max-h-48 rounded-lg object-cover transition-opacity duration-200 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
              />
              
              {/* 悬停遮罩 */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onPreview?.();
                    }}
                    className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                  >
                    <EyeIcon className="w-4 h-4 text-gray-700" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDownload?.();
                    }}
                    className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                  >
                    <ArrowDownTrayIcon className="w-4 h-4 text-gray-700" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* 图片信息 */}
        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {fileName} • {formatFileSize(fileSize)}
        </div>
      </div>
    );
  }

  // 其他文件类型显示文件卡片
  return (
    <div className={`max-w-xs ${isOwnMessage ? 'ml-auto' : 'mr-auto'}`}>
      <div className={`
        flex items-center p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md
        ${bgColor} ${borderColor}
        ${isOwnMessage 
          ? 'bg-primary-500 text-white border-primary-600' 
          : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600'
        }
      `}>
        {/* 文件图标 */}
        <div className={`
          flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center mr-3
          ${isOwnMessage ? 'bg-white bg-opacity-20' : bgColor}
        `}>
          <FileIcon className={`w-6 h-6 ${isOwnMessage ? 'text-white' : color}`} />
        </div>
        
        {/* 文件信息 */}
        <div className="flex-1 min-w-0">
          <div className={`
            text-sm font-medium truncate
            ${isOwnMessage ? 'text-white' : 'text-gray-900 dark:text-gray-100'}
          `}>
            {fileName}
          </div>
          <div className={`
            text-xs mt-1 flex items-center space-x-2
            ${isOwnMessage ? 'text-white text-opacity-80' : 'text-gray-500 dark:text-gray-400'}
          `}>
            <span>{formatFileSize(fileSize)}</span>
            <span>•</span>
            <span className="uppercase">{getFileExtension(fileName)}</span>
          </div>
        </div>
        
        {/* 操作按钮 */}
        <div className="flex-shrink-0 flex items-center space-x-1 ml-2">
          {isPreviewable && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onPreview?.();
              }}
              className={`
                p-1.5 rounded-full transition-all duration-200 hover:scale-110
                ${isOwnMessage 
                  ? 'text-white hover:bg-white hover:bg-opacity-20' 
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                }
              `}
              title="预览"
            >
              <EyeIcon className="w-4 h-4" />
            </button>
          )}
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDownload?.();
            }}
            className={`
              p-1.5 rounded-full transition-all duration-200 hover:scale-110
              ${isOwnMessage 
                ? 'text-white hover:bg-white hover:bg-opacity-20' 
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
              }
            `}
            title="下载"
          >
            <ArrowDownTrayIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
