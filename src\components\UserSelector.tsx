import React, { useState, useEffect } from 'react';
import Avatar from './Avatar';
import { User } from '@prisma/client';

interface UserSelectorProps {
  selectedUsers: string[];
  onSelectionChange: (userIds: string[]) => void;
  placeholder?: string;
  multiple?: boolean;
  className?: string;
}

interface UserWithProfile extends Omit<User, 'avatar' | 'age' | 'department' | 'position'> {
  avatar?: string | null;
  age?: number | null;
  department?: string | null;
  position?: string | null;
}

const UserSelector: React.FC<UserSelectorProps> = ({
  selectedUsers,
  onSelectionChange,
  placeholder = "选择用户",
  multiple = true,
  className = ""
}) => {
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.department && user.department.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const selectedUserObjects = users.filter(user => selectedUsers.includes(user.id));

  const handleUserToggle = (userId: string) => {
    if (multiple) {
      const newSelection = selectedUsers.includes(userId)
        ? selectedUsers.filter(id => id !== userId)
        : [...selectedUsers, userId];
      onSelectionChange(newSelection);
    } else {
      onSelectionChange([userId]);
      setIsOpen(false);
    }
  };

  const removeUser = (userId: string) => {
    onSelectionChange(selectedUsers.filter(id => id !== userId));
  };



  return (
    <div className={`relative ${className}`}>
      {/* 选中的用户显示 */}
      {selectedUserObjects.length > 0 && (
        <div className="mb-2 flex flex-wrap gap-2">
          {selectedUserObjects.map(user => (
            <div
              key={user.id}
              className="flex items-center bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 px-3 py-1 rounded-full text-sm"
            >
              <Avatar
                user={{
                  id: user.id,
                  name: user.name,
                  avatar: user.avatar
                }}
                size="xs"
                className="mr-2"
              />
              <span>{user.name}</span>
              <button
                type="button"
                onClick={() => removeUser(user.id)}
                className="ml-2 text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-200"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {/* 选择器输入框 */}
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-left cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          <span className="block truncate text-gray-700 dark:text-gray-300">
            {selectedUsers.length > 0
              ? `已选择 ${selectedUsers.length} 个用户`
              : placeholder
            }
          </span>
          <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3z" clipRule="evenodd" />
            </svg>
          </span>
        </button>

        {/* 下拉选项 */}
        {isOpen && (
          <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-700 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
            {/* 搜索框 */}
            <div className="sticky top-0 bg-white dark:bg-gray-700 px-3 py-2 border-b border-gray-200 dark:border-gray-600">
              <input
                type="text"
                placeholder="搜索用户..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
            </div>

            {/* 用户列表 */}
            {loading ? (
              <div className="px-3 py-2 text-gray-500 dark:text-gray-400">加载中...</div>
            ) : filteredUsers.length === 0 ? (
              <div className="px-3 py-2 text-gray-500 dark:text-gray-400">没有找到用户</div>
            ) : (
              filteredUsers.map(user => (
                <div
                  key={user.id}
                  onClick={() => handleUserToggle(user.id)}
                  className={`cursor-pointer select-none relative py-2 px-3 hover:bg-gray-100 dark:hover:bg-gray-600 ${
                    selectedUsers.includes(user.id)
                      ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-900 dark:text-primary-100'
                      : 'text-gray-900 dark:text-gray-100'
                  }`}
                >
                  <div className="flex items-center">
                    <Avatar
                      user={{
                        id: user.id,
                        name: user.name,
                        avatar: user.avatar
                      }}
                      size="sm"
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{user.name}</span>
                        {selectedUsers.includes(user.id) && (
                          <svg className="h-5 w-5 text-primary-600 dark:text-primary-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {user.email}
                        {user.department && ` • ${user.department}`}
                        {user.position && ` • ${user.position}`}
                        {user.age && ` • ${user.age}岁`}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserSelector;
