import Link from 'next/link';

interface NewUserWelcomeProps {
  userName: string;
  userRole: string;
}

export default function NewUserWelcome({ userName, userRole }: NewUserWelcomeProps) {
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'ADMIN': return '管理员';
      case 'LEADER': return '项目负责人';
      case 'MEMBER': return '团队成员';
      default: return '访客';
    }
  };

  const getWelcomeMessage = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return '作为管理员，您可以管理所有项目、用户和系统设置。';
      case 'LEADER':
        return '作为项目负责人，您可以创建和管理项目，分配任务给团队成员。';
      case 'MEMBER':
        return '作为团队成员，您可以参与项目协作，完成分配的任务。';
      default:
        return '欢迎使用LabSync团队协作平台！';
    }
  };

  const getQuickActions = (role: string) => {
    const commonActions = [
      {
        title: '完善个人资料',
        description: '添加头像、联系方式等个人信息',
        href: '/profile',
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        ),
        color: 'from-blue-500 to-blue-600'
      },
      {
        title: '查看团队成员',
        description: '了解您的团队成员和协作伙伴',
        href: '/team',
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        ),
        color: 'from-purple-500 to-purple-600'
      }
    ];

    if (role === 'ADMIN' || role === 'LEADER') {
      return [
        {
          title: '创建第一个项目',
          description: '开始您的第一个团队协作项目',
          href: '/projects/new',
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          ),
          color: 'from-green-500 to-green-600'
        },
        ...commonActions
      ];
    }

    return [
      ...commonActions,
      {
        title: '浏览项目',
        description: '查看可参与的项目',
        href: '/projects',
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        ),
        color: 'from-orange-500 to-orange-600'
      }
    ];
  };

  const quickActions = getQuickActions(userRole);

  return (
    <div className="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-800 dark:to-gray-900 rounded-xl p-8 border border-primary-200 dark:border-gray-700">
      <div className="text-center mb-8">
        <div className="mx-auto w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mb-4">
          <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          欢迎加入 LabSync，{userName}！
        </h2>
        <p className="text-lg text-primary-600 dark:text-primary-400 mb-2">
          {getRoleDisplayName(userRole)}
        </p>
        <p className="text-gray-600 dark:text-gray-400">
          {getWelcomeMessage(userRole)}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {quickActions.map((action, index) => (
          <Link
            key={index}
            href={action.href}
            className="group block p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-600"
          >
            <div className={`inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r ${action.color} text-white rounded-lg mb-4 group-hover:scale-110 transition-transform`}>
              {action.icon}
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400">
              {action.title}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              {action.description}
            </p>
          </Link>
        ))}
      </div>

      <div className="mt-8 p-4 bg-primary-50 dark:bg-gray-700 rounded-lg border border-primary-200 dark:border-gray-600">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-primary-600 dark:text-primary-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="text-sm font-medium text-primary-800 dark:text-primary-300 mb-1">
              需要帮助？
            </h4>
            <p className="text-sm text-primary-700 dark:text-primary-400">
              如果您在使用过程中遇到任何问题，可以联系管理员或查看帮助文档。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
