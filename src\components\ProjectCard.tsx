import { format } from 'date-fns';
import Link from 'next/link';

interface ProjectCardProps {
  project: {
    id: string;
    title: string;
    description: string | null;
    startDate: Date;
    endDate: Date | null;
    status: string;
    progress: number;
    owner: {
      name: string;
    };
    _count?: {
      tasks: number;
      files: number;
    };
  };
}

export default function ProjectCard({ project }: ProjectCardProps) {
  // 格式化日期
  const formatDate = (date: Date | null) => {
    if (!date) return '未设置';
    return format(new Date(date), 'yyyy-MM-dd');
  };

  // 根据项目状态获取状态标签样式
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return '规划中';
      case 'ACTIVE':
        return '进行中';
      case 'COMPLETED':
        return '已完成';
      case 'ARCHIVED':
        return '已归档';
      default:
        return status;
    }
  };

  // 计算进度条颜色
  const getProgressColor = (progress: number) => {
    if (progress < 0.3) return 'bg-red-500';
    if (progress < 0.7) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden card-hover">
      <div className="p-5">
        <div className="flex justify-between items-start mb-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            <Link href={`/projects/${project.id}`} className="hover:text-primary-600 dark:hover:text-primary-400">
              {project.title}
            </Link>
          </h3>
          <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(project.status)}`}>
            {getStatusText(project.status)}
          </span>
        </div>
        
        <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
          {project.description || '无描述'}
        </p>
        
        <div className="mb-4">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
            <span>进度</span>
            <span>{Math.round(project.progress * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${getProgressColor(project.progress)}`} 
              style={{ width: `${project.progress * 100}%` }}
            ></div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-2 text-sm mb-4">
          <div>
            <p className="text-gray-500 dark:text-gray-400">开始日期</p>
            <p>{formatDate(project.startDate)}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">截止日期</p>
            <p>{formatDate(project.endDate)}</p>
          </div>
        </div>
        
        <div className="flex justify-between items-center text-sm">
          <div className="text-gray-600 dark:text-gray-400">
            负责人: {project.owner.name}
          </div>
          
          {project._count && (
            <div className="flex space-x-3 text-gray-500 dark:text-gray-400">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                {project._count.tasks}
              </div>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                </svg>
                {project._count.files}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
