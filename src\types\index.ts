// 基础用户类型
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
  avatar: string | null;
  age: number | null;
  bio: string | null;
  phone: string | null;
  department: string | null;
  position: string | null;
  approvedAt: Date | null;
  approvedBy: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// 用户选择器中使用的用户类型
export interface UserWithProfile extends Omit<User, 'avatar'> {
  avatar?: string | null;
}

// 项目成员类型
export interface ProjectMember {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string | null;
  position?: string | null;
  department?: string | null;
}

// 项目所有者类型
export interface ProjectOwner {
  id: string;
  name: string;
  email: string;
  avatar?: string | null;
  position?: string | null;
}

// 任务分配者类型
export interface TaskAssignee {
  id: string;
  name: string;
  email: string;
  avatar?: string | null;
}

// 文件上传者类型
export interface FileUploader {
  id: string;
  name: string;
  email: string;
}

// 任务类型
export interface Task {
  id: string;
  title: string;
  description: string | null;
  dueDate: Date | null;
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  createdAt: Date;
  updatedAt: Date;
  projectId: string;
  assigneeId: string | null;
  assignee: TaskAssignee | null;
}

// 文件类型
export interface File {
  id: string;
  name: string;
  path: string;
  type: string;
  size: number;
  description: string | null;
  createdAt: Date;
  updatedAt: Date;
  uploaderId: string;
  projectId: string | null;
  taskId: string | null;
  uploader: FileUploader;
  project?: {
    id: string;
    title: string;
  } | null;
  task?: {
    id: string;
    title: string;
  } | null;
}

// 项目类型
export interface Project {
  id: string;
  title: string;
  description: string | null;
  startDate: Date;
  endDate: Date | null;
  status: 'PLANNING' | 'ACTIVE' | 'COMPLETED' | 'ARCHIVED';
  progress: number;
  createdAt: Date;
  updatedAt: Date;
  ownerId: string;
  owner: ProjectOwner;
  members: ProjectMember[];
  tasks: Task[];
  files: File[];
}

// API 响应类型
export interface ApiResponse<T = any> {
  success?: boolean;
  message?: string;
  data?: T;
  error?: string;
}

// 任务统计类型
export interface TaskStats {
  todo: number;
  inProgress: number;
  review: number;
  completed: number;
}

// 成员统计类型
export interface MemberStats {
  name: string;
  completedTasks: number;
  totalTasks: number;
}

// 表单数据类型
export interface ProjectFormData {
  title: string;
  description?: string;
  startDate: string;
  endDate?: string;
  members?: string[];
}

export interface TaskFormData {
  title: string;
  description?: string;
  dueDate?: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  assigneeId?: string;
}

export interface UserFormData {
  name: string;
  email: string;
  password?: string;
  role?: string;
  age?: number;
  bio?: string;
  phone?: string;
  department?: string;
  position?: string;
}

// 聊天相关类型
export interface ChatMessage {
  id: string;
  content: string;
  type: 'TEXT' | 'FILE' | 'IMAGE';
  createdAt: Date;
  updatedAt: Date;
  senderId: string;
  chatId: string;
  sender: {
    id: string;
    name: string;
    avatar: string | null;
  };
}

export interface Chat {
  id: string;
  type: 'PRIVATE' | 'GROUP';
  name: string | null;
  createdAt: Date;
  updatedAt: Date;
  participants: User[];
  messages: ChatMessage[];
}

// 项目消息类型
export interface ProjectMessage {
  id: string;
  content: string;
  type: 'TEXT' | 'FILE' | 'IMAGE';
  createdAt: Date;
  updatedAt: Date;
  senderId: string;
  projectId: string;
  sender: {
    id: string;
    name: string;
    avatar: string | null;
  };
}

// 通知类型
export interface Notification {
  id: string;
  type: 'TASK_ASSIGNED' | 'TASK_COMPLETED' | 'PROJECT_UPDATED' | 'MESSAGE_RECEIVED' | 'FILE_UPLOADED' | 'MEMBER_ADDED';
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  relatedId?: string; // 相关的项目、任务或消息ID
  relatedType?: 'project' | 'task' | 'message' | 'file';
}

// 通知计数类型
export interface NotificationCounts {
  unreadChats: number;
  unreadTasks: number;
  unreadProjectMessages: number;
  unreadNotifications: number;
  total: number;
}

// 分页类型
export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 文件上传类型
export interface FileUploadResponse {
  id: string;
  name: string;
  path: string;
  type: string;
  size: number;
  url: string;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
}
