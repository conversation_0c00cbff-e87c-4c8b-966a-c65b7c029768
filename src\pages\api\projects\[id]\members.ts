import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';
import { notifyMemberAdded } from '@/lib/notifications';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const { id: projectId } = req.query;
  const currentUserId = await getCurrentUserId(req, res);

  // 验证项目ID
  if (!projectId || Array.isArray(projectId)) {
    return res.status(400).json({ message: '无效的项目ID' });
  }

  // 检查项目是否存在以及用户权限
  const project = await prisma.project.findUnique({
    where: { id: projectId },
    include: {
      members: true,
    },
  });

  if (!project) {
    return res.status(404).json({ message: '项目不存在' });
  }

  // 检查用户是否为项目负责人
  const isOwner = project.ownerId === currentUserId;
  if (!isOwner) {
    return res.status(403).json({ message: '只有项目负责人可以管理成员' });
  }

  // 处理POST请求 - 添加成员
  if (req.method === 'POST') {
    try {
      const { userId } = req.body;

      // 验证用户ID
      if (!userId) {
        return res.status(400).json({ message: '用户ID为必填项' });
      }

      // 检查用户是否存在且已审核
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return res.status(404).json({ message: '用户不存在' });
      }

      if (user.status !== 'APPROVED') {
        return res.status(400).json({ message: '只能添加已审核的用户' });
      }

      // 检查用户是否已经是项目成员
      if (project.ownerId === userId) {
        return res.status(400).json({ message: '用户已经是项目负责人' });
      }

      const isAlreadyMember = project.members.some(member => member.id === userId);
      if (isAlreadyMember) {
        return res.status(400).json({ message: '用户已经是项目成员' });
      }

      // 添加成员到项目
      await prisma.project.update({
        where: { id: projectId },
        data: {
          members: {
            connect: { id: userId },
          },
        },
      });

      // 返回更新后的项目信息
      const updatedProject = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          members: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              position: true,
              department: true,
            },
          },
        },
      });

      // 发送成员添加通知
      try {
        // 获取当前用户信息（添加者）
        const currentUser = await prisma.user.findUnique({
          where: { id: currentUserId! },
          select: { name: true },
        });

        if (currentUser && userId !== currentUserId) {
          await notifyMemberAdded(
            projectId,
            userId,
            currentUser.name,
            updatedProject!.title
          );
        }
      } catch (notificationError) {
        console.error('发送成员添加通知失败:', notificationError);
        // 不影响成员添加，只记录错误
      }

      return res.status(200).json({
        message: '成员添加成功',
        project: updatedProject,
      });
    } catch (error) {
      console.error('添加成员失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理DELETE请求 - 移除成员
  if (req.method === 'DELETE') {
    try {
      const { userId } = req.body;

      // 验证用户ID
      if (!userId) {
        return res.status(400).json({ message: '用户ID为必填项' });
      }

      // 检查用户是否为项目成员
      const isMember = project.members.some(member => member.id === userId);
      if (!isMember) {
        return res.status(400).json({ message: '用户不是项目成员' });
      }

      // 不能移除项目负责人
      if (project.ownerId === userId) {
        return res.status(400).json({ message: '不能移除项目负责人' });
      }

      // 从项目中移除成员
      await prisma.project.update({
        where: { id: projectId },
        data: {
          members: {
            disconnect: { id: userId },
          },
        },
      });

      // 返回更新后的项目信息
      const updatedProject = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          members: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              position: true,
              department: true,
            },
          },
        },
      });

      return res.status(200).json({
        message: '成员移除成功',
        project: updatedProject,
      });
    } catch (error) {
      console.error('移除成员失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理GET请求 - 获取项目成员列表
  if (req.method === 'GET') {
    try {
      const projectWithMembers = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          owner: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              position: true,
              department: true,
            },
          },
          members: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              position: true,
              department: true,
            },
          },
        },
      });

      if (!projectWithMembers) {
        return res.status(404).json({ message: '项目不存在' });
      }

      return res.status(200).json({
        owner: projectWithMembers.owner,
        members: projectWithMembers.members,
        totalMembers: projectWithMembers.members.length + 1, // +1 for owner
      });
    } catch (error) {
      console.error('获取成员列表失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
