/* 任务详情页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.task-detail {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.task-header {
  margin-bottom: 30rpx;
}

.task-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #6b7280;
}

.meta-icon {
  font-size: 28rpx;
}

.task-desc {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.task-assignees {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
}

.assignee-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.assignee-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 20rpx;
  background: #f9fafb;
  border-radius: 20rpx;
}

.assignee-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  overflow: hidden;
}

.assignee-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.assignee-name {
  font-size: 26rpx;
  color: #1f2937;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-outline {
  background: transparent;
  color: #3b82f6;
  border: 2rpx solid #3b82f6;
}

.loading {
  text-align: center;
  padding: 60rpx;
  color: #6b7280;
  font-size: 28rpx;
}
