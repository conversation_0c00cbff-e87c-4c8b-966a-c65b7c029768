import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import Link from 'next/link';

interface InviteCode {
  id: string;
  code: string;
  description: string | null;
  maxUses: number;
  usedCount: number;
  expiresAt: string | null;
  isActive: boolean;
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  usedBy: Array<{
    id: string;
    name: string;
    email: string;
    createdAt: string;
  }>;
}

export default function InviteCodesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [inviteCodes, setInviteCodes] = useState<InviteCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [createForm, setCreateForm] = useState({
    description: '',
    maxUses: 1,
    expiresAt: ''
  });

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/login');
      return;
    }

    if (session.user.role !== 'ADMIN') {
      router.push('/');
      return;
    }

    fetchInviteCodes();
  }, [session, status, router]);

  const fetchInviteCodes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/invite-codes');
      const result = await response.json();

      if (result.success) {
        setInviteCodes(result.data);
      } else {
        setError(result.message || '获取邀请码失败');
      }
    } catch (error) {
      setError('网络错误，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  const createInviteCode = async () => {
    try {
      const response = await fetch('/api/invite-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(createForm),
      });

      const result = await response.json();

      if (result.success) {
        setInviteCodes([result.data, ...inviteCodes]);
        setShowCreateForm(false);
        setCreateForm({ description: '', maxUses: 1, expiresAt: '' });
      } else {
        setError(result.message || '创建邀请码失败');
      }
    } catch (error) {
      setError('网络错误，请稍后再试');
    }
  };

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code).then(() => {
      alert('邀请码已复制到剪贴板');
    });
  };

  const deleteInviteCode = async (id: string, code: string) => {
    if (!confirm(`确定要删除邀请码 ${code} 吗？\n\n注意：如果邀请码已被使用，将会被禁用而不是删除。`)) {
      return;
    }

    try {
      const response = await fetch('/api/invite-codes', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }),
      });

      const result = await response.json();

      if (result.success) {
        // 刷新列表
        fetchInviteCodes();
        alert(result.message || '操作成功');
      } else {
        setError(result.message || '删除失败');
      }
    } catch (error) {
      setError('网络错误，请稍后再试');
    }
  };

  const toggleInviteCodeStatus = async (id: string, code: string, currentStatus: boolean) => {
    const action = currentStatus ? '禁用' : '启用';
    if (!confirm(`确定要${action}邀请码 ${code} 吗？`)) {
      return;
    }

    try {
      const response = await fetch('/api/invite-codes', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, isActive: !currentStatus }),
      });

      const result = await response.json();

      if (result.success) {
        // 刷新列表
        fetchInviteCodes();
        alert(result.message || '操作成功');
      } else {
        setError(result.message || '操作失败');
      }
    } catch (error) {
      setError('网络错误，请稍后再试');
    }
  };

  const getInviteCodeStatus = (inviteCode: InviteCode) => {
    if (!inviteCode.isActive) {
      return { text: '已禁用', color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' };
    }

    if (inviteCode.expiresAt && new Date() > new Date(inviteCode.expiresAt)) {
      return { text: '已过期', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' };
    }

    if (inviteCode.usedCount >= inviteCode.maxUses) {
      return { text: '已用完', color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' };
    }

    return { text: '有效', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' };
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            邀请码管理
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            管理课题组成员邀请码
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          生成邀请码
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* 创建表单 */}
      {showCreateForm && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            生成新邀请码
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                描述
              </label>
              <input
                type="text"
                value={createForm.description}
                onChange={(e) => setCreateForm({ ...createForm, description: e.target.value })}
                placeholder="如：张教授邀请的博士生"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                最大使用次数
              </label>
              <input
                type="number"
                min="1"
                value={createForm.maxUses}
                onChange={(e) => setCreateForm({ ...createForm, maxUses: parseInt(e.target.value) || 1 })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                过期时间（可选）
              </label>
              <input
                type="datetime-local"
                value={createForm.expiresAt}
                onChange={(e) => setCreateForm({ ...createForm, expiresAt: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>
            <div className="flex space-x-3">
              <button
                onClick={createInviteCode}
                className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
              >
                生成
              </button>
              <button
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m0 0v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m0 0V7a2 2 0 012-2m0 0V5a2 2 0 012-2h4a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">总邀请码</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{inviteCodes.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">有效邀请码</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {inviteCodes.filter(code => {
                  const status = getInviteCodeStatus(code);
                  return status.text === '有效';
                }).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
              <svg className="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已用完</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {inviteCodes.filter(code => code.usedCount >= code.maxUses).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">总使用次数</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {inviteCodes.reduce((sum, code) => sum + code.usedCount, 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 邀请码列表 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            邀请码列表
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  邀请码
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  描述
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  使用情况
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  创建时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {inviteCodes.map((inviteCode) => (
                <tr key={inviteCode.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <code className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                        {inviteCode.code}
                      </code>
                      <button
                        onClick={() => copyToClipboard(inviteCode.code)}
                        className="ml-2 text-primary-600 hover:text-primary-700 text-sm"
                      >
                        复制
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {inviteCode.description || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    <div className="flex items-center">
                      <span className={inviteCode.usedCount >= inviteCode.maxUses ? 'text-red-600 font-semibold' : ''}>
                        {inviteCode.usedCount} / {inviteCode.maxUses}
                      </span>
                      {inviteCode.usedCount >= inviteCode.maxUses && (
                        <span className="ml-2 text-xs text-red-500">(已用完)</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {(() => {
                      const status = getInviteCodeStatus(inviteCode);
                      return (
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${status.color}`}>
                          {status.text}
                        </span>
                      );
                    })()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {new Date(inviteCode.createdAt).toLocaleDateString('zh-CN')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => toggleInviteCodeStatus(inviteCode.id, inviteCode.code, inviteCode.isActive)}
                        className={`font-medium ${
                          inviteCode.isActive
                            ? 'text-orange-600 hover:text-orange-700'
                            : 'text-green-600 hover:text-green-700'
                        }`}
                        title={inviteCode.isActive ? '禁用邀请码' : '启用邀请码'}
                      >
                        {inviteCode.isActive ? '禁用' : '启用'}
                      </button>
                      <button
                        onClick={() => deleteInviteCode(inviteCode.id, inviteCode.code)}
                        className="text-red-600 hover:text-red-700 font-medium"
                        title={inviteCode.usedCount > 0 ? '已使用的邀请码将被禁用' : '删除邀请码'}
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
