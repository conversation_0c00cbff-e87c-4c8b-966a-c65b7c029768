# TabBar 图标制作指南

## 🚨 重要说明

微信小程序TabBar图标**只支持PNG/JPG格式**，不支持SVG！

## 📐 规格要求

### 尺寸
- **大小**: 81px × 81px
- **格式**: PNG (推荐) 或 JPG
- **背景**: 透明 (PNG)

### 颜色规范
- **未激活状态**: #9CA3AF (灰色)
- **激活状态**: #3B82F6 (蓝色)

## 🎨 需要制作的图标

### 1. 首页图标 (home.png / home-active.png)
```
图标描述: 房子轮廓
设计要点:
- 简单的房子形状
- 2-3px 描边
- 屋顶 + 墙体 + 门窗
- 线条风格，无填充
```

### 2. 项目图标 (project.png / project-active.png)
```
图标描述: 文件夹
设计要点:
- 标准文件夹形状
- 带有标签页
- 简洁线条设计
```

### 3. 任务图标 (task.png / task-active.png)
```
图标描述: 任务清单
设计要点:
- 矩形清单背景
- 3个复选框
- 其中1个已勾选
```

### 4. 聊天图标 (chat.png / chat-active.png)
```
图标描述: 对话气泡
设计要点:
- 圆角矩形气泡
- 带有小尾巴
- 内部可有省略号
```

### 5. 个人图标 (profile.png / profile-active.png)
```
图标描述: 用户头像
设计要点:
- 圆形头部
- 肩膀轮廓
- 简化的人形图标
```

## 🛠️ 制作工具推荐

### 在线工具
1. **Iconfont** - https://www.iconfont.cn/
2. **Feather Icons** - https://feathericons.com/
3. **Heroicons** - https://heroicons.com/

### 设计软件
1. **Figma** (免费)
2. **Sketch** (Mac)
3. **Adobe Illustrator**

## 📋 制作步骤

### 方法1: 使用在线图标库
1. 访问 Iconfont 或 Feather Icons
2. 搜索对应的图标 (home, folder, checklist, chat, user)
3. 下载PNG格式，尺寸设为81px
4. 使用图片编辑器调整颜色

### 方法2: 自己设计
1. 创建81px × 81px画布
2. 使用2-3px描边绘制图标
3. 导出PNG格式
4. 制作两个版本 (灰色和蓝色)

## 🎯 快速解决方案

如果暂时无法制作图标，当前使用emoji + 文字的方案也很好：
- 🏠 首页
- 📁 项目  
- ✅ 任务
- 💬 聊天
- 👤 我的

这样既美观又符合微信小程序的规范！

## 📝 文件命名

确保文件名完全匹配：
- `home.png` / `home-active.png`
- `project.png` / `project-active.png`
- `task.png` / `task-active.png`
- `chat.png` / `chat-active.png`
- `profile.png` / `profile-active.png`

制作完成后，将PNG文件放入 `miniprogram/images/` 目录，然后更新 `app.json` 中的 `iconPath` 和 `selectedIconPath`。
