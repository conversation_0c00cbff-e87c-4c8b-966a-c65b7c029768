import { NextApiRequest, NextApiResponse } from 'next';
import formidable, { IncomingForm } from 'formidable';
import path from 'path';
import fs from 'fs';
import { getCurrentUserId } from '@/lib/auth';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    // 验证用户身份
    const userId = await getCurrentUserId(req, res);
    if (!userId) {
      return res.status(401).json({ message: '用户未认证' });
    }

    // 确保上传目录存在
    const uploadDir = path.join(process.cwd(), 'public', 'chat-files');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 配置formidable
    const form = new IncomingForm({
      uploadDir,
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
    });

    // 解析上传的文件
    const [fields, files] = await new Promise<[any, any]>((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        else resolve([fields, files]);
      });
    });

    const uploadedFile = Array.isArray(files.file) ? files.file[0] : files.file;

    if (!uploadedFile) {
      return res.status(400).json({ message: '没有上传文件' });
    }

    // 生成唯一文件名并重命名文件
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(uploadedFile.originalFilename || '');
    const newFileName = `${timestamp}_${randomString}${fileExtension}`;
    const newFilePath = path.join(uploadDir, newFileName);

    // 重命名文件
    fs.renameSync(uploadedFile.filepath, newFilePath);

    // 获取文件信息
    const fileName = uploadedFile.originalFilename || 'unknown';
    const fileSize = uploadedFile.size;
    const fileType = uploadedFile.mimetype || 'application/octet-stream';

    // 构建文件URL（使用我们重命名后的文件名）
    const fileUrl = `/chat-files/${newFileName}`;

    console.log('聊天文件上传成功:', {
      originalFilename: fileName,
      size: fileSize,
      filepath: newFilePath,
      newFilename: newFileName,
      url: fileUrl,
    });

    return res.status(200).json({
      fileName,
      fileUrl,
      fileSize,
      message: '文件上传成功',
    });
  } catch (error) {
    console.error('聊天文件上传失败:', error);
    return res.status(500).json({ message: '文件上传失败，请稍后再试' });
  }
}
