import { hash } from 'bcryptjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/pages/api/auth/[...nextauth]';

// 密码加密
export async function hashPassword(password: string): Promise<string> {
  return await hash(password, 12);
}

// 获取服务器端会话
export async function getSession(req: NextApiRequest, res: NextApiResponse) {
  return await getServerSession(req, res, authOptions);
}

// 检查用户是否已认证
export async function isAuthenticated(req: NextApiRequest, res: NextApiResponse) {
  const session = await getSession(req, res);
  return !!session;
}

// 检查用户是否为管理员
export async function isAdmin(req: NextApiRequest, res: NextApiResponse) {
  const session = await getSession(req, res);
  return session?.user?.role === 'ADMIN';
}

// 检查用户是否为项目负责人
export async function isProjectLeader(req: NextApiRequest, res: NextApiResponse) {
  const session = await getSession(req, res);
  return session?.user?.role === 'LEADER' || session?.user?.role === 'ADMIN';
}

// 获取当前用户ID
export async function getCurrentUserId(req: NextApiRequest, res: NextApiResponse): Promise<string | null> {
  const session = await getSession(req, res);
  return session?.user?.id || null;
}
