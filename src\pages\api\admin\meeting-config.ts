import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.email) {
      return res.status(401).json({ message: '未授权' });
    }

    // 检查用户权限（只有管理员可以配置）
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return res.status(403).json({ message: '权限不足' });
    }

    if (req.method === 'GET') {
      // 获取配置
      const config = await prisma.meetingConfig.findFirst();
      
      if (config) {
        // 不返回敏感信息的完整内容，只返回部分字符
        const safeConfig = {
          ...config,
          secretKey: config.secretKey ? '***' + config.secretKey.slice(-4) : '',
        };
        return res.status(200).json({ config: safeConfig });
      } else {
        return res.status(200).json({ config: null });
      }
    }

    if (req.method === 'POST') {
      // 保存配置
      const { appId, secretId, secretKey, sdkId, isEnabled } = req.body;

      if (!appId || !secretId || !secretKey || !sdkId) {
        return res.status(400).json({ message: '请填写所有必填字段' });
      }

      // 检查是否已存在配置
      const existingConfig = await prisma.meetingConfig.findFirst();

      let config;
      if (existingConfig) {
        // 更新现有配置
        config = await prisma.meetingConfig.update({
          where: { id: existingConfig.id },
          data: {
            appId,
            secretId,
            secretKey,
            sdkId,
            isEnabled,
            updatedAt: new Date(),
          },
        });
      } else {
        // 创建新配置
        config = await prisma.meetingConfig.create({
          data: {
            appId,
            secretId,
            secretKey,
            sdkId,
            isEnabled,
          },
        });
      }

      return res.status(200).json({ 
        message: '配置保存成功',
        config: {
          ...config,
          secretKey: '***' + config.secretKey.slice(-4),
        }
      });
    }

    return res.status(405).json({ message: '方法不允许' });
  } catch (error) {
    console.error('腾讯会议配置API错误:', error);
    return res.status(500).json({ message: '服务器错误' });
  } finally {
    await prisma.$disconnect();
  }
}
