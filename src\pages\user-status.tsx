import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import Link from 'next/link';

interface UserStatusInfo {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    projectCount: number;
    taskCount: number;
  };
  permissions: {
    canAccessDashboard: boolean;
    canCreateProjects: boolean;
    canManageUsers: boolean;
    isNewUser: boolean;
  };
  statusMessage: string;
}

export default function UserStatusPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [userStatus, setUserStatus] = useState<UserStatusInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    fetchUserStatus();
  }, [session, status, router]);

  const fetchUserStatus = async () => {
    try {
      const response = await fetch('/api/user/status');
      const result = await response.json();

      if (result.success) {
        setUserStatus(result.data);
        
        // 如果用户已通过审核，重定向到首页
        if (result.data.permissions.canAccessDashboard) {
          router.push('/');
          return;
        }
      } else {
        setError(result.message || '获取用户状态失败');
      }
    } catch (error) {
      console.error('获取用户状态失败:', error);
      setError('网络错误，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/signin' });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return (
          <div className="mx-auto w-20 h-20 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mb-4">
            <svg className="w-10 h-10 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      case 'REJECTED':
        return (
          <div className="mx-auto w-20 h-20 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4">
            <svg className="w-10 h-10 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="mx-auto w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
            <svg className="w-10 h-10 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'text-yellow-600 dark:text-yellow-400';
      case 'REJECTED': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">正在检查用户状态...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">出现错误</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
          <div className="space-y-3">
            <button
              onClick={fetchUserStatus}
              className="w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
            >
              重试
            </button>
            <button
              onClick={handleSignOut}
              className="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              重新登录
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!userStatus) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          {getStatusIcon(userStatus.user.status)}
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            账号状态：
            <span className={`ml-2 ${getStatusColor(userStatus.user.status)}`}>
              {userStatus.user.status === 'PENDING' ? '待审核' :
               userStatus.user.status === 'REJECTED' ? '已拒绝' : '未知'}
            </span>
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {userStatus.statusMessage}
          </p>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">账号信息</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500 dark:text-gray-400">姓名：</span>
              <span className="text-gray-900 dark:text-gray-100 ml-2">{userStatus.user.name}</span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">邮箱：</span>
              <span className="text-gray-900 dark:text-gray-100 ml-2">{userStatus.user.email}</span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">角色：</span>
              <span className="text-gray-900 dark:text-gray-100 ml-2">
                {userStatus.user.role === 'ADMIN' ? '管理员' :
                 userStatus.user.role === 'LEADER' ? '项目负责人' : '团队成员'}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">注册时间：</span>
              <span className="text-gray-900 dark:text-gray-100 ml-2">
                {new Date(userStatus.user.createdAt).toLocaleDateString('zh-CN')}
              </span>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <button
            onClick={fetchUserStatus}
            className="w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            刷新状态
          </button>
          
          {userStatus.user.status === 'REJECTED' && (
            <Link
              href="/contact"
              className="block w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-center"
            >
              联系管理员
            </Link>
          )}
          
          <button
            onClick={handleSignOut}
            className="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            退出登录
          </button>
        </div>
      </div>
    </div>
  );
}
