/* 文件管理页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.file-list {
  margin-bottom: 120rpx;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.file-item:active {
  transform: scale(0.98);
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f3f4f6;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 40rpx;
}

.file-icon.image {
  background: #dbeafe;
  color: #3b82f6;
}

.file-icon.document {
  background: #fef3c7;
  color: #d97706;
}

.file-icon.archive {
  background: #f3e8ff;
  color: #8b5cf6;
}

.file-content {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-info {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #6b7280;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  transition: background-color 0.2s;
}

.download-btn {
  background: #dbeafe;
  color: #3b82f6;
}

.delete-btn {
  background: #fee2e2;
  color: #ef4444;
}

.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: #3b82f6;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
  z-index: 100;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}

.loading {
  text-align: center;
  padding: 60rpx;
  color: #6b7280;
  font-size: 28rpx;
}

.empty {
  text-align: center;
  padding: 120rpx 60rpx;
  color: #9ca3af;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #d1d5db;
}
