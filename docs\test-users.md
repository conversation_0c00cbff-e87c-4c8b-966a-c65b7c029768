# LabSync 测试用户信息

## 概述
已成功添加三个测试用户到 LabSync 系统中，包括一个管理员和两个研究员。所有用户都已通过审核，可以直接登录使用。

## 测试用户详情

### 1. 管理员账户
- **姓名**: 张教授
- **邮箱**: <EMAIL>
- **密码**: 123456
- **角色**: ADMIN (管理员)
- **状态**: APPROVED (已审核通过)
- **年龄**: 45岁
- **部门**: 计算机科学与技术学院
- **职位**: 教授/博士生导师
- **电话**: 13800138000
- **头像**: /avatars/default-admin.png
- **个人简介**: 计算机科学与技术学院教授，博士生导师，主要研究方向为人工智能、机器学习和数据挖掘。

### 2. 研究员账户1
- **姓名**: 李研究员
- **邮箱**: <EMAIL>
- **密码**: 123456
- **角色**: MEMBER (普通成员)
- **状态**: APPROVED (已审核通过)
- **年龄**: 28岁
- **部门**: 计算机科学系
- **职位**: 研究员
- **电话**: 13800138001
- **头像**: /avatars/default-user1.png
- **个人简介**: 专注于机器学习和深度学习研究，具有丰富的数据分析经验。

### 3. 研究员账户2
- **姓名**: 王博士
- **邮箱**: <EMAIL>
- **密码**: 123456
- **角色**: MEMBER (普通成员)
- **状态**: APPROVED (已审核通过)
- **年龄**: 32岁
- **部门**: 人工智能实验室
- **职位**: 博士后研究员
- **电话**: 13800138002
- **头像**: /avatars/default-user2.png
- **个人简介**: 计算机视觉和图像处理专家，发表多篇高质量学术论文。

## 示例数据

### 项目
- **项目名称**: 人工智能研究项目
- **项目负责人**: 张教授 (<EMAIL>)
- **项目成员**: 李研究员、王博士
- **项目状态**: ACTIVE (进行中)
- **项目进度**: 35%
- **项目描述**: 基于深度学习的图像识别技术研究，旨在开发高精度的图像分类和目标检测算法。

### 任务
1. **数据收集与预处理**
   - 负责人: 李研究员
   - 状态: IN_PROGRESS (进行中)
   - 优先级: HIGH (高)
   - 描述: 收集图像数据集，包括各种类别的图像样本，并进行数据清洗、标注和预处理工作。

2. **模型架构设计**
   - 负责人: 王博士
   - 状态: TODO (待开始)
   - 优先级: MEDIUM (中)
   - 描述: 设计深度学习模型架构，包括卷积神经网络的层次结构、激活函数选择和优化策略。

3. **实验结果分析**
   - 负责人: 李研究员
   - 状态: TODO (待开始)
   - 优先级: LOW (低)
   - 描述: 分析模型训练结果，评估模型性能指标，撰写实验报告和技术文档。

## 登录测试

可以使用以下任一账户登录系统进行测试：

1. **管理员登录**:
   - 邮箱: <EMAIL>
   - 密码: 123456
   - 权限: 完整的管理员权限，可以管理所有用户、项目和任务

2. **普通用户登录**:
   - 邮箱: <EMAIL> 或 <EMAIL>
   - 密码: 123456
   - 权限: 普通成员权限，可以参与项目、完成任务、上传文件等

## 注意事项

1. 所有测试用户的密码都是 `123456`，在生产环境中应使用更安全的密码
2. 所有用户都已通过审核，可以直接登录使用
3. 头像路径为占位符，实际使用时需要提供真实的头像文件
4. 测试数据包含完整的项目成员关系和任务分配，便于测试各种功能

## 数据库操作

测试用户数据通过以下命令创建：
```bash
npm run db:seed
```

如需重新初始化数据库：
```bash
npx prisma db push --force-reset
npm run db:seed
```
