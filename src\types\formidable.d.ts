declare module 'formidable' {
  export interface File {
    size: number;
    filepath: string;
    originalFilename?: string;
    newFilename?: string;
    mimetype?: string;
  }

  export interface Fields {
    [key: string]: string | string[];
  }

  export interface Files {
    [key: string]: File | File[];
  }

  export interface IncomingFormOptions {
    uploadDir?: string;
    keepExtensions?: boolean;
    maxFileSize?: number;
  }

  export class IncomingForm {
    constructor(options?: IncomingFormOptions);
    parse(
      req: any,
      callback: (err: any, fields: Fields, files: Files) => void
    ): void;
  }
}
