<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头像上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        .file-input {
            display: none;
        }
        .preview {
            max-width: 200px;
            max-height: 200px;
            margin: 20px auto;
            border-radius: 10px;
            display: none;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .upload-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>头像上传测试工具</h1>
        <p>这个工具可以帮助测试头像上传功能是否正常工作。</p>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <p>点击这里选择头像文件</p>
            <p>或者拖拽文件到这里</p>
            <p style="font-size: 12px; color: #666;">支持 JPEG、PNG、GIF、WebP 格式，最大 5MB</p>
        </div>
        
        <input type="file" id="fileInput" class="file-input" accept="image/jpeg,image/png,image/gif,image/webp">
        
        <img id="preview" class="preview" alt="预览">
        
        <div style="text-align: center;">
            <button id="uploadBtn" class="upload-btn" onclick="uploadAvatar()" disabled>上传头像</button>
            <button class="upload-btn" onclick="testAPI()" style="background-color: #28a745;">测试API</button>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在上传...</p>
        </div>
        
        <div id="result" class="result"></div>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
            <h3>调试信息</h3>
            <p><strong>当前URL:</strong> <span id="currentUrl"></span></p>
            <p><strong>用户认证状态:</strong> <span id="authStatus">检查中...</span></p>
            <p><strong>API端点:</strong> /api/upload/avatar</p>
            <div id="debugLog" style="background: #000; color: #0f0; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('currentUrl').textContent = window.location.href;
            checkAuthStatus();
            setupDragAndDrop();
        });
        
        // 检查认证状态
        async function checkAuthStatus() {
            try {
                const response = await fetch('/api/auth/session');
                const session = await response.json();
                
                if (session && session.user) {
                    document.getElementById('authStatus').textContent = `已登录: ${session.user.name || session.user.email}`;
                    document.getElementById('authStatus').style.color = 'green';
                } else {
                    document.getElementById('authStatus').textContent = '未登录';
                    document.getElementById('authStatus').style.color = 'red';
                }
                
                log('认证状态检查完成: ' + JSON.stringify(session));
            } catch (error) {
                document.getElementById('authStatus').textContent = '检查失败';
                document.getElementById('authStatus').style.color = 'red';
                log('认证状态检查失败: ' + error.message);
            }
        }
        
        // 设置拖拽上传
        function setupDragAndDrop() {
            const uploadArea = document.querySelector('.upload-area');
            
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelect(files[0]);
                }
            });
        }
        
        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });
        
        // 处理文件选择
        function handleFileSelect(file) {
            selectedFile = file;
            
            log(`文件选择: ${file.name}, 大小: ${(file.size / 1024).toFixed(2)}KB, 类型: ${file.type}`);
            
            // 验证文件
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                showResult('只支持 JPEG、PNG、GIF、WebP 格式的图片', 'error');
                return;
            }
            
            if (file.size > 5 * 1024 * 1024) {
                showResult('文件大小不能超过 5MB', 'error');
                return;
            }
            
            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('preview');
                preview.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
            
            // 启用上传按钮
            document.getElementById('uploadBtn').disabled = false;
            
            showResult('文件选择成功，可以上传了', 'success');
        }
        
        // 上传头像
        async function uploadAvatar() {
            if (!selectedFile) {
                showResult('请先选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('avatar', selectedFile);
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('uploadBtn').disabled = true;
            
            log('开始上传头像...');
            
            try {
                const response = await fetch('/api/upload/avatar', {
                    method: 'POST',
                    body: formData,
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                log('响应数据: ' + JSON.stringify(result, null, 2));
                
                if (response.ok) {
                    showResult(`上传成功！头像URL: ${result.avatar}`, 'success');
                } else {
                    showResult(`上传失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log('上传错误: ' + error.message);
                showResult(`上传失败: ${error.message}`, 'error');
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('uploadBtn').disabled = false;
            }
        }
        
        // 测试API连接
        async function testAPI() {
            log('测试API连接...');
            
            try {
                const response = await fetch('/api/upload/avatar', {
                    method: 'GET',
                });
                
                log(`API测试响应: ${response.status} ${response.statusText}`);
                
                if (response.status === 405) {
                    showResult('API端点正常（返回405 Method Not Allowed是正确的）', 'success');
                } else {
                    const result = await response.text();
                    log('API测试响应内容: ' + result);
                    showResult(`API响应: ${response.status}`, response.ok ? 'success' : 'error');
                }
            } catch (error) {
                log('API测试错误: ' + error.message);
                showResult(`API测试失败: ${error.message}`, 'error');
            }
        }
        
        // 显示结果
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
            
            log(`结果: [${type.toUpperCase()}] ${message}`);
        }
        
        // 日志记录
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
    </script>
</body>
</html>
