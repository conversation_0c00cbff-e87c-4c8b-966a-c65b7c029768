import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, isAdmin } from '@/lib/auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  // 检查是否为管理员
  if (!(await isAdmin(req, res))) {
    return res.status(403).json({ message: '需要管理员权限' });
  }

  // 处理GET请求 - 获取待审核用户列表
  if (req.method === 'GET') {
    try {
      const pendingUsers = await prisma.user.findMany({
        where: {
          status: 'PENDING',
        },
        select: {
          id: true,
          name: true,
          email: true,
          status: true,
          department: true,
          position: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return res.status(200).json(pendingUsers);
    } catch (error) {
      console.error('获取待审核用户失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
