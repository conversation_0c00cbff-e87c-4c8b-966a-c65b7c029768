import React, { useState } from 'react';
import { getSafeAvatarUrl, generateSolidColorAvatar, getInitials } from '@/lib/avatars';

interface AvatarProps {
  user: {
    id: string;
    name: string;
    avatar?: string | null;
  };
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showTooltip?: boolean;
}

const sizeClasses = {
  xs: 'w-6 h-6 text-xs',
  sm: 'w-8 h-8 text-sm',
  md: 'w-10 h-10 text-base',
  lg: 'w-12 h-12 text-lg',
  xl: 'w-16 h-16 text-xl'
};

export default function Avatar({
  user,
  size = 'md',
  className = '',
  showTooltip = false
}: AvatarProps) {
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const sizeClass = sizeClasses[size];

  // 获取头像URL，如果出错则使用备用方案
  const getAvatarUrl = () => {
    if (hasError || retryCount > 1) {
      // 如果加载失败，生成默认头像
      return generateSolidColorAvatar('#6B7280', getInitials(user.name));
    }
    return getSafeAvatarUrl(user);
  };

  const handleError = () => {
    if (retryCount < 2) {
      setRetryCount(prev => prev + 1);
      // 短暂延迟后重试
      setTimeout(() => {
        setHasError(false);
      }, 100);
    } else {
      setHasError(true);
    }
  };

  const avatarElement = (
    <img
      src={getAvatarUrl()}
      alt={`${user.name}的头像`}
      className={`${sizeClass} rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 ${className}`}
      onError={handleError}
      onLoad={() => {
        setHasError(false);
        setRetryCount(0);
      }}
    />
  );

  if (showTooltip) {
    return (
      <div className="relative group">
        {avatarElement}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
          {user.name}
        </div>
      </div>
    );
  }

  return avatarElement;
}
