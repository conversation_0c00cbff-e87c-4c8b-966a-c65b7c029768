// 首页
const { dashboardApi } = require('../../utils/api.js');
const { formatRelativeTime } = require('../../utils/util.js');
const app = getApp();

Page({
  data: {
    userInfo: {},
    stats: {
      totalProjects: 0,
      activeProjects: 0,
      pendingTasks: 0,
      completedTasks: 0,
      unreadNotifications: 0,
      totalUsers: 0,
      unreadMessages: 0
    },
    recentActivities: [],
    loading: true
  },

  onLoad() {
    this.checkLogin();
  },

  onShow() {
    if (app.isLoggedIn()) {
      this.loadDashboardData();
    }
  },

  // 检查登录状态
  checkLogin() {
    if (!app.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }

    this.setData({
      userInfo: app.globalData.userInfo || {}
    });

    this.loadDashboardData();
  },

  // 加载仪表盘数据
  async loadDashboardData() {
    this.setData({ loading: true });

    try {
      // 尝试加载真实数据
      const statsRes = await dashboardApi.getStats();

      this.setData({
        stats: statsRes.data?.stats || this.getMockStats(),
        recentActivities: statsRes.data?.recentActivities || this.getMockActivities(),
        loading: false
      });

    } catch (error) {
      // 检查是否为API不可用错误
      if (error.message === 'API_UNAVAILABLE') {
        console.log('API服务器未连接，使用模拟数据');
      } else {
        console.error('加载仪表盘数据失败:', error);
      }

      // 使用模拟数据作为后备方案
      this.setData({
        stats: this.getMockStats(),
        recentActivities: this.getMockActivities(),
        loading: false
      });
    }
  },

  // 获取模拟统计数据
  getMockStats() {
    return {
      totalProjects: 8,
      activeProjects: 3,
      pendingTasks: 12,
      completedTasks: 25,
      unreadNotifications: 5,
      totalUsers: 15,
      unreadMessages: 3
    };
  },

  // 获取模拟活动数据
  getMockActivities() {
    return [
      {
        id: 'task-1',
        title: '完成用户界面设计',
        status: 'IN_PROGRESS',
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        project: {
          title: 'LabSync项目'
        }
      },
      {
        id: 'task-2',
        title: '数据库优化',
        status: 'COMPLETED',
        updatedAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
        project: {
          title: '系统优化项目'
        }
      },
      {
        id: 'task-3',
        title: 'API文档编写',
        status: 'TODO',
        updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        project: {
          title: '文档项目'
        }
      },
      {
        id: 'task-4',
        title: '测试用例编写',
        status: 'REVIEW',
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        project: {
          title: '质量保证项目'
        }
      }
    ];
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDashboardData();
    wx.stopPullDownRefresh();
  },

  // 跳转到项目页面
  goToProjects() {
    wx.switchTab({
      url: '/pages/projects/projects'
    });
  },

  // 跳转到任务页面
  goToTasks() {
    wx.switchTab({
      url: '/pages/tasks/tasks'
    });
  },

  // 跳转到聊天页面
  goToChat() {
    wx.switchTab({
      url: '/pages/chat/chat'
    });
  },

  // 跳转到团队页面
  goToTeam() {
    wx.navigateTo({
      url: '/pages/team/team'
    });
  },

  // 跳转到通知页面
  goToNotifications() {
    wx.navigateTo({
      url: '/pages/notifications/notifications'
    });
  },

  // 格式化时间
  formatTime(time) {
    if (!time) return '';
    // 如果是字符串，转换为Date对象
    const date = typeof time === 'string' ? new Date(time) : time;
    return formatRelativeTime(date);
  }
});
