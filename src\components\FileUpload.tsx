import { useState, useRef, ChangeEvent, FormEvent } from 'react';

interface FileUploadProps {
  projectId?: string;
  taskId?: string;
  onUploadComplete?: (file: any) => void;
  compact?: boolean;
  showDragArea?: boolean;
}

export default function FileUpload({ projectId, taskId, onUploadComplete, compact = false, showDragArea = false }: FileUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [description, setDescription] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState('');
  const [progress, setProgress] = useState(0);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setError('');
    }
  };

  // 拖拽处理函数
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      setFile(files[0]);
      setError('');
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleDescriptionChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setDescription(e.target.value);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError('请选择要上传的文件');
      return;
    }

    setIsUploading(true);
    setProgress(0);
    setError('');

    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);
    formData.append('description', description);

    if (projectId) {
      formData.append('projectId', projectId);
    }

    if (taskId) {
      formData.append('taskId', taskId);
    }

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + 5;
          if (newProgress >= 95) {
            clearInterval(progressInterval);
            return 95;
          }
          return newProgress;
        });
      }, 100);

      // 发送上传请求
      const response = await fetch('/api/files', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '文件上传失败');
      }

      setProgress(100);

      const data = await response.json();

      // 重置表单
      setFile(null);
      setDescription('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // 调用上传完成回调
      if (onUploadComplete) {
        onUploadComplete(data);
      }

      // 延迟重置上传状态，以便显示100%进度
      setTimeout(() => {
        setIsUploading(false);
        setProgress(0);
      }, 1000);
    } catch (error) {
      console.error('文件上传失败:', error);
      let errorMessage = '文件上传失败，请稍后再试';

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      setError(errorMessage);
      setIsUploading(false);
      setProgress(0);
    }
  };

  if (compact && showDragArea) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border-2 border-dashed border-gray-300 dark:border-gray-600 p-4">
        <div
          className={`relative transition-all duration-200 ${
            isDragOver ? 'bg-primary-50 dark:bg-primary-900/20 border-primary-300 dark:border-primary-600' : ''
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            disabled={isUploading}
          />

          <div className="text-center cursor-pointer">
            <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              {file ? `已选择: ${file.name}` : '拖拽文件到此处或点击选择'}
            </p>
            {file && (
              <div className="mt-2 flex items-center justify-center space-x-2">
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSubmit(e as any);
                  }}
                  className="btn btn-primary btn-sm"
                  disabled={isUploading}
                >
                  {isUploading ? '上传中...' : '立即上传'}
                </button>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    setFile(null);
                  }}
                  className="btn btn-secondary btn-sm"
                  disabled={isUploading}
                >
                  取消
                </button>
              </div>
            )}
          </div>

          {isUploading && (
            <div className="mt-3">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-primary-500 transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">{progress}%</p>
            </div>
          )}

          {error && (
            <div className="mt-3 text-sm text-red-600 dark:text-red-400 text-center">
              {error}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">上传文件</h3>
        {file && (
          <button
            type="button"
            onClick={() => setFile(null)}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit}>
        {error && (
          <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative mb-4" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {/* 拖拽上传区域 */}
        <div
          className={`mb-4 border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer ${
            isDragOver
              ? 'border-primary-400 bg-primary-50 dark:bg-primary-900/20'
              : 'border-gray-300 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            disabled={isUploading}
          />

          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>

          {file ? (
            <div>
              <p className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                已选择: {file.name}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                大小: {(file.size / 1024).toFixed(2)} KB
              </p>
            </div>
          ) : (
            <div>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">点击选择文件</span> 或拖拽文件到此处
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500">
                支持所有常见文件格式
              </p>
            </div>
          )}
        </div>

        {file && (
          <div className="mb-4">
            <label className="form-label" htmlFor="description">
              文件描述 (可选)
            </label>
            <textarea
              id="description"
              value={description}
              onChange={handleDescriptionChange}
              className="form-input"
              rows={3}
              placeholder="请输入文件描述..."
              disabled={isUploading}
            ></textarea>
          </div>
        )}

        {isUploading && (
          <div className="mb-4">
            <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
              <span>上传进度</span>
              <span>{progress}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-primary-500 transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        )}

        <button
          type="submit"
          className="btn btn-primary w-full"
          disabled={isUploading || !file}
        >
          {isUploading ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              上传中...
            </>
          ) : (
            '上传文件'
          )}
        </button>
      </form>
    </div>
  );
}
