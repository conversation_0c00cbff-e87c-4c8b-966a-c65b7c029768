{"name": "labsync", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:seed": "ts-node --compiler-options \"{\\\"module\\\":\\\"CommonJS\\\"}\" prisma/seed.ts", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.8.2", "@types/formidable": "^3.4.5", "@types/nodemailer": "^6.4.17", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "daisyui": "^5.0.37", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.12.2", "formidable": "^3.5.4", "gsap": "^3.13.0", "multer": "^2.0.0", "next": "^15.3.2", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "postcss": "^8.5.3", "prisma": "^6.8.2", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-file-icon": "^1.6.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "yet-another-react-lightbox": "^3.23.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22.10.5", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^9.17.0", "eslint-config-next": "^15.3.2", "ts-node": "^10.9.2", "typescript": "^5.7.2"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}