import prisma from '@/lib/prisma';
import { notifyProjectMilestone } from '@/lib/systemNotifications';

/**
 * 更新项目进度的统一函数
 * 根据项目中已完成任务的比例计算进度
 */
export async function updateProjectProgress(projectId: string): Promise<void> {
  try {
    // 获取项目信息和任务
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        tasks: {
          select: { status: true }
        },
        members: {
          select: { id: true }
        }
      }
    });

    if (!project) {
      throw new Error('项目不存在');
    }

    const tasks = project.tasks;

    // 如果没有任务，进度为0
    if (tasks.length === 0) {
      await prisma.project.update({
        where: { id: projectId },
        data: { progress: 0 },
      });
      return;
    }

    // 计算已完成任务的比例
    const completedTasks = tasks.filter(task => task.status === 'COMPLETED' || task.status === 'DONE');
    const newProgress = completedTasks.length / tasks.length;
    const oldProgress = project.progress;

    // 更新项目进度
    await prisma.project.update({
      where: { id: projectId },
      data: { progress: newProgress },
    });

    console.log(`项目 ${projectId} 进度已更新: ${Math.round(newProgress * 100)}% (${completedTasks.length}/${tasks.length})`);

    // 检查是否达到重要进度节点，发送通知
    try {
      const memberIds = project.members.map(m => m.id);
      const progressPercent = Math.round(newProgress * 100);
      const oldProgressPercent = Math.round(oldProgress * 100);

      // 重要节点：25%, 50%, 75%, 100%
      const milestones = [25, 50, 75, 100];

      for (const milestone of milestones) {
        if (oldProgressPercent < milestone && progressPercent >= milestone) {
          let milestoneMessage = '';
          switch (milestone) {
            case 25:
              milestoneMessage = '🚀 项目进度达到 25%，开局良好！';
              break;
            case 50:
              milestoneMessage = '⚡ 项目进度过半，继续加油！';
              break;
            case 75:
              milestoneMessage = '🎯 项目进度达到 75%，即将完成！';
              break;
            case 100:
              milestoneMessage = '🎉 恭喜！项目所有任务已完成！';
              break;
          }

          if (memberIds.length > 0) {
            await notifyProjectMilestone(projectId, memberIds, project.title, milestoneMessage, newProgress);
          }
          break; // 只发送第一个达到的节点通知
        }
      }
    } catch (notificationError) {
      console.error('发送项目进度通知失败:', notificationError);
      // 不影响进度更新，只记录错误
    }
  } catch (error) {
    console.error('更新项目进度失败:', error);
    throw error;
  }
}

/**
 * 计算任务完成率统计
 */
export function calculateTaskCompletionRate(tasks: Array<{ status: string }>): {
  total: number;
  completed: number;
  inProgress: number;
  todo: number;
  review: number;
  completionRate: number;
} {
  const total = tasks.length;
  const completed = tasks.filter(task => task.status === 'COMPLETED').length;
  const inProgress = tasks.filter(task => task.status === 'IN_PROGRESS').length;
  const todo = tasks.filter(task => task.status === 'TODO').length;
  const review = tasks.filter(task => task.status === 'REVIEW').length;

  const completionRate = total > 0 ? completed / total : 0;

  return {
    total,
    completed,
    inProgress,
    todo,
    review,
    completionRate,
  };
}
