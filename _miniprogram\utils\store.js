// 全局状态管理
class Store {
  constructor() {
    this.state = {
      user: null,
      token: null,
      unreadCounts: {
        messages: 0,
        notifications: 0,
        tasks: 0
      },
      networkStatus: 'online',
      apiStatus: 'unknown' // unknown, available, unavailable
    };
    
    this.listeners = [];
  }

  // 获取状态
  getState() {
    return { ...this.state };
  }

  // 设置状态
  setState(newState) {
    const oldState = { ...this.state };
    this.state = { ...this.state, ...newState };
    
    // 通知所有监听器
    this.listeners.forEach(listener => {
      listener(this.state, oldState);
    });
  }

  // 添加监听器
  subscribe(listener) {
    this.listeners.push(listener);
    
    // 返回取消订阅函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // 设置用户信息
  setUser(user, token) {
    this.setState({ user, token });
  }

  // 清除用户信息
  clearUser() {
    this.setState({ user: null, token: null });
  }

  // 更新未读数量
  updateUnreadCounts(counts) {
    this.setState({
      unreadCounts: { ...this.state.unreadCounts, ...counts }
    });
  }

  // 设置网络状态
  setNetworkStatus(status) {
    this.setState({ networkStatus: status });
  }

  // 设置API状态
  setApiStatus(status) {
    this.setState({ apiStatus: status });
  }
}

// 创建全局store实例
const store = new Store();

// 导出store和一些便捷方法
module.exports = {
  store,
  
  // 便捷方法
  getUser: () => store.getState().user,
  getToken: () => store.getState().token,
  isLoggedIn: () => !!(store.getState().user && store.getState().token),
  getUnreadCounts: () => store.getState().unreadCounts,
  getNetworkStatus: () => store.getState().networkStatus,
  getApiStatus: () => store.getState().apiStatus,
  
  // 状态更新方法
  setUser: (user, token) => store.setUser(user, token),
  clearUser: () => store.clearUser(),
  updateUnreadCounts: (counts) => store.updateUnreadCounts(counts),
  setNetworkStatus: (status) => store.setNetworkStatus(status),
  setApiStatus: (status) => store.setApiStatus(status),
  
  // 订阅状态变化
  subscribe: (listener) => store.subscribe(listener)
};
