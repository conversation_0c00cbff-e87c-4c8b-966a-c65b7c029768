// API 功能测试脚本
// 在微信开发者工具控制台中运行此脚本来测试API功能

// 测试 buildQueryString 函数
function testBuildQueryString() {
  console.log('=== 测试 buildQueryString 函数 ===');
  
  // 模拟 buildQueryString 函数
  function buildQueryString(params = {}) {
    const queryParts = [];
    
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined && value !== null && value !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }
    
    return queryParts.join('&');
  }
  
  // 测试用例
  const testCases = [
    { input: {}, expected: '' },
    { input: { page: 1 }, expected: 'page=1' },
    { input: { page: 1, limit: 10 }, expected: 'page=1&limit=10' },
    { input: { search: 'test query' }, expected: 'search=test%20query' },
    { input: { page: 1, search: '', limit: 10 }, expected: 'page=1&limit=10' },
    { input: { page: null, limit: 10 }, expected: 'limit=10' }
  ];
  
  testCases.forEach((testCase, index) => {
    const result = buildQueryString(testCase.input);
    const passed = result === testCase.expected;
    console.log(`测试 ${index + 1}: ${passed ? '✅' : '❌'}`);
    console.log(`  输入: ${JSON.stringify(testCase.input)}`);
    console.log(`  期望: "${testCase.expected}"`);
    console.log(`  结果: "${result}"`);
    if (!passed) {
      console.error(`  ❌ 测试失败!`);
    }
  });
}

// 测试API调用
async function testApiCalls() {
  console.log('=== 测试API调用 ===');
  
  const { taskApi, projectApi, fileApi, notificationApi } = require('../utils/api.js');
  
  // 测试任务API
  try {
    console.log('测试任务列表API...');
    const taskResult = await taskApi.getTaskList({ page: 1, status: 'TODO' });
    console.log('✅ 任务API调用成功');
  } catch (error) {
    if (error.message === 'API_UNAVAILABLE') {
      console.log('✅ 任务API正确处理了API不可用情况');
    } else {
      console.error('❌ 任务API调用失败:', error.message);
    }
  }
  
  // 测试项目API
  try {
    console.log('测试项目列表API...');
    const projectResult = await projectApi.getProjectList({ page: 1 });
    console.log('✅ 项目API调用成功');
  } catch (error) {
    if (error.message === 'API_UNAVAILABLE') {
      console.log('✅ 项目API正确处理了API不可用情况');
    } else {
      console.error('❌ 项目API调用失败:', error.message);
    }
  }
  
  // 测试文件API
  try {
    console.log('测试文件列表API...');
    const fileResult = await fileApi.getFileList({ projectId: 1 });
    console.log('✅ 文件API调用成功');
  } catch (error) {
    if (error.message === 'API_UNAVAILABLE') {
      console.log('✅ 文件API正确处理了API不可用情况');
    } else {
      console.error('❌ 文件API调用失败:', error.message);
    }
  }
  
  // 测试通知API
  try {
    console.log('测试通知列表API...');
    const notificationResult = await notificationApi.getNotificationList({ page: 1 });
    console.log('✅ 通知API调用成功');
  } catch (error) {
    if (error.message === 'API_UNAVAILABLE') {
      console.log('✅ 通知API正确处理了API不可用情况');
    } else {
      console.error('❌ 通知API调用失败:', error.message);
    }
  }
}

// 运行所有测试
function runAllTests() {
  console.log('🧪 开始API功能测试...');
  console.log('');
  
  testBuildQueryString();
  console.log('');
  
  testApiCalls().then(() => {
    console.log('');
    console.log('🎉 所有测试完成!');
  }).catch((error) => {
    console.error('❌ 测试过程中发生错误:', error);
  });
}

// 导出测试函数
module.exports = {
  testBuildQueryString,
  testApiCalls,
  runAllTests
};

// 如果在控制台中直接运行，执行所有测试
if (typeof window !== 'undefined') {
  runAllTests();
}
