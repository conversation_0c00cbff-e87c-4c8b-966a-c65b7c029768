import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

// 腾讯会议API签名生成
function generateSignature(secretKey: string, timestamp: string, nonce: string) {
  const stringToSign = `${timestamp}\n${nonce}`;
  return crypto.createHmac('sha256', secretKey).update(stringToSign).digest('hex');
}

// 生成随机字符串
function generateNonce(length: number = 16): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.email) {
      return res.status(401).json({ message: '未授权' });
    }

    // 检查用户权限
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return res.status(403).json({ message: '权限不足' });
    }

    const { appId, secretId, secretKey, sdkId } = req.body;

    if (!appId || !secretId || !secretKey || !sdkId) {
      return res.status(400).json({ message: '请填写完整的配置信息' });
    }

    // 生成签名参数
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const nonce = generateNonce();
    const signature = generateSignature(secretKey, timestamp, nonce);

    // 测试API调用 - 获取用户信息
    const testUrl = 'https://api.meeting.qq.com/v1/users';
    
    const headers = {
      'Content-Type': 'application/json',
      'X-TC-Key': secretId,
      'X-TC-Timestamp': timestamp,
      'X-TC-Nonce': nonce,
      'X-TC-Signature': signature,
      'X-TC-Registered': '1',
      'AppId': appId,
      'SdkId': sdkId,
    };

    try {
      const response = await fetch(testUrl, {
        method: 'GET',
        headers,
      });

      if (response.ok) {
        return res.status(200).json({ 
          message: '连接测试成功！腾讯会议API配置正确。',
          status: 'success'
        });
      } else {
        const errorData = await response.text();
        console.error('腾讯会议API测试失败:', response.status, errorData);
        
        let errorMessage = '连接测试失败';
        if (response.status === 401) {
          errorMessage = '认证失败，请检查Secret ID和Secret Key是否正确';
        } else if (response.status === 403) {
          errorMessage = '权限不足，请检查账号权限或App ID配置';
        } else if (response.status === 400) {
          errorMessage = '请求参数错误，请检查配置信息';
        }

        return res.status(400).json({ 
          message: errorMessage,
          details: errorData,
          status: 'error'
        });
      }
    } catch (fetchError) {
      console.error('网络请求失败:', fetchError);
      return res.status(500).json({ 
        message: '网络连接失败，请检查网络连接或API地址',
        status: 'error'
      });
    }

  } catch (error) {
    console.error('测试连接API错误:', error);
    return res.status(500).json({ message: '服务器错误' });
  } finally {
    await prisma.$disconnect();
  }
}
