import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.email) {
      return res.status(401).json({ message: '未授权' });
    }

    // 检查用户权限（只有管理员和项目负责人可以发布公告）
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!currentUser || (currentUser.role !== 'ADMIN' && currentUser.role !== 'LEADER')) {
      return res.status(403).json({ message: '权限不足' });
    }

    const { title, content, priority } = req.body;

    if (!title || !content) {
      return res.status(400).json({ message: '标题和内容不能为空' });
    }

    if (title.length > 100) {
      return res.status(400).json({ message: '标题不能超过100个字符' });
    }

    if (content.length > 1000) {
      return res.status(400).json({ message: '内容不能超过1000个字符' });
    }

    const validPriorities = ['LOW', 'NORMAL', 'HIGH', 'URGENT'];
    if (!validPriorities.includes(priority)) {
      return res.status(400).json({ message: '无效的优先级' });
    }

    // 获取所有已审核的用户
    const allUsers = await prisma.user.findMany({
      where: {
        status: 'APPROVED',
        id: {
          not: currentUser.id, // 排除发送者自己
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (allUsers.length === 0) {
      return res.status(400).json({ message: '没有可接收公告的用户' });
    }

    // 批量创建通知
    const notifications = allUsers.map(user => ({
      userId: user.id,
      type: 'TEAM_ANNOUNCEMENT',
      title: `团队公告: ${title}`,
      message: content,
      relatedType: 'announcement',
      relatedId: currentUser.id,
    }));

    await prisma.notification.createMany({
      data: notifications,
    });

    // 记录公告发送日志（可选，用于统计）
    const announcementLog = await prisma.notification.create({
      data: {
        userId: currentUser.id,
        type: 'ANNOUNCEMENT_SENT',
        title: '团队公告已发送',
        message: `向 ${allUsers.length} 位成员发送了公告: ${title}`,
        relatedType: 'announcement',
        relatedId: currentUser.id,
        read: true, // 发送者的记录标记为已读
      },
    });

    res.status(200).json({
      message: '团队公告发送成功',
      data: {
        title,
        content,
        priority,
        recipientCount: allUsers.length,
        sentAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('发送团队公告失败:', error);
    res.status(500).json({ message: '服务器错误' });
  } finally {
    await prisma.$disconnect();
  }
}
