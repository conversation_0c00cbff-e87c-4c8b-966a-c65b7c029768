// 任务状态相关工具函数

// 获取任务状态显示文本
export const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'TODO':
      return '待处理';
    case 'IN_PROGRESS':
      return '进行中';
    case 'REVIEW':
      return '审核中';
    case 'COMPLETED':
      return '已完成';
    case 'DONE':
      return '已完成';
    default:
      return status;
  }
};

// 获取任务状态标签样式
export const getTaskStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'TODO':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    case 'IN_PROGRESS':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'REVIEW':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    case 'COMPLETED':
    case 'DONE':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  }
};

// 获取项目状态显示文本
export const getProjectStatusText = (status: string) => {
  switch (status) {
    case 'PLANNING':
      return '规划中';
    case 'ACTIVE':
      return '进行中';
    case 'COMPLETED':
      return '已完成';
    case 'ARCHIVED':
      return '已归档';
    default:
      return status;
  }
};

// 获取项目状态标签样式
export const getProjectStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'PLANNING':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'ACTIVE':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'COMPLETED':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    case 'ARCHIVED':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  }
};

// 获取任务优先级显示文本
export const getTaskPriorityText = (priority: string) => {
  switch (priority) {
    case 'LOW':
      return '低';
    case 'MEDIUM':
      return '中';
    case 'HIGH':
      return '高';
    case 'URGENT':
      return '紧急';
    default:
      return priority;
  }
};

// 获取任务优先级标签样式
export const getTaskPriorityBadgeClass = (priority: string) => {
  switch (priority) {
    case 'LOW':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'MEDIUM':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    case 'HIGH':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
    case 'URGENT':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  }
};

// 获取用户角色显示文本
export const getUserRoleText = (role: string) => {
  switch (role) {
    case 'ADMIN':
      return '管理员';
    case 'LEADER':
      return '项目负责人';
    case 'MEMBER':
      return '项目成员';
    case 'GUEST':
      return '访客';
    default:
      return role;
  }
};

// 获取用户角色标签样式
export const getUserRoleBadgeClass = (role: string) => {
  switch (role) {
    case 'ADMIN':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    case 'LEADER':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    case 'MEMBER':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'GUEST':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  }
};
