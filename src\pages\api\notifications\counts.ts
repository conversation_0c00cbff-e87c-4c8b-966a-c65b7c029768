import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    // 验证用户身份
    const currentUserId = await getCurrentUserId(req, res);
    if (!currentUserId) {
      return res.status(401).json({ message: '用户未认证' });
    }

    // 1. 获取聊天未读消息数
    const userChats = await prisma.chat.findMany({
      where: {
        participants: {
          some: {
            id: currentUserId,
          },
        },
      },
      select: {
        id: true,
      },
    });

    const chatIds = userChats.map(chat => chat.id);

    const unreadChats = await prisma.chatMessage.count({
      where: {
        chatId: {
          in: chatIds,
        },
        senderId: {
          not: currentUserId, // 不包括自己发送的消息
        },
        readBy: {
          none: {
            userId: currentUserId, // 用户未读的消息
          },
        },
      },
    });

    // 2. 获取系统通知未读数
    const systemChat = await prisma.chat.findFirst({
      where: {
        type: 'SYSTEM',
        participants: {
          some: {
            id: currentUserId,
          },
        },
      },
    });

    let unreadNotifications = 0;
    if (systemChat) {
      unreadNotifications = await prisma.chatMessage.count({
        where: {
          chatId: systemChat.id,
          isSystem: true,
          isRead: false,
        },
      });
    }

    // 3. 获取任务相关通知（如果有Notification表）
    let unreadTasks = 0;
    try {
      unreadTasks = await prisma.notification.count({
        where: {
          userId: currentUserId,
          read: false,
          type: {
            in: ['TASK_ASSIGNED', 'TASK_UPDATED', 'TASK_COMPLETED']
          }
        },
      });
    } catch (error) {
      // Notification表可能不存在，忽略错误
    }

    // 4. 获取项目相关通知
    let unreadProjectMessages = 0;
    try {
      unreadProjectMessages = await prisma.notification.count({
        where: {
          userId: currentUserId,
          read: false,
          type: {
            in: ['PROJECT_UPDATED', 'FILE_UPLOADED', 'PROJECT_COMPLETED']
          }
        },
      });
    } catch (error) {
      // Notification表可能不存在，忽略错误
    }

    // 5. 计算总未读数
    const total = unreadChats + unreadNotifications + unreadTasks + unreadProjectMessages;

    return res.status(200).json({
      unreadChats,
      unreadNotifications,
      unreadTasks,
      unreadProjectMessages,
      total,
    });

  } catch (error) {
    console.error('获取通知数量失败:', error);
    return res.status(500).json({ message: '服务器内部错误' });
  }
}
