import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';

export default function EditTask() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { id } = router.query;
  const [task, setTask] = useState(null);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [selectedAssignees, setSelectedAssignees] = useState<string[]>([]);

  // 表单数据
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    dueDate: '',
    status: 'TODO',
    priority: 'MEDIUM',
    assigneeId: '',
  });

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取任务详情
  useEffect(() => {
    if (id && status === 'authenticated') {
      fetchTask();
    }
  }, [id, status]);

  const fetchTask = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/tasks/${id}`);

      if (!response.ok) {
        throw new Error('获取任务详情失败');
      }

      const taskData = await response.json();
      setTask(taskData);

      // 获取项目信息
      if (taskData.project?.id) {
        const projectResponse = await fetch(`/api/projects/${taskData.project.id}`);
        if (projectResponse.ok) {
          const projectData = await projectResponse.json();
          setProject(projectData);
        }
      }

      // 设置表单数据
      setFormData({
        title: taskData.title || '',
        description: taskData.description || '',
        dueDate: taskData.dueDate ? taskData.dueDate.split('T')[0] : '',
        status: taskData.status || 'TODO',
        priority: taskData.priority || 'MEDIUM',
        assigneeId: taskData.assignee?.id || '',
      });

      // 设置已选择的分配者
      if (taskData.assignees && taskData.assignees.length > 0) {
        setSelectedAssignees(taskData.assignees.map(assignee => assignee.id));
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      setError('获取任务详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');

    try {
      const response = await fetch(`/api/tasks/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          assigneeIds: selectedAssignees,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '更新任务失败');
      }

      // 返回任务详情页
      router.push(`/tasks/${id}`);
    } catch (error) {
      console.error('更新任务失败:', error);
      setError(error.message || '更新任务失败，请稍后再试');
    } finally {
      setSaving(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 加载中
  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 错误状态
  if (error && !task) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="mt-4">
          <Link href="/tasks" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回任务列表
          </Link>
        </div>
      </div>
    );
  }

  // 任务不存在
  if (!task) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">任务不存在或您没有权限编辑</span>
        </div>
        <div className="mt-4">
          <Link href="/tasks" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回任务列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-6">
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-2">
          <Link href="/tasks" className="hover:text-gray-700 dark:hover:text-gray-300">
            任务
          </Link>
          <span>/</span>
          <Link href={`/tasks/${id}`} className="hover:text-gray-700 dark:hover:text-gray-300">
            {task.title}
          </Link>
          <span>/</span>
          <span>编辑</span>
        </div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          编辑任务
        </h1>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-6 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* 编辑表单 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 任务标题 */}
          <div>
            <label htmlFor="title" className="form-label">
              任务标题 *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="form-input"
              required
            />
          </div>

          {/* 任务描述 */}
          <div>
            <label htmlFor="description" className="form-label">
              任务描述
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="form-input"
              placeholder="详细描述任务内容..."
            />
          </div>

          {/* 截止日期、状态、优先级 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label htmlFor="dueDate" className="form-label">
                截止日期
              </label>
              <input
                type="date"
                id="dueDate"
                name="dueDate"
                value={formData.dueDate}
                onChange={handleInputChange}
                className="form-input"
              />
            </div>

            <div>
              <label htmlFor="status" className="form-label">
                任务状态
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="form-input"
              >
                <option value="TODO">待处理</option>
                <option value="IN_PROGRESS">进行中</option>
                <option value="REVIEW">审核中</option>
                <option value="COMPLETED">已完成</option>
              </select>
            </div>

            <div>
              <label htmlFor="priority" className="form-label">
                优先级
              </label>
              <select
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                className="form-input"
              >
                <option value="LOW">低</option>
                <option value="MEDIUM">中</option>
                <option value="HIGH">高</option>
                <option value="URGENT">紧急</option>
              </select>
            </div>
          </div>

          {/* 负责人分配 */}
          {project && (
            <>
              {/* 主要负责人 */}
              <div>
                <label htmlFor="assigneeId" className="form-label">
                  主要负责人
                </label>
                <select
                  id="assigneeId"
                  name="assigneeId"
                  value={formData.assigneeId}
                  onChange={handleInputChange}
                  className="form-input"
                >
                  <option value="">未分配</option>
                  <option value={project.owner.id}>{project.owner.name} (项目负责人)</option>
                  {project.members.map(member => (
                    <option key={member.id} value={member.id}>
                      {member.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* 协作成员 */}
              <div>
                <label className="form-label">
                  协作成员 <span className="text-sm text-gray-500">(可选择多人)</span>
                </label>
                <div className="border border-gray-300 dark:border-gray-600 rounded-md p-3 bg-white dark:bg-gray-700">
                  <div className="space-y-2">
                    {/* 项目负责人 */}
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                        checked={selectedAssignees.includes(project.owner.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedAssignees([...selectedAssignees, project.owner.id]);
                          } else {
                            setSelectedAssignees(selectedAssignees.filter(id => id !== project.owner.id));
                          }
                        }}
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        {project.owner.name} <span className="text-xs text-gray-500">(项目负责人)</span>
                      </span>
                    </label>

                    {/* 项目成员 */}
                    {project.members.map(member => (
                      <label key={member.id} className="flex items-center">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                          checked={selectedAssignees.includes(member.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedAssignees([...selectedAssignees, member.id]);
                            } else {
                              setSelectedAssignees(selectedAssignees.filter(id => id !== member.id));
                            }
                          }}
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          {member.name}
                        </span>
                      </label>
                    ))}
                  </div>

                  {selectedAssignees.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        已选择 {selectedAssignees.length} 位协作成员
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-4">
            <Link href={`/tasks/${id}`} className="btn btn-secondary">
              取消
            </Link>
            <button
              type="submit"
              disabled={saving}
              className="btn btn-primary"
            >
              {saving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  保存中...
                </>
              ) : (
                '保存更改'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
