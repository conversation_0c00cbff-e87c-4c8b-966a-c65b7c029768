import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { hashPassword } from '@/lib/auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '只允许POST请求' });
  }

  const { name, email, password, inviteCode } = req.body;

  // 验证请求数据
  if (!name || !email || !password || !inviteCode) {
    return res.status(400).json({ message: '请提供所有必填字段，包括邀请码' });
  }

  if (password.length < 6) {
    return res.status(400).json({ message: '密码长度至少为6个字符' });
  }

  try {
    // 验证邀请码
    const validInviteCode = await prisma.inviteCode.findUnique({
      where: { code: inviteCode.toUpperCase() },
    });

    if (!validInviteCode) {
      return res.status(400).json({ message: '邀请码不存在' });
    }

    if (!validInviteCode.isActive) {
      return res.status(400).json({ message: '邀请码已被禁用' });
    }

    if (validInviteCode.expiresAt && new Date() > validInviteCode.expiresAt) {
      return res.status(400).json({ message: '邀请码已过期' });
    }

    if (validInviteCode.usedCount >= validInviteCode.maxUses) {
      return res.status(400).json({ message: '邀请码使用次数已达上限，无法继续使用' });
    }

    // 检查邮箱是否已被注册
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return res.status(400).json({ message: '该邮箱已被注册' });
    }

    // 创建新用户并更新邀请码使用次数
    const hashedPassword = await hashPassword(password);

    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        // 默认为普通成员角色
        role: 'MEMBER',
        // 新用户默认为待审核状态
        status: 'PENDING',
        // 关联使用的邀请码
        usedInviteCodeId: validInviteCode.id,
      },
    });

    // 更新邀请码使用次数
    await prisma.inviteCode.update({
      where: { id: validInviteCode.id },
      data: {
        usedCount: validInviteCode.usedCount + 1,
      },
    });

    // 不返回密码
    const { password: _, ...userWithoutPassword } = user;

    return res.status(201).json({
      message: '注册成功！您的账号正在等待管理员审核，审核通过后即可登录。',
      user: userWithoutPassword,
    });
  } catch (error) {
    console.error('注册失败:', error);
    return res.status(500).json({ message: '服务器错误，请稍后再试' });
  }
}
