# 图片资源说明

这个目录包含小程序所需的图片资源。

## 📋 当前状态

### ✅ 已有图片文件
- `logo.svg` - 应用Logo (SVG格式，用于登录页面)
- `default-avatar.svg` - 默认头像 (SVG格式)

### ⚠️ 需要添加的PNG图标 (TabBar专用)

微信小程序TabBar只支持PNG/JPG格式，需要以下图标：

#### TabBar 图标 (81px × 81px, PNG格式)
- `home.png` - 首页图标 (灰色 #9CA3AF)
- `home-active.png` - 首页激活图标 (蓝色 #3B82F6)
- `project.png` - 项目图标 (灰色 #9CA3AF)
- `project-active.png` - 项目激活图标 (蓝色 #3B82F6)
- `task.png` - 任务图标 (灰色 #9CA3AF)
- `task-active.png` - 任务激活图标 (蓝色 #3B82F6)
- `chat.png` - 聊天图标 (灰色 #9CA3AF)
- `chat-active.png` - 聊天激活图标 (蓝色 #3B82F6)
- `profile.png` - 个人图标 (灰色 #9CA3AF)
- `profile-active.png` - 个人激活图标 (蓝色 #3B82F6)

## 🎨 设计规范

### 颜色规范 (与网页版一致)
- **未激活状态**: #9CA3AF (gray-400)
- **激活状态**: #3B82F6 (blue-600)
- **背景**: 透明

### 尺寸规范
- **TabBar图标**: 81px × 81px
- **Logo**: 120px × 120px
- **头像**: 120px × 120px

## 🔧 临时解决方案

在PNG图标准备好之前，可以：

1. **暂时移除TabBar图标** - 只显示文字
2. **使用在线图标生成器** - 如 Iconfont、Feather Icons
3. **使用设计工具** - Figma、Sketch 导出PNG

## 📝 图标设计建议

### 首页图标 (home)
- 简单的房子轮廓
- 2px 描边，无填充

### 项目图标 (project)
- 文件夹图标
- 简洁的线条设计

### 任务图标 (task)
- 清单/复选框图标
- 包含勾选元素

### 聊天图标 (chat)
- 对话气泡
- 圆角矩形设计

### 个人图标 (profile)
- 用户头像轮廓
- 头部+肩膀简化设计

在正式发布前，建议：
1. 设计专业的图标和Logo
2. 使用矢量图标确保清晰度
3. 遵循微信小程序设计规范
4. 进行多设备测试
