/* 聊天列表页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 20rpx 60rpx 20rpx 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f9fafb;
  box-sizing: border-box;
}

.search-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  font-size: 28rpx;
  color: #6b7280;
}

/* 聊天列表 */
.chat-list {
  margin-bottom: 120rpx;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.chat-item:active {
  transform: scale(0.98);
}

.chat-avatar {
  position: relative;
  margin-right: 24rpx;
}

.chat-avatar image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  object-fit: cover;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ef4444;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  font-weight: 600;
}

.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.chat-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-time {
  font-size: 24rpx;
  color: #9ca3af;
  flex-shrink: 0;
  margin-left: 20rpx;
}

.chat-last-message {
  display: flex;
  align-items: center;
}

.message-text {
  font-size: 26rpx;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.message-media {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #6b7280;
}

.media-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.chat-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  margin-left: 20rpx;
}

.group-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

.group-icon {
  font-size: 24rpx;
  color: #6b7280;
}

.online-indicator {
  width: 16rpx;
  height: 16rpx;
  background: #10b981;
  border-radius: 50%;
  border: 2rpx solid white;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: #3b82f6;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
  z-index: 100;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: 300;
}

/* 加载和空状态 */
.loading {
  text-align: center;
  padding: 60rpx;
  color: #6b7280;
  font-size: 28rpx;
}

.empty {
  text-align: center;
  padding: 120rpx 60rpx;
  color: #9ca3af;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #d1d5db;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 15rpx;
  }
  
  .chat-item {
    padding: 25rpx;
  }
  
  .chat-avatar image {
    width: 80rpx;
    height: 80rpx;
  }
  
  .chat-name {
    font-size: 30rpx;
  }
  
  .fab {
    width: 100rpx;
    height: 100rpx;
    bottom: 100rpx;
    right: 30rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}
