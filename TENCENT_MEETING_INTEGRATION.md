# 腾讯会议集成功能实现 🎥

## 🚀 功能概述

LabSync现在支持腾讯会议的完整集成，实现了从配置到一键创建会议的完整流程，为团队协作提供了专业的视频会议解决方案。

## ✨ 主要功能

### 🔧 **管理员配置功能**
- **会议配置页面**: `/admin/meeting-config`
- **API密钥管理**: App ID、Secret ID、Secret Key、SDK ID
- **连接测试**: 验证配置是否正确
- **启用控制**: 可以启用/禁用会议功能

### 🎯 **一键创建会议**
- **快速创建**: 填写主题、时间即可创建
- **成员邀请**: 可选择团队成员参与会议
- **自动通知**: 创建成功后自动发送通知
- **会议信息**: 自动生成会议号和加入链接

### 🔐 **权限控制**
- **配置权限**: 只有管理员可以配置腾讯会议
- **创建权限**: 管理员和项目负责人可以创建会议
- **安全存储**: 敏感信息加密存储

## 🏗️ 技术架构

### 📊 **数据库设计**

#### MeetingConfig 表
```sql
model MeetingConfig {
  id        String   @id @default(cuid())
  appId     String   // 腾讯会议App ID
  secretId  String   // Secret ID
  secretKey String   // Secret Key (加密存储)
  sdkId     String   // SDK ID
  isEnabled Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

#### Meeting 表
```sql
model Meeting {
  id          String   @id @default(cuid())
  meetingId   String   @unique // 腾讯会议ID
  meetingCode String   // 会议号
  subject     String   // 会议主题
  joinUrl     String   // 加入链接
  password    String?  // 会议密码
  startTime   DateTime // 开始时间
  endTime     DateTime // 结束时间
  status      String   @default("SCHEDULED")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  creator     User     @relation("MeetingCreator", fields: [creatorId], references: [id])
  creatorId   String
}
```

### 🔌 **API端点**

#### 配置管理
- **GET** `/api/admin/meeting-config` - 获取配置
- **POST** `/api/admin/meeting-config` - 保存配置
- **POST** `/api/admin/meeting-config/test` - 测试连接

#### 会议管理
- **POST** `/api/meeting/create` - 创建会议

### 🔒 **安全机制**

#### API签名验证
```javascript
function generateSignature(secretKey, timestamp, nonce) {
  const stringToSign = `${timestamp}\n${nonce}`;
  return crypto.createHmac('sha256', secretKey).update(stringToSign).digest('hex');
}
```

#### 请求头配置
```javascript
const headers = {
  'Content-Type': 'application/json',
  'X-TC-Key': secretId,
  'X-TC-Timestamp': timestamp,
  'X-TC-Nonce': nonce,
  'X-TC-Signature': signature,
  'X-TC-Registered': '1',
  'AppId': appId,
  'SdkId': sdkId,
};
```

## 🎨 **用户界面**

### 📱 **会议配置界面**
- **现代化设计**: 清晰的表单布局和视觉层次
- **实时验证**: 表单字段的即时验证反馈
- **安全显示**: 敏感信息的安全显示方式
- **操作反馈**: 保存和测试的状态反馈

### 🚀 **一键开会界面**
- **平台选择**: 支持多种会议平台选择
- **快速创建**: 简洁的会议创建表单
- **成员选择**: 直观的团队成员邀请界面
- **时间控制**: 智能的时间选择和验证

### 🎯 **团队页面集成**
- **视频会议按钮**: 集成在快速联系功能中
- **一键创建**: 优先显示的创建会议选项
- **平台选择**: 腾讯会议、钉钉等多平台支持

## 📋 **使用流程**

### 🔧 **管理员配置流程**
1. **访问配置页面**: 管理员登录后访问 `/admin/meeting-config`
2. **填写API信息**: 输入从腾讯会议开放平台获取的API密钥
3. **测试连接**: 点击"测试连接"验证配置是否正确
4. **保存配置**: 确认无误后保存并启用功能

### 🎥 **创建会议流程**
1. **访问团队页面**: 进入 `/team` 页面
2. **点击视频会议**: 选择"一键创建会议"
3. **填写会议信息**: 输入主题、开始时间、结束时间
4. **选择参与者**: 可选择团队成员参与
5. **创建会议**: 点击创建，系统自动生成会议信息
6. **获取会议信息**: 显示会议号、加入链接等信息

### 📢 **通知流程**
1. **会议创建成功**: 系统自动记录会议信息
2. **发送通知**: 向被邀请的成员发送会议邀请通知
3. **通知内容**: 包含会议主题、时间、加入方式等信息

## 🔗 **腾讯会议API集成**

### 📚 **API文档参考**
- **官方文档**: https://cloud.tencent.com/document/product/1095
- **开放平台**: https://meeting.tencent.com/open-api.html
- **SDK下载**: https://meeting.tencent.com/sdk.html

### 🔑 **获取API密钥步骤**
1. **注册开发者账号**: 访问腾讯会议开放平台
2. **创建应用**: 在控制台创建新的应用
3. **获取密钥**: 获取App ID、Secret ID、Secret Key、SDK ID
4. **配置权限**: 确保账号具有创建会议的权限

### 📊 **API调用示例**
```javascript
// 创建会议请求
const meetingData = {
  userid: currentUser.id,
  instanceid: 1, // PC端
  subject: "团队周会",
  type: 0, // 预约会议
  start_time: "1640995200", // Unix时间戳
  end_time: "1640998800",
  settings: {
    mute_enable_type_join: 2,
    allow_unmute_self: true,
    allow_in_before_host: true,
    auto_in_waiting_room: false,
    only_user_join_type: 1,
  },
};
```

## 🎯 **功能特色**

### ✨ **智能化特性**
- **时间验证**: 自动验证会议时间的合理性
- **冲突检测**: 避免时间冲突的会议安排
- **自动提醒**: 会议前的自动提醒功能
- **状态同步**: 会议状态的实时同步

### 🔒 **安全特性**
- **权限控制**: 基于角色的访问控制
- **数据加密**: 敏感信息的安全存储
- **签名验证**: API请求的安全签名
- **审计日志**: 完整的操作记录

### 🎨 **用户体验**
- **一键操作**: 简化的会议创建流程
- **实时反馈**: 操作状态的即时反馈
- **错误处理**: 友好的错误提示和处理
- **响应式设计**: 适配各种设备屏幕

## 🚀 **部署说明**

### 📋 **环境要求**
- Node.js 18.x+
- PostgreSQL 14+
- 腾讯会议企业版账号
- 有效的API密钥

### 🔧 **配置步骤**
1. **数据库迁移**: 运行 `npx prisma db push`
2. **管理员配置**: 访问会议配置页面
3. **API密钥配置**: 输入腾讯会议API信息
4. **功能测试**: 测试会议创建功能

### ⚠️ **注意事项**
- 确保服务器网络可以访问腾讯会议API
- API密钥需要妥善保管，避免泄露
- 建议使用企业版账号以获得更多功能
- 定期检查API配额和使用情况

## 🎉 **总结**

腾讯会议集成功能为LabSync带来了：

### 🏆 **核心价值**
- **无缝集成**: 与现有系统完美融合
- **一键操作**: 极简的会议创建体验
- **专业品质**: 企业级的会议解决方案
- **安全可靠**: 完善的安全保障机制

### 🚀 **技术优势**
- **标准API**: 基于腾讯会议官方API
- **现代架构**: 采用最新的技术栈
- **扩展性强**: 易于扩展和维护
- **性能优秀**: 高效的数据处理和响应

### 📈 **业务价值**
- **提升效率**: 减少会议安排的时间成本
- **增强协作**: 促进团队沟通和协作
- **统一管理**: 集中化的会议管理平台
- **数据洞察**: 会议数据的统计和分析

**LabSync现在具备了完整的视频会议解决方案，为实验室团队提供了专业、便捷、安全的会议服务！** 🎥✨
