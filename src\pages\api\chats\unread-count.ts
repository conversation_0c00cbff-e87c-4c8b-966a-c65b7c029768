import { NextApiRequest, NextApiResponse } from 'next';
import { getCurrentUserId } from '@/lib/auth';
import prisma from '@/lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    // 验证用户身份
    const currentUserId = await getCurrentUserId(req, res);
    if (!currentUserId) {
      return res.status(401).json({ message: '用户未认证' });
    }

    // 获取用户参与的所有聊天
    const userChats = await prisma.chat.findMany({
      where: {
        participants: {
          some: {
            id: currentUserId,
          },
        },
      },
      select: {
        id: true,
      },
    });

    const chatIds = userChats.map(chat => chat.id);

    // 使用MessageRead表计算真正的未读消息数量
    const totalUnreadCount = await prisma.chatMessage.count({
      where: {
        chatId: {
          in: chatIds,
        },
        senderId: {
          not: currentUserId, // 不包括自己发送的消息
        },
        readBy: {
          none: {
            userId: currentUserId, // 用户未读的消息
          },
        },
      },
    });

    return res.status(200).json({
      totalUnreadCount
    });
  } catch (error) {
    console.error('获取未读消息数失败:', error);
    return res.status(500).json({ message: '服务器内部错误' });
  }
}
