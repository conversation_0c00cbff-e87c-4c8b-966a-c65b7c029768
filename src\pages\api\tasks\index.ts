import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId, isAdmin } from '@/lib/auth';
import { isProjectMember } from '@/lib/permissions';
import { notifyTaskAssigned } from '@/lib/systemNotifications';
import { updateProjectProgress } from '@/lib/project-utils';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const userId = await getCurrentUserId(req, res);
  const isUserAdmin = await isAdmin(req, res);

  // 验证用户ID
  if (!userId) {
    return res.status(401).json({ message: '用户未认证' });
  }

  // 处理GET请求 - 获取任务列表
  if (req.method === 'GET') {
    try {
      let tasks;
      const { projectId, myTasks } = req.query;

      // 如果指定了项目ID，则检查用户是否有权限访问该项目
      if (projectId) {
        const canAccessProject = await isProjectMember(req, res, projectId as string);
        if (!canAccessProject) {
          return res.status(403).json({ message: '没有权限访问该项目的任务' });
        }

        // 获取指定项目的任务
        tasks = await prisma.task.findMany({
          where: {
            projectId: projectId as string,
          },
          include: {
            project: {
              select: {
                id: true,
                title: true,
              },
            },
            assignee: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            assignees: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            updatedAt: 'desc',
          },
        });
      } else if (myTasks === 'true') {
        // 只获取分配给当前用户的任务
        tasks = await prisma.task.findMany({
          where: {
            assigneeId: userId,
          },
          include: {
            project: {
              select: {
                id: true,
                title: true,
              },
            },
            assignee: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            assignees: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            updatedAt: 'desc',
          },
        });
      } else {
        // 没有指定项目ID，则获取用户有权限访问的所有任务
        if (isUserAdmin) {
          // 管理员可以查看所有任务
          tasks = await prisma.task.findMany({
            include: {
              project: {
                select: {
                  id: true,
                  title: true,
                },
              },
              assignee: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              assignees: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              updatedAt: 'desc',
            },
          });
        } else {
          // 普通用户只能查看分配给自己的任务（主要负责人或协作成员）
          tasks = await prisma.task.findMany({
            where: {
              OR: [
                { assigneeId: userId },
                { assignees: { some: { id: userId } } },
              ],
            },
            include: {
              project: {
                select: {
                  id: true,
                  title: true,
                },
              },
              assignee: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              assignees: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              updatedAt: 'desc',
            },
          });
        }
      }

      return res.status(200).json(tasks);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理POST请求 - 创建新任务
  if (req.method === 'POST') {
    const { title, description, dueDate, status, priority, projectId, assigneeId, assigneeIds } = req.body;

    // 验证请求数据
    if (!title || !projectId) {
      return res.status(400).json({ message: '请提供任务标题和项目ID' });
    }

    // 检查用户是否有权限访问该项目
    const canAccessProject = await isProjectMember(req, res, projectId);
    if (!canAccessProject) {
      return res.status(403).json({ message: '没有权限在该项目中创建任务' });
    }

    try {
      // 创建新任务
      const task = await prisma.task.create({
        data: {
          title,
          description,
          dueDate: dueDate ? new Date(dueDate) : null,
          status: status || 'TODO',
          priority: priority || 'MEDIUM',
          project: {
            connect: { id: projectId },
          },
          ...(assigneeId
            ? {
                assignee: {
                  connect: { id: assigneeId },
                },
              }
            : {}),
          ...(assigneeIds && assigneeIds.length > 0
            ? {
                assignees: {
                  connect: assigneeIds.map((id: string) => ({ id })),
                },
              }
            : {}),
        },
        include: {
          project: {
            select: {
              id: true,
              title: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          assignees: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // 更新项目进度
      await updateProjectProgress(projectId);

      // 发送任务分配通知
      try {
        // 获取当前用户信息（任务创建者）
        const currentUser = await prisma.user.findUnique({
          where: { id: userId! },
          select: { name: true },
        });

        if (currentUser) {
          // 通知主要负责人
          if (assigneeId && task.assignee && assigneeId !== userId) {
            await notifyTaskAssigned(
              task.id,
              assigneeId,
              currentUser.name,
              task.title,
              task.project.title
            );
          }

          // 通知所有协作成员
          if (assigneeIds && assigneeIds.length > 0) {
            for (const collaboratorId of assigneeIds) {
              if (collaboratorId !== userId && collaboratorId !== assigneeId) {
                await notifyTaskAssigned(
                  task.id,
                  collaboratorId,
                  currentUser.name,
                  task.title,
                  task.project.title
                );
              }
            }
          }
        }
      } catch (notificationError) {
        console.error('发送任务分配通知失败:', notificationError);
        // 不影响任务创建，只记录错误
      }

      return res.status(201).json(task);
    } catch (error) {
      console.error('创建任务失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}


