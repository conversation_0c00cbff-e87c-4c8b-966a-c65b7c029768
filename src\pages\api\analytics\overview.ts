import type { NextApiRequest, NextApiResponse } from 'next';
import { AnalyticsService, AnalyticsTimeRange } from '@/lib/analytics';
import { withPermissions } from '@/lib/apiMiddleware';
import { Permission } from '@/lib/permissions';
import { sendSuccess, sendError, handleApiError } from '@/lib/apiMiddleware';
import { getCurrentUserId, isAdmin } from '@/lib/auth';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
  }

  try {
    const userId = await getCurrentUserId(req, res);
    const isUserAdmin = await isAdmin(req, res);
    
    const {
      timeRange = AnalyticsTimeRange.LAST_30_DAYS,
      startDate,
      endDate,
      scope = 'personal' // 'personal' | 'global'
    } = req.query;

    // 检查权限：只有管理员可以查看全局分析
    if (scope === 'global' && !isUserAdmin) {
      return sendError(res, '权限不足，无法查看全局分析数据', 'FORBIDDEN', 403);
    }

    // 解析自定义日期范围
    let customStart: Date | undefined;
    let customEnd: Date | undefined;
    
    if (timeRange === AnalyticsTimeRange.CUSTOM) {
      if (startDate && endDate) {
        customStart = new Date(startDate as string);
        customEnd = new Date(endDate as string);
        
        if (isNaN(customStart.getTime()) || isNaN(customEnd.getTime())) {
          return sendError(res, '无效的日期格式', 'INVALID_DATE_FORMAT', 400);
        }
        
        if (customStart >= customEnd) {
          return sendError(res, '开始日期必须早于结束日期', 'INVALID_DATE_RANGE', 400);
        }
      } else {
        return sendError(res, '自定义时间范围需要提供开始和结束日期', 'MISSING_DATE_RANGE', 400);
      }
    }

    // 获取分析数据
    const analyticsData = await AnalyticsService.getAnalyticsData(
      timeRange as AnalyticsTimeRange,
      scope === 'personal' ? userId! : undefined,
      customStart,
      customEnd
    );

    return sendSuccess(res, analyticsData, '分析数据获取成功');
  } catch (error) {
    console.error('获取分析数据失败:', error);
    return handleApiError(error, req, res);
  }
}

// 导出带权限检查的处理器
export default withPermissions([Permission.PROJECT_READ, Permission.SYSTEM_ADMIN], handler);
