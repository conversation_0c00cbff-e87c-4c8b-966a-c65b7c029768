// 清理重复聊天记录的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanupDuplicateChats() {
  try {
    console.log('开始清理重复聊天记录...');

    // 获取所有私聊
    const allChats = await prisma.chat.findMany({
      where: {
        type: 'PRIVATE',
      },
      include: {
        participants: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            messages: true,
          },
        },
      },
    });

    console.log(`找到 ${allChats.length} 个私聊记录`);

    // 按参与者组合分组
    const chatGroups = new Map();

    for (const chat of allChats) {
      if (chat.participants.length === 2) {
        // 创建参与者ID的排序键
        const participantIds = chat.participants.map(p => p.id).sort();
        const key = participantIds.join('-');

        if (!chatGroups.has(key)) {
          chatGroups.set(key, []);
        }
        chatGroups.get(key).push(chat);
      }
    }

    console.log(`发现 ${chatGroups.size} 个不同的聊天组合`);

    let duplicatesFound = 0;
    let duplicatesRemoved = 0;

    // 检查每个组合是否有重复
    for (const [key, chats] of chatGroups) {
      if (chats.length > 1) {
        duplicatesFound += chats.length - 1;
        console.log(`\n发现重复聊天组合: ${key}`);
        console.log(`参与者: ${chats[0].participants.map(p => p.name).join(', ')}`);
        console.log(`重复数量: ${chats.length}`);

        // 按消息数量和创建时间排序，保留最有价值的聊天
        chats.sort((a, b) => {
          // 优先保留有消息的聊天
          if (a._count.messages !== b._count.messages) {
            return b._count.messages - a._count.messages;
          }
          // 其次保留较早创建的聊天
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });

        const keepChat = chats[0];
        const duplicateChats = chats.slice(1);

        console.log(`保留聊天: ${keepChat.id} (消息数: ${keepChat._count.messages})`);

        // 删除重复的聊天
        for (const duplicateChat of duplicateChats) {
          console.log(`删除重复聊天: ${duplicateChat.id} (消息数: ${duplicateChat._count.messages})`);
          
          try {
            await prisma.chat.delete({
              where: { id: duplicateChat.id },
            });
            duplicatesRemoved++;
          } catch (error) {
            console.error(`删除聊天 ${duplicateChat.id} 失败:`, error.message);
          }
        }
      }
    }

    console.log(`\n清理完成！`);
    console.log(`发现重复聊天: ${duplicatesFound} 个`);
    console.log(`成功删除: ${duplicatesRemoved} 个`);

  } catch (error) {
    console.error('清理失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupDuplicateChats();
