import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  const { id: chatId } = req.query;
  const currentUserId = await getCurrentUserId(req, res);

  // 验证用户ID
  if (!currentUserId) {
    return res.status(401).json({ message: '用户未认证' });
  }

  // 验证聊天ID
  if (!chatId || Array.isArray(chatId)) {
    return res.status(400).json({ message: '无效的聊天ID' });
  }

  // 检查用户是否有权限访问此聊天
  const chat = await prisma.chat.findUnique({
    where: { id: chatId },
    include: {
      participants: true,
    },
  });

  if (!chat) {
    return res.status(404).json({ message: '聊天不存在' });
  }

  const isParticipant = chat.participants.some(p => p.id === currentUserId);
  if (!isParticipant) {
    return res.status(403).json({ message: '您没有权限访问此聊天' });
  }

  // 处理GET请求 - 获取聊天消息
  if (req.method === 'GET') {
    try {
      const { page = '1', limit = '50' } = req.query;
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      const messages = await prisma.chatMessage.findMany({
        where: {
          chatId,
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limitNum,
      });

      // 反转消息顺序，使最新消息在底部
      const reversedMessages = messages.reverse();

      return res.status(200).json({
        messages: reversedMessages,
        pagination: {
          page: pageNum,
          limit: limitNum,
          hasMore: messages.length === limitNum,
        },
      });
    } catch (error) {
      console.error('获取聊天消息失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理POST请求 - 发送消息
  if (req.method === 'POST') {
    try {
      const { content, type = 'TEXT', fileName, fileUrl, fileSize } = req.body;

      // 验证消息内容
      if (type === 'FILE') {
        // 文件消息验证
        if (!fileName || !fileUrl) {
          return res.status(400).json({ message: '文件消息缺少必要信息' });
        }
      } else {
        // 文本消息验证
        if (!content || content.trim().length === 0) {
          return res.status(400).json({ message: '消息内容不能为空' });
        }

        if (content.length > 2000) {
          return res.status(400).json({ message: '消息内容不能超过2000个字符' });
        }
      }

      // 创建消息
      const message = await prisma.chatMessage.create({
        data: {
          content: type === 'FILE' ? fileName : content.trim(),
          type,
          ...(type === 'FILE' && {
            fileName,
            fileUrl,
            fileSize,
          }),
          chatId,
          senderId: currentUserId,
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
      });

      // 更新聊天的最后更新时间
      await prisma.chat.update({
        where: { id: chatId },
        data: { updatedAt: new Date() },
      });

      return res.status(201).json(message);
    } catch (error) {
      console.error('发送消息失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
