import type { NextApiRequest, NextApiResponse } from 'next';
import { ActivityMonitorService } from '@/lib/activityMonitor';
import { withPermissions } from '@/lib/apiMiddleware';
import { Permission } from '@/lib/permissions';
import { sendSuccess, sendError, handleApiError } from '@/lib/apiMiddleware';
import prisma from '@/lib/prisma';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
  }

  try {
    // 获取系统健康状态
    const systemHealth = await ActivityMonitorService.getSystemHealth();

    // 获取数据库统计信息
    const databaseStats = await getDatabaseStats();

    // 获取系统统计信息
    const systemStats = await getSystemStats();

    // 获取最近的系统活动
    const recentActivities = await getRecentSystemActivities();

    const healthData = {
      ...systemHealth,
      database: databaseStats,
      system: systemStats,
      recentActivities,
      timestamp: new Date(),
    };

    return sendSuccess(res, healthData, '系统健康状态获取成功');
  } catch (error) {
    console.error('获取系统健康状态失败:', error);
    return handleApiError(error, req, res);
  }
}

// 获取数据库统计信息
async function getDatabaseStats() {
  try {
    const [
      userCount,
      projectCount,
      taskCount,
      fileCount,
      messageCount,
      notificationCount
    ] = await Promise.all([
      prisma.user.count(),
      prisma.project.count(),
      prisma.task.count(),
      prisma.file.count(),
      prisma.chatMessage.count(),
      prisma.notification.count(),
    ]);

    // 获取数据库大小（简化实现）
    const databaseSize = await getDatabaseSize();

    return {
      tables: {
        users: userCount,
        projects: projectCount,
        tasks: taskCount,
        files: fileCount,
        messages: messageCount,
        notifications: notificationCount,
      },
      totalRecords: userCount + projectCount + taskCount + fileCount + messageCount + notificationCount,
      estimatedSize: databaseSize,
      status: 'connected',
    };
  } catch (error) {
    console.error('获取数据库统计失败:', error);
    return {
      status: 'error',
      error: error instanceof Error ? error.message : '数据库连接失败',
    };
  }
}

// 获取系统统计信息
async function getSystemStats() {
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  return {
    uptime: process.uptime(),
    memory: {
      used: memoryUsage.heapUsed,
      total: memoryUsage.heapTotal,
      external: memoryUsage.external,
      rss: memoryUsage.rss,
      usage: ((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100).toFixed(2) + '%',
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system,
    },
    node: {
      version: process.version,
      platform: process.platform,
      arch: process.arch,
    },
    environment: process.env.NODE_ENV || 'development',
  };
}

// 获取最近的系统活动
async function getRecentSystemActivities() {
  try {
    // 获取最近的用户登录
    const recentLogins = await prisma.user.findMany({
      where: {
        lastLoginAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // 最近24小时
        },
      },
      select: {
        id: true,
        name: true,
        lastLoginAt: true,
      },
      orderBy: {
        lastLoginAt: 'desc',
      },
      take: 10,
    });

    // 获取最近创建的项目
    const recentProjects = await prisma.project.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 最近7天
        },
      },
      select: {
        id: true,
        title: true,
        createdAt: true,
        owner: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 5,
    });

    // 获取最近完成的任务
    const recentCompletedTasks = await prisma.task.findMany({
      where: {
        status: 'COMPLETED',
        updatedAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 最近7天
        },
      },
      select: {
        id: true,
        title: true,
        updatedAt: true,
        assignee: {
          select: {
            name: true,
          },
        },
        project: {
          select: {
            title: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
      take: 5,
    });

    return {
      recentLogins: recentLogins.map(user => ({
        userId: user.id,
        userName: user.name,
        loginTime: user.lastLoginAt,
      })),
      recentProjects: recentProjects.map(project => ({
        projectId: project.id,
        title: project.title,
        createdAt: project.createdAt,
        ownerName: project.owner.name,
      })),
      recentCompletedTasks: recentCompletedTasks.map(task => ({
        taskId: task.id,
        title: task.title,
        completedAt: task.updatedAt,
        assigneeName: task.assignee?.name,
        projectTitle: task.project.title,
      })),
    };
  } catch (error) {
    console.error('获取最近系统活动失败:', error);
    return {
      recentLogins: [],
      recentProjects: [],
      recentCompletedTasks: [],
      error: '获取活动数据失败',
    };
  }
}

// 获取数据库大小（简化实现）
async function getDatabaseSize(): Promise<string> {
  try {
    // 对于SQLite，可以通过文件大小估算
    // 对于其他数据库，需要使用相应的查询
    return '估算中...';
  } catch (error) {
    return '未知';
  }
}

// 导出带权限检查的处理器（只有管理员可以访问）
export default withPermissions(Permission.SYSTEM_ADMIN, handler);
