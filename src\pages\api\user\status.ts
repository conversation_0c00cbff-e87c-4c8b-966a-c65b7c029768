import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';
import { sendSuccess, sendError, handleApiError } from '@/lib/apiMiddleware';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
  }

  try {
    // 检查用户是否已认证
    if (!(await isAuthenticated(req, res))) {
      return sendError(res, '请先登录', 'UNAUTHORIZED', 401);
    }

    const userId = await getCurrentUserId(req, res);
    if (!userId) {
      return sendError(res, '用户信息无效', 'INVALID_USER', 401);
    }

    // 获取用户状态信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            ownedProjects: true,
            memberProjects: true,
            assignedTasks: true
          }
        }
      }
    });

    if (!user) {
      return sendError(res, '用户不存在', 'USER_NOT_FOUND', 404);
    }

    // 根据用户状态返回相应信息
    const statusInfo = {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        projectCount: user._count.ownedProjects + user._count.memberProjects,
        taskCount: user._count.assignedTasks
      },
      permissions: {
        canAccessDashboard: user.status === 'APPROVED',
        canCreateProjects: user.status === 'APPROVED' && (user.role === 'ADMIN' || user.role === 'LEADER'),
        canManageUsers: user.role === 'ADMIN',
        isNewUser: (user._count.ownedProjects + user._count.memberProjects) === 0
      },
      statusMessage: getStatusMessage(user.status)
    };

    return sendSuccess(res, statusInfo, '用户状态获取成功');
  } catch (error) {
    console.error('获取用户状态失败:', error);
    return handleApiError(error, req, res);
  }
}

function getStatusMessage(status: string): string {
  switch (status) {
    case 'PENDING':
      return '您的账号正在审核中，请耐心等待管理员审核。';
    case 'APPROVED':
      return '您的账号已通过审核，可以正常使用所有功能。';
    case 'REJECTED':
      return '很抱歉，您的账号申请被拒绝。如有疑问，请联系管理员。';
    default:
      return '账号状态未知，请联系管理员。';
  }
}
