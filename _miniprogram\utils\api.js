// API接口定义
const { get, post, put, del, uploadFile } = require('./request.js')

// 用户相关API
const userApi = {
  // 登录
  login: (data) => post('/api/auth/signin', data, { needAuth: false }),

  // 注册
  register: (data) => post('/api/auth/register', data, { needAuth: false }),

  // 获取用户信息
  getUserInfo: (id) => get(`/api/users/${id}`),

  // 更新用户信息
  updateUserInfo: (id, data) => put(`/api/users/${id}`, data),

  // 获取用户列表
  getUserList: () => get('/api/users'),

  // 上传头像
  uploadAvatar: (filePath) => uploadFile({
    url: '/api/upload/avatar',
    filePath,
    name: 'avatar'
  }),

  // 获取用户状态
  getUserStatus: () => get('/api/user/status')
}

// 项目相关API
const projectApi = {
  // 获取项目列表
  getProjectList: (params = {}) => {
    const query = new URLSearchParams(params).toString();
    return get(`/api/projects${query ? '?' + query : ''}`);
  },

  // 获取项目详情
  getProjectDetail: (id) => get(`/api/projects/${id}`),

  // 创建项目
  createProject: (data) => post('/api/projects', data),

  // 更新项目
  updateProject: (id, data) => put(`/api/projects/${id}`, data),

  // 删除项目
  deleteProject: (id) => del(`/api/projects/${id}`),

  // 获取项目成员
  getProjectMembers: (id) => get(`/api/projects/${id}/members`),

  // 添加项目成员
  addProjectMember: (id, data) => post(`/api/projects/${id}/members`, data),

  // 移除项目成员
  removeProjectMember: (id, memberId) => del(`/api/projects/${id}/members/${memberId}`),

  // 获取项目消息
  getProjectMessages: (id, page = 1) => get(`/api/projects/${id}/messages?page=${page}`)
}

// 任务相关API
const taskApi = {
  // 获取任务列表
  getTaskList: (params = {}) => {
    const query = new URLSearchParams(params).toString();
    return get(`/api/tasks${query ? '?' + query : ''}`);
  },

  // 获取任务详情
  getTaskDetail: (id) => get(`/api/tasks/${id}`),

  // 创建任务
  createTask: (data) => post('/api/tasks', data),

  // 更新任务
  updateTask: (id, data) => put(`/api/tasks/${id}`, data),

  // 删除任务
  deleteTask: (id) => del(`/api/tasks/${id}`),

  // 更新任务状态
  updateTaskStatus: (id, status) => put(`/api/tasks/${id}`, { status }),

  // 分配任务
  assignTask: (id, assigneeIds) => put(`/api/tasks/${id}`, { assigneeIds })
}

// 聊天相关API
const chatApi = {
  // 获取聊天列表
  getChatList: () => get('/api/chats'),

  // 获取聊天详情
  getChatDetail: (id) => get(`/api/chats/${id}`),

  // 创建聊天
  createChat: (data) => post('/api/chats', data),

  // 发送消息
  sendMessage: (chatId, data) => post(`/api/chats/${chatId}/messages`, data),

  // 获取消息列表
  getMessages: (chatId, page = 1) => get(`/api/chats/${chatId}/messages?page=${page}`),

  // 标记消息已读
  markAsRead: (chatId) => post(`/api/chats/${chatId}/read`),

  // 获取未读消息数量
  getUnreadCount: () => get('/api/chats/unread-count'),

  // 上传聊天文件
  uploadChatFile: (filePath) => uploadFile({
    url: '/api/upload/chat-file',
    filePath,
    name: 'file'
  })
}

// 文件相关API
const fileApi = {
  // 获取文件列表
  getFileList: (params = {}) => {
    const query = new URLSearchParams(params).toString();
    return get(`/api/files${query ? '?' + query : ''}`);
  },

  // 获取文件详情
  getFileDetail: (id) => get(`/api/files/${id}`),

  // 上传文件
  uploadFile: (filePath, projectId) => uploadFile({
    url: '/api/upload/file',
    filePath,
    name: 'file',
    formData: { projectId }
  }),

  // 删除文件
  deleteFile: (id) => del(`/api/files/${id}`),

  // 下载文件
  downloadFile: (id) => get(`/api/files/${id}/download`),

  // 预览文件
  previewFile: (id) => get(`/api/files/${id}/view`)
}

// 通知相关API
const notificationApi = {
  // 获取通知列表
  getNotificationList: (params = {}) => {
    const query = new URLSearchParams(params).toString();
    return get(`/api/notifications${query ? '?' + query : ''}`);
  },

  // 获取通知详情
  getNotificationDetail: (id) => get(`/api/notifications/${id}`),

  // 标记通知已读
  markNotificationRead: (id) => put(`/api/notifications/${id}`, { isRead: true }),

  // 标记所有通知已读
  markAllNotificationsRead: () => post('/api/notifications/mark-all-read'),

  // 获取未读通知数量
  getUnreadNotificationCount: () => get('/api/notifications/counts'),

  // 删除通知
  deleteNotification: (id) => del(`/api/notifications/${id}`)
}

// 仪表盘相关API
const dashboardApi = {
  // 获取仪表盘统计数据（包含最近活动）
  getStats: () => get('/api/dashboard/stats'),

  // 获取最近活动（从统计数据中获取）
  getRecentActivities: () => get('/api/dashboard/stats').then(res => ({
    ...res,
    data: res.data?.recentActivities || []
  }))
}

// 团队相关API
const teamApi = {
  // 获取团队成员
  getTeamMembers: () => get('/api/team/members'),

  // 邀请成员
  inviteMember: (data) => post('/api/team/invite', data),

  // 获取邀请码
  getInviteCodes: () => get('/api/invite-codes'),

  // 创建邀请码
  createInviteCode: (data) => post('/api/invite-codes', data),

  // 验证邀请码
  verifyInviteCode: (code) => post('/api/invite-codes/verify', { code }),

  // 获取团队统计
  getTeamStats: () => get('/api/team/stats'),

  // 发布团队公告
  publishAnnouncement: (data) => post('/api/team/announcement', data),

  // 导出团队数据
  exportTeamData: () => get('/api/team/export')
}

// 搜索相关API
const searchApi = {
  // 全局搜索
  globalSearch: (query) => get(`/api/search?q=${encodeURIComponent(query)}`),

  // 高级搜索
  advancedSearch: (params) => {
    const query = buildQueryString(params);
    return get(`/api/search/advanced?${query}`);
  }
}

// 系统相关API
const systemApi = {
  // 获取系统配置
  getSystemConfig: () => get('/api/system/config'),

  // 健康检查
  healthCheck: () => get('/api/system/health', { needAuth: false })
}

module.exports = {
  userApi,
  projectApi,
  taskApi,
  chatApi,
  fileApi,
  notificationApi,
  dashboardApi,
  teamApi,
  searchApi,
  systemApi
};
