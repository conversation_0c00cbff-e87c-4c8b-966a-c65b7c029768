import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, isAdmin } from '@/lib/auth';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 检查用户是否已认证
  if (!(await isAuthenticated(req, res))) {
    return res.status(401).json({ message: '未授权' });
  }

  // 处理GET请求 - 获取用户列表
  if (req.method === 'GET') {
    try {
      // 检查是否为管理员（只有管理员可以获取完整的用户列表）
      const isUserAdmin = await isAdmin(req, res);

      let users;

      if (isUserAdmin) {
        // 管理员可以查看所有用户的详细信息
        users = await prisma.user.findMany({
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            age: true,
            department: true,
            position: true,
            role: true,
            status: true, // 管理员也需要看到状态信息
            createdAt: true,
            updatedAt: true,
          },
          orderBy: {
            name: 'asc',
          },
        });
      } else {
        // 普通用户只能查看已审核的基本用户信息（用于选择项目成员等）
        users = await prisma.user.findMany({
          where: {
            status: 'APPROVED', // 只返回已审核的用户
          },
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            age: true,
            department: true,
            position: true,
            role: true,
            status: true,
          },
          orderBy: {
            name: 'asc',
          },
        });
      }

      return res.status(200).json(users);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 处理POST请求 - 创建新用户（仅限管理员）
  if (req.method === 'POST') {
    // 检查是否为管理员
    const isUserAdmin = await isAdmin(req, res);
    if (!isUserAdmin) {
      return res.status(403).json({ message: '权限不足，只有管理员可以创建用户' });
    }

    const { name, email, password, role } = req.body;

    // 验证请求数据
    if (!name || !email || !password) {
      return res.status(400).json({ message: '请提供所有必填字段' });
    }

    try {
      // 检查邮箱是否已被注册
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return res.status(400).json({ message: '该邮箱已被注册' });
      }

      // 创建新用户
      const user = await prisma.user.create({
        data: {
          name,
          email,
          password, // 注意：实际应用中应该对密码进行哈希处理
          role: role || 'MEMBER',
          status: 'APPROVED', // 管理员创建的用户直接设置为已审核
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return res.status(201).json(user);
    } catch (error) {
      console.error('创建用户失败:', error);
      return res.status(500).json({ message: '服务器错误，请稍后再试' });
    }
  }

  // 如果不是支持的HTTP方法
  return res.status(405).json({ message: '方法不允许' });
}
