import { ReactNode, useState } from 'react';
import { useSession } from 'next-auth/react';
import Head from 'next/head';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import GlobalSearch from './GlobalSearch';


interface LayoutProps {
  children: ReactNode;
  title?: string;
}

export default function Layout({ children, title }: LayoutProps) {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';
  const [isSearchOpen, setIsSearchOpen] = useState(false);


  return (
    <>
      <Head>
        <title>{title || 'LabSync - 实验室管理系统'}</title>
        <meta name="description" content="现代化的实验室管理系统，专为科研团队设计" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen flex flex-col gradient-bg">
        <Navbar
          onSearchOpen={() => setIsSearchOpen(true)}
        />

        <div className="flex flex-1">
          {isAuthenticated && (
            <Sidebar />
          )}

          <main className="flex-1 p-4 md:p-6">
            <div className="fade-in">
              {children}
            </div>
          </main>
        </div>

        {/* 全局搜索 */}
        <GlobalSearch
          isOpen={isSearchOpen}
          onClose={() => setIsSearchOpen(false)}
        />


      </div>
    </>
  );
}
