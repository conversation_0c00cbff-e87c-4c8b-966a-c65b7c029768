// 创建测试用户的脚本
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('🔧 创建测试用户...\n');
    
    // 创建几个测试用户
    const testUsers = [
      {
        name: '李研究员',
        email: '<EMAIL>',
        password: 'password123',
        role: 'MEMBER',
        status: 'APPROVED',
      },
      {
        name: '王博士',
        email: '<EMAIL>',
        password: 'password123',
        role: 'MEMBER',
        status: 'APPROVED',
      },
      {
        name: '陈工程师',
        email: '<EMAIL>',
        password: 'password123',
        role: 'MEMBER',
        status: 'APPROVED',
      },
    ];
    
    for (const userData of testUsers) {
      // 检查用户是否已存在
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email },
      });
      
      if (existingUser) {
        console.log(`⚠️  用户 ${userData.name} (${userData.email}) 已存在，跳过创建`);
        continue;
      }
      
      // 创建用户
      const user = await prisma.user.create({
        data: userData,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          status: true,
        },
      });
      
      console.log(`✅ 创建用户成功: ${user.name} (${user.email}) - ${user.role} - ${user.status}`);
    }
    
    console.log('\n🎉 测试用户创建完成！');
    
    // 显示最新的用户统计
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
    
    console.log(`\n📊 当前系统用户总数: ${allUsers.length}`);
    console.log('用户列表:');
    allUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.name} (${user.email}) - ${user.role} - ${user.status}`);
    });
    
    // 检查已审核用户
    const approvedUsers = allUsers.filter(user => user.status === 'APPROVED');
    console.log(`\n✅ 已审核用户数: ${approvedUsers.length}`);
    
  } catch (error) {
    console.error('❌ 创建用户过程中出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行创建脚本
createTestUser();
