// 测试头像更新功能的脚本
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function testAvatarUpdate() {
  console.log('🧪 测试头像更新功能...\n');

  try {
    // 1. 检查头像目录
    console.log('📁 检查头像存储...');
    const avatarDir = './public/avatars';
    
    if (!fs.existsSync(avatarDir)) {
      fs.mkdirSync(avatarDir, { recursive: true });
      console.log('✅ 头像目录已创建');
    } else {
      console.log('✅ 头像目录存在');
    }

    // 检查目录权限
    try {
      const testFile = path.join(avatarDir, 'test.txt');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      console.log('✅ 头像目录可写');
    } catch (error) {
      console.error('❌ 头像目录权限问题:', error.message);
    }

    // 列出现有头像文件
    const avatarFiles = fs.readdirSync(avatarDir);
    console.log(`📊 现有头像文件: ${avatarFiles.length} 个`);
    
    if (avatarFiles.length > 0) {
      console.log('📋 头像文件列表:');
      avatarFiles.forEach(file => {
        const filePath = path.join(avatarDir, file);
        const stats = fs.statSync(filePath);
        const sizeKB = Math.round(stats.size / 1024);
        const modifiedTime = stats.mtime.toLocaleString();
        console.log(`  - ${file} (${sizeKB}KB, ${modifiedTime})`);
      });
    }

    // 2. 检查用户头像设置
    console.log('\n👥 检查用户头像设置...');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    console.log(`✅ 找到 ${users.length} 个用户`);
    
    const usersWithAvatar = users.filter(user => user.avatar);
    const usersWithoutAvatar = users.filter(user => !user.avatar);
    
    console.log(`  - 已设置头像: ${usersWithAvatar.length} 人`);
    console.log(`  - 未设置头像: ${usersWithoutAvatar.length} 人`);

    if (usersWithAvatar.length > 0) {
      console.log('\n📸 已设置头像的用户:');
      usersWithAvatar.forEach(user => {
        console.log(`  - ${user.name}: ${user.avatar}`);
      });
    }

    if (usersWithoutAvatar.length > 0) {
      console.log('\n👤 未设置头像的用户:');
      usersWithoutAvatar.forEach(user => {
        console.log(`  - ${user.name} (${user.email})`);
      });
    }

    // 3. 验证头像文件完整性
    console.log('\n🔍 验证头像文件完整性...');
    let validAvatars = 0;
    let invalidAvatars = 0;

    for (const user of usersWithAvatar) {
      if (user.avatar) {
        // 检查是否是相对路径
        if (user.avatar.startsWith('/avatars/')) {
          const fileName = user.avatar.replace('/avatars/', '');
          const filePath = path.join(avatarDir, fileName);
          
          if (fs.existsSync(filePath)) {
            validAvatars++;
            console.log(`  ✅ ${user.name}: 头像文件存在`);
          } else {
            invalidAvatars++;
            console.log(`  ❌ ${user.name}: 头像文件不存在 (${user.avatar})`);
          }
        } else {
          // 外部URL或其他格式
          console.log(`  ℹ️  ${user.name}: 外部头像 (${user.avatar})`);
        }
      }
    }

    console.log(`\n📊 头像文件验证结果:`);
    console.log(`  - 有效头像: ${validAvatars} 个`);
    console.log(`  - 无效头像: ${invalidAvatars} 个`);

    // 4. 检查孤立的头像文件
    console.log('\n🗑️  检查孤立的头像文件...');
    const userAvatarFiles = usersWithAvatar
      .map(user => user.avatar)
      .filter(avatar => avatar && avatar.startsWith('/avatars/'))
      .map(avatar => avatar.replace('/avatars/', ''));

    const orphanedFiles = avatarFiles.filter(file => !userAvatarFiles.includes(file));
    
    if (orphanedFiles.length > 0) {
      console.log(`❌ 发现 ${orphanedFiles.length} 个孤立文件:`);
      orphanedFiles.forEach(file => {
        const filePath = path.join(avatarDir, file);
        const stats = fs.statSync(filePath);
        const sizeKB = Math.round(stats.size / 1024);
        console.log(`  - ${file} (${sizeKB}KB)`);
      });
      
      console.log('\n💡 建议: 可以删除这些孤立的头像文件以节省空间');
    } else {
      console.log('✅ 没有发现孤立的头像文件');
    }

    console.log('\n🎉 头像更新功能测试完成！');
    console.log('\n🔧 修复内容:');
    console.log('  • 使用NextAuth session.update()替代页面刷新');
    console.log('  • 添加了session更新的trigger处理');
    console.log('  • 改进了上传成功的用户反馈');
    console.log('  • 添加了详细的错误处理和日志');

    console.log('\n💡 使用说明:');
    console.log('  1. 访问 /profile/edit 页面');
    console.log('  2. 点击头像区域选择图片文件');
    console.log('  3. 支持 JPEG、PNG、GIF、WebP 格式');
    console.log('  4. 文件大小限制 5MB');
    console.log('  5. 上传成功后会自动更新导航栏头像');

    console.log('\n🚀 优化建议:');
    console.log('  • 定期清理孤立的头像文件');
    console.log('  • 考虑添加头像压缩功能');
    console.log('  • 可以添加头像裁剪功能');
    console.log('  • 考虑使用CDN存储头像文件');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAvatarUpdate();
