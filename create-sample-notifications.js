// 创建示例系统通知的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createSampleNotifications() {
  try {
    console.log('开始创建示例系统通知...');

    // 获取第一个用户
    const user = await prisma.user.findFirst();
    if (!user) {
      console.log('没有找到用户');
      return;
    }

    console.log(`为用户 ${user.name} 创建多种类型的系统通知...`);

    // 查找或创建系统聊天
    let systemChat = await prisma.chat.findFirst({
      where: {
        type: 'SYSTEM',
        participants: {
          some: {
            id: user.id,
          },
        },
      },
    });

    if (!systemChat) {
      console.log('创建系统聊天...');
      systemChat = await prisma.chat.create({
        data: {
          type: 'SYSTEM',
          name: '系统通知',
          participants: {
            connect: { id: user.id },
          },
        },
      });
    }

    console.log(`系统聊天ID: ${systemChat.id}`);

    // 创建不同类型的系统通知
    const notifications = [
      {
        content: '📋 您被分配了新任务：「开发用户认证模块」\n项目：LabSync协作平台\n分配人：项目经理',
        delay: 0,
      },
      {
        content: '✅ 任务已完成：「数据库设计」\n项目：LabSync协作平台\n完成人：李开发',
        delay: 2000,
      },
      {
        content: '👥 您被添加到项目：「移动端应用开发」\n添加人：张教授',
        delay: 4000,
      },
      {
        content: '📊 项目状态已更新：「LabSync协作平台」\n新状态：进行中\n更新人：项目经理',
        delay: 6000,
      },
      {
        content: '📎 新文件上传：「API设计文档.pdf」\n项目：LabSync协作平台\n上传人：架构师',
        delay: 8000,
      },
      {
        content: '⏰ 项目截止日期提醒：「LabSync协作平台」\n剩余时间：7 天',
        delay: 10000,
      },
      {
        content: '⏰ 任务截止日期提醒：「开发用户认证模块」\n项目：LabSync协作平台\n剩余时间：3 天',
        delay: 12000,
      },
    ];

    // 依次创建通知（带延迟以模拟真实场景）
    for (let i = 0; i < notifications.length; i++) {
      const notification = notifications[i];
      
      if (notification.delay > 0) {
        console.log(`等待 ${notification.delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, notification.delay));
      }

      const systemMessage = await prisma.chatMessage.create({
        data: {
          content: notification.content,
          type: 'SYSTEM',
          isSystem: true,
          chatId: systemChat.id,
          // 系统消息不需要发送者
        },
      });

      console.log(`创建系统通知 ${i + 1}/${notifications.length}: ${systemMessage.id}`);

      // 更新聊天的最后更新时间
      await prisma.chat.update({
        where: { id: systemChat.id },
        data: { updatedAt: new Date() },
      });
    }

    console.log('所有示例系统通知创建完成！');

    // 查看最终结果
    const chatWithMessages = await prisma.chat.findUnique({
      where: { id: systemChat.id },
      include: {
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        participants: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    console.log('\n系统聊天最终状态:');
    console.log(`聊天ID: ${chatWithMessages.id}`);
    console.log(`聊天类型: ${chatWithMessages.type}`);
    console.log(`参与者: ${chatWithMessages.participants.map(p => p.name).join(', ')}`);
    console.log(`消息总数: ${chatWithMessages.messages.length}`);
    console.log('\n最新的5条消息:');
    chatWithMessages.messages.slice(0, 5).forEach((msg, index) => {
      console.log(`${index + 1}. [${msg.createdAt.toLocaleString()}] ${msg.content.split('\n')[0]}`);
    });

  } catch (error) {
    console.error('创建示例通知失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSampleNotifications();
