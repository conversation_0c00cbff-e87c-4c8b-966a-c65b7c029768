import React, { useState } from 'react';
import { XMarkIcon, ArrowDownTrayIcon, EyeIcon } from '@heroicons/react/24/outline';

interface ChatFilePreviewProps {
  file: {
    id: string;
    name: string;
    type: string;
    size: number;
    url: string;
  };
  isOpen: boolean;
  onClose: () => void;
}

export default function ChatFilePreview({ file, isOpen, onClose }: ChatFilePreviewProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen) return null;

  const isImage = file.type.startsWith('image/');
  const isPDF = file.type === 'application/pdf';
  const isText = file.type.startsWith('text/') || 
                 file.type === 'application/json' ||
                 file.name.endsWith('.md') ||
                 file.name.endsWith('.txt');

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDownload = () => {
    const a = document.createElement('a');
    a.href = file.url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const renderPreview = () => {
    if (isImage) {
      return (
        <div className="flex items-center justify-center h-full relative">
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary-500 border-t-transparent"></div>
            </div>
          )}
          {error ? (
            <div className="text-center">
              <div className="text-6xl mb-4">🖼️</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                图片加载失败
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {file.name}
              </p>
              <button
                onClick={handleDownload}
                className="btn btn-primary"
              >
                下载文件
              </button>
            </div>
          ) : (
            <img
              src={file.url}
              alt={file.name}
              className="max-w-full max-h-full object-contain rounded-lg"
              onLoad={() => setLoading(false)}
              onError={() => {
                setLoading(false);
                setError('图片加载失败');
              }}
            />
          )}
        </div>
      );
    }

    if (isPDF) {
      return (
        <div className="h-full relative">
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-10">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary-500 border-t-transparent mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">正在加载PDF...</p>
              </div>
            </div>
          )}
          <iframe
            src={file.url}
            className="w-full h-full border-0 rounded-lg"
            title={file.name}
            onLoad={() => setLoading(false)}
            onError={() => {
              setLoading(false);
              setError('PDF加载失败');
            }}
          />
        </div>
      );
    }

    if (isText) {
      return (
        <div className="h-full relative">
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-10">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary-500 border-t-transparent mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">正在加载文本...</p>
              </div>
            </div>
          )}
          <iframe
            src={file.url}
            className="w-full h-full border-0 rounded-lg bg-white dark:bg-gray-800"
            title={file.name}
            onLoad={() => setLoading(false)}
            onError={() => {
              setLoading(false);
              setError('文本加载失败');
            }}
          />
        </div>
      );
    }

    // 不支持预览的文件类型
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-6xl mb-4">📄</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            无法预览此文件
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {file.name}
          </p>
          <button
            onClick={handleDownload}
            className="btn btn-primary"
          >
            下载文件
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-75" onClick={onClose} />
      
      <div className="relative h-full flex flex-col">
        {/* 头部工具栏 */}
        <div className="bg-white/10 backdrop-blur-md p-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <EyeIcon className="h-5 w-5 text-white" />
            <div>
              <h2 className="text-white font-medium truncate max-w-md">
                {file.name}
              </h2>
              <p className="text-gray-300 text-sm">
                {formatFileSize(file.size)} • {file.type}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleDownload}
              className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
              title="下载文件"
            >
              <ArrowDownTrayIcon className="h-5 w-5" />
            </button>
            
            <button
              onClick={onClose}
              className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
              title="关闭预览"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* 预览内容 */}
        <div className="flex-1 p-4">
          <div className="h-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
            {renderPreview()}
          </div>
        </div>
      </div>
    </div>
  );
}
