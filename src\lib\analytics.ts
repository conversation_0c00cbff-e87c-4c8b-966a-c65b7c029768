import prisma from './prisma';
import { startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, subWeeks, subMonths } from 'date-fns';

// 分析时间范围
export enum AnalyticsTimeRange {
  LAST_7_DAYS = 'last_7_days',
  LAST_30_DAYS = 'last_30_days',
  LAST_3_MONTHS = 'last_3_months',
  LAST_6_MONTHS = 'last_6_months',
  LAST_YEAR = 'last_year',
  THIS_WEEK = 'this_week',
  THIS_MONTH = 'this_month',
  CUSTOM = 'custom',
}

// 分析数据接口
export interface AnalyticsData {
  timeRange: AnalyticsTimeRange;
  startDate: Date;
  endDate: Date;
  projectStats: ProjectAnalytics;
  taskStats: TaskAnalytics;
  userStats: UserAnalytics;
  performanceStats: PerformanceAnalytics;
  trends: TrendAnalytics;
}

export interface ProjectAnalytics {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  archivedProjects: number;
  averageProjectDuration: number; // 天数
  projectsByStatus: Array<{ status: string; count: number }>;
  projectCompletionRate: number;
  topPerformingProjects: Array<{
    id: string;
    title: string;
    progress: number;
    completionRate: number;
  }>;
}

export interface TaskAnalytics {
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  tasksByStatus: Array<{ status: string; count: number }>;
  tasksByPriority: Array<{ priority: string; count: number }>;
  averageTaskCompletionTime: number; // 小时
  taskCompletionRate: number;
  mostProductiveUsers: Array<{
    userId: string;
    userName: string;
    completedTasks: number;
    averageCompletionTime: number;
  }>;
}

export interface UserAnalytics {
  totalUsers: number;
  activeUsers: number; // 最近30天有活动的用户
  usersByRole: Array<{ role: string; count: number }>;
  userEngagement: Array<{
    userId: string;
    userName: string;
    loginCount: number;
    lastLoginAt: Date | null;
    projectsCount: number;
    tasksCount: number;
    messagesCount: number;
  }>;
}

export interface PerformanceAnalytics {
  averageResponseTime: number; // API响应时间（毫秒）
  systemUptime: number; // 系统正常运行时间百分比
  errorRate: number; // 错误率百分比
  databasePerformance: {
    queryCount: number;
    averageQueryTime: number;
    slowQueries: number;
  };
}

export interface TrendAnalytics {
  projectCreationTrend: Array<{ date: string; count: number }>;
  taskCompletionTrend: Array<{ date: string; count: number }>;
  userActivityTrend: Array<{ date: string; activeUsers: number }>;
  fileUploadTrend: Array<{ date: string; count: number; totalSize: number }>;
}

// 分析服务类
export class AnalyticsService {
  // 获取时间范围
  static getDateRange(timeRange: AnalyticsTimeRange, customStart?: Date, customEnd?: Date): { startDate: Date; endDate: Date } {
    const now = new Date();
    
    switch (timeRange) {
      case AnalyticsTimeRange.LAST_7_DAYS:
        return { startDate: subDays(now, 7), endDate: now };
      case AnalyticsTimeRange.LAST_30_DAYS:
        return { startDate: subDays(now, 30), endDate: now };
      case AnalyticsTimeRange.LAST_3_MONTHS:
        return { startDate: subMonths(now, 3), endDate: now };
      case AnalyticsTimeRange.LAST_6_MONTHS:
        return { startDate: subMonths(now, 6), endDate: now };
      case AnalyticsTimeRange.LAST_YEAR:
        return { startDate: subMonths(now, 12), endDate: now };
      case AnalyticsTimeRange.THIS_WEEK:
        return { startDate: startOfWeek(now), endDate: endOfWeek(now) };
      case AnalyticsTimeRange.THIS_MONTH:
        return { startDate: startOfMonth(now), endDate: endOfMonth(now) };
      case AnalyticsTimeRange.CUSTOM:
        return { 
          startDate: customStart || subDays(now, 30), 
          endDate: customEnd || now 
        };
      default:
        return { startDate: subDays(now, 30), endDate: now };
    }
  }

  // 获取项目分析数据
  static async getProjectAnalytics(startDate: Date, endDate: Date, userId?: string): Promise<ProjectAnalytics> {
    const whereClause = userId ? {
      OR: [
        { ownerId: userId },
        { members: { some: { id: userId } } }
      ],
      createdAt: { gte: startDate, lte: endDate }
    } : {
      createdAt: { gte: startDate, lte: endDate }
    };

    // 基础统计
    const totalProjects = await prisma.project.count({ where: whereClause });
    const activeProjects = await prisma.project.count({
      where: { ...whereClause, status: 'ACTIVE' }
    });
    const completedProjects = await prisma.project.count({
      where: { ...whereClause, status: 'COMPLETED' }
    });
    const archivedProjects = await prisma.project.count({
      where: { ...whereClause, status: 'ARCHIVED' }
    });

    // 项目状态分布
    const projectsByStatus = await prisma.project.groupBy({
      by: ['status'],
      where: whereClause,
      _count: { id: true }
    });

    // 计算平均项目持续时间
    const projectsWithDuration = await prisma.project.findMany({
      where: {
        ...whereClause,
        status: 'COMPLETED',
        endDate: { not: null }
      },
      select: {
        startDate: true,
        endDate: true
      }
    });

    const averageProjectDuration = projectsWithDuration.length > 0
      ? projectsWithDuration.reduce((sum, project) => {
          const duration = project.endDate!.getTime() - project.startDate.getTime();
          return sum + (duration / (1000 * 60 * 60 * 24)); // 转换为天数
        }, 0) / projectsWithDuration.length
      : 0;

    // 项目完成率
    const projectCompletionRate = totalProjects > 0 ? (completedProjects / totalProjects) * 100 : 0;

    // 表现最好的项目
    const topPerformingProjects = await prisma.project.findMany({
      where: whereClause,
      select: {
        id: true,
        title: true,
        progress: true,
        _count: {
          select: {
            tasks: true
          }
        },
        tasks: {
          select: {
            status: true
          }
        }
      },
      orderBy: {
        progress: 'desc'
      },
      take: 5
    });

    return {
      totalProjects,
      activeProjects,
      completedProjects,
      archivedProjects,
      averageProjectDuration,
      projectsByStatus: projectsByStatus.map(item => ({
        status: item.status,
        count: item._count.id
      })),
      projectCompletionRate,
      topPerformingProjects: topPerformingProjects.map(project => {
        const totalTasks = project._count.tasks;
        const completedTasks = project.tasks.filter(task => task.status === 'COMPLETED').length;
        const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
        
        return {
          id: project.id,
          title: project.title,
          progress: project.progress * 100,
          completionRate
        };
      })
    };
  }

  // 获取任务分析数据
  static async getTaskAnalytics(startDate: Date, endDate: Date, userId?: string): Promise<TaskAnalytics> {
    const whereClause = userId ? {
      OR: [
        { assigneeId: userId },
        { assignees: { some: { id: userId } } },
        { project: { ownerId: userId } }
      ],
      createdAt: { gte: startDate, lte: endDate }
    } : {
      createdAt: { gte: startDate, lte: endDate }
    };

    // 基础统计
    const totalTasks = await prisma.task.count({ where: whereClause });
    const completedTasks = await prisma.task.count({
      where: { ...whereClause, status: 'COMPLETED' }
    });

    // 过期任务
    const overdueTasks = await prisma.task.count({
      where: {
        ...whereClause,
        dueDate: { lt: new Date() },
        status: { not: 'COMPLETED' }
      }
    });

    // 任务状态分布
    const tasksByStatus = await prisma.task.groupBy({
      by: ['status'],
      where: whereClause,
      _count: { id: true }
    });

    // 任务优先级分布
    const tasksByPriority = await prisma.task.groupBy({
      by: ['priority'],
      where: whereClause,
      _count: { id: true }
    });

    // 计算平均任务完成时间
    const completedTasksWithTime = await prisma.task.findMany({
      where: {
        ...whereClause,
        status: 'COMPLETED'
      },
      select: {
        createdAt: true,
        updatedAt: true
      }
    });

    const averageTaskCompletionTime = completedTasksWithTime.length > 0
      ? completedTasksWithTime.reduce((sum, task) => {
          const duration = task.updatedAt.getTime() - task.createdAt.getTime();
          return sum + (duration / (1000 * 60 * 60)); // 转换为小时
        }, 0) / completedTasksWithTime.length
      : 0;

    // 任务完成率
    const taskCompletionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    // 最高效的用户
    const mostProductiveUsers = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        assignedTasks: {
          where: {
            status: 'COMPLETED',
            createdAt: { gte: startDate, lte: endDate }
          },
          select: {
            createdAt: true,
            updatedAt: true
          }
        }
      },
      orderBy: {
        assignedTasks: {
          _count: 'desc'
        }
      },
      take: 5
    });

    return {
      totalTasks,
      completedTasks,
      overdueTasks,
      tasksByStatus: tasksByStatus.map(item => ({
        status: item.status,
        count: item._count.id
      })),
      tasksByPriority: tasksByPriority.map(item => ({
        priority: item.priority,
        count: item._count.id
      })),
      averageTaskCompletionTime,
      taskCompletionRate,
      mostProductiveUsers: mostProductiveUsers.map(user => {
        const completedTasks = user.assignedTasks.length;
        const averageCompletionTime = completedTasks > 0
          ? user.assignedTasks.reduce((sum, task) => {
              const duration = task.updatedAt.getTime() - task.createdAt.getTime();
              return sum + (duration / (1000 * 60 * 60));
            }, 0) / completedTasks
          : 0;

        return {
          userId: user.id,
          userName: user.name,
          completedTasks,
          averageCompletionTime
        };
      })
    };
  }

  // 获取完整分析数据
  static async getAnalyticsData(
    timeRange: AnalyticsTimeRange,
    userId?: string,
    customStart?: Date,
    customEnd?: Date
  ): Promise<AnalyticsData> {
    const { startDate, endDate } = this.getDateRange(timeRange, customStart, customEnd);

    const [projectStats, taskStats] = await Promise.all([
      this.getProjectAnalytics(startDate, endDate, userId),
      this.getTaskAnalytics(startDate, endDate, userId)
    ]);

    // 简化的用户和性能统计（可以根据需要扩展）
    const userStats: UserAnalytics = {
      totalUsers: 0,
      activeUsers: 0,
      usersByRole: [],
      userEngagement: []
    };

    const performanceStats: PerformanceAnalytics = {
      averageResponseTime: 0,
      systemUptime: 99.9,
      errorRate: 0.1,
      databasePerformance: {
        queryCount: 0,
        averageQueryTime: 0,
        slowQueries: 0
      }
    };

    const trends: TrendAnalytics = {
      projectCreationTrend: [],
      taskCompletionTrend: [],
      userActivityTrend: [],
      fileUploadTrend: []
    };

    return {
      timeRange,
      startDate,
      endDate,
      projectStats,
      taskStats,
      userStats,
      performanceStats,
      trends
    };
  }
}
