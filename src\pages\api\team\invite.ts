import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { PrismaClient } from '@prisma/client';
import nodemailer from 'nodemailer';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.email) {
      return res.status(401).json({ message: '未授权' });
    }

    // 检查用户权限（只有管理员和项目负责人可以邀请）
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!currentUser || (currentUser.role !== 'ADMIN' && currentUser.role !== 'LEADER')) {
      return res.status(403).json({ message: '权限不足' });
    }

    const { email, role } = req.body;

    if (!email || !role) {
      return res.status(400).json({ message: '邮箱和角色不能为空' });
    }

    // 检查邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: '邮箱格式不正确' });
    }

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return res.status(400).json({ message: '该邮箱已注册' });
    }

    // 检查是否已有待处理的邀请
    const existingInvite = await prisma.invitation.findFirst({
      where: {
        email,
        status: 'PENDING',
      },
    });

    if (existingInvite) {
      return res.status(400).json({ message: '该邮箱已有待处理的邀请' });
    }

    // 生成邀请码
    const inviteCode = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    // 创建邀请记录
    const invitation = await prisma.invitation.create({
      data: {
        email,
        role,
        code: inviteCode,
        inviterId: currentUser.id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
      },
    });

    // 发送邀请邮件
    try {
      const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: false,
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });

      const inviteUrl = `${process.env.NEXTAUTH_URL}/register?invite=${inviteCode}`;

      await transporter.sendMail({
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: email,
        subject: '邀请加入 LabSync 团队',
        html: `
          <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #6366f1; margin: 0;">LabSync</h1>
              <p style="color: #666; margin: 5px 0;">实验室协作管理平台</p>
            </div>

            <div style="background: #f8fafc; padding: 30px; border-radius: 10px; margin-bottom: 20px;">
              <h2 style="color: #1f2937; margin-top: 0;">您被邀请加入团队！</h2>
              <p style="color: #4b5563; line-height: 1.6;">
                ${currentUser.name} 邀请您加入 LabSync 团队，担任 <strong>${getRoleDisplayName(role)}</strong> 角色。
              </p>
              <p style="color: #4b5563; line-height: 1.6;">
                LabSync 是一个专业的实验室协作管理平台，帮助团队更好地协作和管理项目。
              </p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${inviteUrl}"
                 style="display: inline-block; background: #6366f1; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                接受邀请并注册
              </a>
            </div>

            <div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <p style="color: #92400e; margin: 0; font-size: 14px;">
                ⚠️ 此邀请链接将在 7 天后过期，请尽快完成注册。
              </p>
            </div>

            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px;">
              <p style="color: #9ca3af; font-size: 12px; text-align: center;">
                如果您无法点击按钮，请复制以下链接到浏览器：<br>
                <a href="${inviteUrl}" style="color: #6366f1;">${inviteUrl}</a>
              </p>
            </div>
          </div>
        `,
      });
    } catch (emailError) {
      console.error('发送邮件失败:', emailError);
      // 删除创建的邀请记录
      await prisma.invitation.delete({
        where: { id: invitation.id },
      });
      return res.status(500).json({ message: '发送邀请邮件失败' });
    }

    res.status(200).json({
      message: '邀请发送成功',
      invitation: {
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        expiresAt: invitation.expiresAt,
      },
    });
  } catch (error) {
    console.error('邀请成员失败:', error);
    res.status(500).json({ message: '服务器错误' });
  } finally {
    await prisma.$disconnect();
  }
}

function getRoleDisplayName(role: string) {
  switch (role) {
    case 'ADMIN': return '管理员';
    case 'LEADER': return '项目负责人';
    case 'MEMBER': return '团队成员';
    default: return '访客';
  }
}
