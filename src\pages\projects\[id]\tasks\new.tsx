import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';

type FormData = {
  title: string;
  description: string;
  dueDate: string;
  status: string;
  priority: string;
  assigneeId: string;
};

export default function NewTask() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { id: projectId } = router.query;

  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [selectedAssignees, setSelectedAssignees] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>();

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取项目详情
  useEffect(() => {
    if (status === 'authenticated' && projectId) {
      fetchProjectDetails();
    }
  }, [status, projectId]);

  const fetchProjectDetails = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/projects/${projectId}`);

      if (!response.ok) {
        throw new Error('获取项目详情失败');
      }

      const data = await response.json();
      setProject(data);

      // 检查当前用户是否为项目所有者
      if (session?.user?.id !== data.owner.id) {
        setError('只有项目负责人可以添加任务');
      }
    } catch (error) {
      console.error('获取项目详情失败:', error);
      setError('获取项目详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: FormData) => {
    setSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          projectId,
          assigneeIds: selectedAssignees,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '创建任务失败');
      }

      const task = await response.json();

      // 创建成功后跳转到项目详情页
      router.push(`/projects/${projectId}?tab=tasks`);
    } catch (error) {
      console.error('创建任务失败:', error);
      setError(error instanceof Error ? error.message : '创建任务失败，请稍后再试');
    } finally {
      setSubmitting(false);
    }
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 加载中
  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 项目不存在
  if (!project) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">项目不存在或您没有权限访问</span>
        </div>
        <div className="mt-4">
          <Link href="/projects" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回项目列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          添加新任务
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          为项目 "{project.title}" 添加新任务
        </p>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <label className="form-label" htmlFor="title">
              任务标题 <span className="text-red-500">*</span>
            </label>
            <input
              id="title"
              type="text"
              className="form-input"
              placeholder="输入任务标题"
              {...register('title', {
                required: '请输入任务标题',
                minLength: {
                  value: 2,
                  message: '任务标题至少需要2个字符',
                },
              })}
            />
            {errors.title && (
              <p className="form-error">{errors.title.message}</p>
            )}
          </div>

          <div className="mb-4">
            <label className="form-label" htmlFor="description">
              任务描述
            </label>
            <textarea
              id="description"
              className="form-input"
              rows={4}
              placeholder="输入任务描述"
              {...register('description')}
            ></textarea>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="form-label" htmlFor="dueDate">
                截止日期
              </label>
              <input
                id="dueDate"
                type="date"
                className="form-input"
                {...register('dueDate')}
              />
            </div>

            <div>
              <label className="form-label" htmlFor="priority">
                优先级 <span className="text-red-500">*</span>
              </label>
              <select
                id="priority"
                className="form-input"
                defaultValue="MEDIUM"
                {...register('priority', {
                  required: '请选择优先级',
                })}
              >
                <option value="LOW">低</option>
                <option value="MEDIUM">中</option>
                <option value="HIGH">高</option>
                <option value="URGENT">紧急</option>
              </select>
              {errors.priority && (
                <p className="form-error">{errors.priority.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="form-label" htmlFor="status">
                状态 <span className="text-red-500">*</span>
              </label>
              <select
                id="status"
                className="form-input"
                defaultValue="TODO"
                {...register('status', {
                  required: '请选择状态',
                })}
              >
                <option value="TODO">待处理</option>
                <option value="IN_PROGRESS">进行中</option>
                <option value="REVIEW">审核中</option>
                <option value="COMPLETED">已完成</option>
              </select>
              {errors.status && (
                <p className="form-error">{errors.status.message}</p>
              )}
            </div>

            <div>
              <label className="form-label" htmlFor="assigneeId">
                主要负责人
              </label>
              <select
                id="assigneeId"
                className="form-input"
                {...register('assigneeId')}
              >
                <option value="">未分配</option>
                <option value={project.owner.id}>{project.owner.name} (项目负责人)</option>
                {project.members.map(member => (
                  <option key={member.id} value={member.id}>
                    {member.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* 协作成员选择 */}
          <div className="mb-4">
            <label className="form-label">
              协作成员 <span className="text-sm text-gray-500">(可选择多人)</span>
            </label>
            <div className="border border-gray-300 dark:border-gray-600 rounded-md p-3 bg-white dark:bg-gray-700">
              <div className="space-y-2">
                {/* 项目负责人 */}
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                    checked={selectedAssignees.includes(project.owner.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedAssignees([...selectedAssignees, project.owner.id]);
                      } else {
                        setSelectedAssignees(selectedAssignees.filter(id => id !== project.owner.id));
                      }
                    }}
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    {project.owner.name} <span className="text-xs text-gray-500">(项目负责人)</span>
                  </span>
                </label>

                {/* 项目成员 */}
                {project.members.map(member => (
                  <label key={member.id} className="flex items-center">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                      checked={selectedAssignees.includes(member.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedAssignees([...selectedAssignees, member.id]);
                        } else {
                          setSelectedAssignees(selectedAssignees.filter(id => id !== member.id));
                        }
                      }}
                    />
                    <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                      {member.name}
                    </span>
                  </label>
                ))}
              </div>

              {selectedAssignees.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    已选择 {selectedAssignees.length} 位协作成员
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <Link
              href={`/projects/${projectId}`}
              className="btn btn-secondary"
            >
              取消
            </Link>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={submitting}
            >
              {submitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  创建中...
                </>
              ) : '创建任务'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
