// 网络状态监控
const { setNetworkStatus, setApiStatus } = require('./store.js');

class NetworkMonitor {
  constructor() {
    this.isMonitoring = false;
    this.apiCheckInterval = null;
    this.lastApiCheck = 0;
    this.apiCheckDelay = 30000; // 30秒检查一次API状态
  }

  // 开始监控
  start() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      console.log('网络状态变化:', res);
      setNetworkStatus(res.isConnected ? 'online' : 'offline');
      
      if (res.isConnected) {
        // 网络恢复时检查API状态
        this.checkApiStatus();
      } else {
        setApiStatus('unavailable');
      }
    });

    // 获取初始网络状态
    wx.getNetworkType({
      success: (res) => {
        const isConnected = res.networkType !== 'none';
        setNetworkStatus(isConnected ? 'online' : 'offline');
        
        if (isConnected) {
          this.checkApiStatus();
        }
      }
    });

    // 定期检查API状态
    this.startApiCheck();
  }

  // 停止监控
  stop() {
    this.isMonitoring = false;
    
    if (this.apiCheckInterval) {
      clearInterval(this.apiCheckInterval);
      this.apiCheckInterval = null;
    }
  }

  // 开始API状态检查
  startApiCheck() {
    if (this.apiCheckInterval) return;
    
    this.apiCheckInterval = setInterval(() => {
      this.checkApiStatus();
    }, this.apiCheckDelay);
  }

  // 检查API状态
  async checkApiStatus() {
    const now = Date.now();
    
    // 避免频繁检查
    if (now - this.lastApiCheck < 5000) {
      return;
    }
    
    this.lastApiCheck = now;
    
    try {
      const app = getApp();
      
      // 尝试调用健康检查API
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: app.globalData.baseUrl + '/api/system/health',
          method: 'GET',
          timeout: 5000,
          success: resolve,
          fail: reject
        });
      });

      if (response.statusCode === 200) {
        setApiStatus('available');
      } else {
        setApiStatus('unavailable');
      }

    } catch (error) {
      setApiStatus('unavailable');
    }
  }

  // 手动触发API检查
  triggerApiCheck() {
    this.lastApiCheck = 0;
    this.checkApiStatus();
  }
}

// 创建全局实例
const networkMonitor = new NetworkMonitor();

// 导出
module.exports = {
  networkMonitor,
  
  // 便捷方法
  startNetworkMonitoring: () => networkMonitor.start(),
  stopNetworkMonitoring: () => networkMonitor.stop(),
  checkApiStatus: () => networkMonitor.triggerApiCheck()
};
